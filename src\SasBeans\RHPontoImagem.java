/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class RHPontoImagem {
    private BigDecimal Matr;
    private String DtCompet;
    private BigDecimal Batida;
    private String URL;

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(BigDecimal Matr) {
        this.Matr = Matr;
    }

    public String getDtCompet() {
        return DtCompet;
    }

    public void setDtCompet(String DtCompet) {
        this.DtCompet = DtCompet;
    }

    public BigDecimal getBatida() {
        return Batida;
    }

    public void setBatida(BigDecimal Batida) {
        this.Batida = Batida;
    }

    public String getURL() {
        return URL;
    }

    public void setURL(String URL) {
        this.URL = URL;
    }
    
}
