//package br.com.sasw.pacotesuteis.azure;
//
//import com.azure.storage.blob.BlobClient;
//import com.azure.storage.blob.BlobContainerClient;
//import com.azure.storage.blob.BlobServiceClient;
//import com.azure.storage.blob.BlobServiceClientBuilder;
//import com.azure.storage.blob.models.BlobStorageException; 
//
//import java.io.ByteArrayInputStream;
//import java.io.IOException;
//import java.net.HttpURLConnection;
//import java.net.URL;
//import java.util.logging.Level;
//import java.util.logging.Logger;
//
//public class AzureBlobStorageServico {
//
//    // Use variáveis de ambiente, Azure Key Vault, ou um arquivo de configuração seguro.
//    private final String connectionString;
//    private final String containerName;
//
//    // Cliente de serviço Blob, para ser inicializado apenas uma vez e reutilizado
//    private BlobServiceClient blobServiceClient;
//
//    /**
//     * Construtor da classe AzureBlobStorageService.
//     * @param connectionString A string de conexão da sua Conta de Armazenamento.
//     * @param containerName O nome do contêiner onde as imagens serão salvas.
//     */
//    public AzureBlobStorageServico(String connectionString, String containerName) {
//        if (connectionString == null || connectionString.trim().isEmpty()) {
//            throw new IllegalArgumentException(
//                    "A string de conexão do Azure não pode ser nula ou vazia.");
//        }
//        if (containerName == null || containerName.trim().isEmpty()) {
//            throw new IllegalArgumentException(
//                    "O nome do contêiner do Azure não pode ser nulo ou vazio.");
//        }
//        this.connectionString = connectionString;
//        this.containerName = containerName;
//        // Inicializa o cliente de serviço no construtor para reutilização
//        this.blobServiceClient = new BlobServiceClientBuilder()
//                                    .connectionString(this.connectionString)
//                                    .buildClient();
//    }
//
//    /**
//     * Obtém o cliente de contêiner para o contêiner especificado.
//     * Garante que o contêiner exista.
//     * @return BlobContainerClient para o contêiner.
//     * @throws IOException Se houver um erro ao criar o contêiner.
//     */
//    private BlobContainerClient getOrCreateContainerClient() throws IOException {
//        BlobContainerClient containerClient = blobServiceClient.
//                getBlobContainerClient(containerName);
//        
//        try {
//            // Verifica se o contêiner existe. Se não, tenta criá-lo.
//            if (!containerClient.exists()) {
//                containerClient.create(); // Cria o contêiner
//                System.out.println("Contêiner '" + containerName + 
//                        "' criado no Azure Blob Storage.");
//            }
//        } catch (BlobStorageException e) {
//            // Trata casos onde o contêiner já existe mas ocorreu um erro 
//            // 409 Conflict: contêiner já existe
//            if (e.getStatusCode() == 409) { 
//                System.out.println("Contêiner '" + containerName + 
//                        "' já existe. Ignorando criação.");
//            } else {
//                throw new IOException("Erro ao verificar ou criar o contêiner '" 
//                        + containerName + "': " + e.getMessage(), e);
//            }
//        } catch (Exception e) {
//            throw new IOException("Erro inesperado ao obter ou criar o contêiner '"
//                    + containerName + "': " + e.getMessage(), e);
//        }
//        return containerClient;
//    }
//
//    /**
//     * Salva uma imagem (array de bytes) no Azure Blob Storage.
//     *
//     * @param imgBytes O array de bytes que representa a imagem.
//     * @param blobName O nome completo do blob, incluindo a estrutura de pastas.
//     * @return A URL pública do blob salvo.
//     * @throws IOException Se ocorrer um erro durante o upload para o Azure Blob Storage.
//     */
//    public String salvarImagem(byte[] imgBytes, String blobName) throws IOException {
//        if (imgBytes == null || imgBytes.length == 0) {
//            throw new IllegalArgumentException("Os bytes da imagem não podem ser nulos ou vazios.");
//        }
//        if (blobName == null || blobName.trim().isEmpty()) {
//            throw new IllegalArgumentException("O nome do blob não pode ser nulo ou vazio.");
//        }
//
//        try {
//            BlobContainerClient containerClient = getOrCreateContainerClient();
//            BlobClient blobClient = containerClient.getBlobClient(blobName);
//
//            // Realiza o upload da imagem a partir do array de bytes
//            blobClient.upload(new ByteArrayInputStream(imgBytes), 
//                    imgBytes.length, true);
//
//            // Retorna a URL pública do blob salvo
//            return blobClient.getBlobUrl();
//        } catch (IOException e) {
//            // Propaga a IOException para o chamador tratar
//            throw e; 
//        } catch (Exception e) {
//            // Captura qualquer outra exceção e a relança como IOException 
//            throw new IOException("Erro ao salvar imagem no Azure Blob Storage: " 
//                    + e.getMessage(), e);
//        }
//    }
//    
///**
//     * Verifica se uma imagem existe acessível publicamente via HTTP/HTTPS HEAD request.
//     * O protocolo (HTTP ou HTTPS) é determinado pela URL fornecida.
//     * Retorna true se o status HTTP for 200 (OK) ou 204 (No Content), 
//     * indicando que o recurso existe.
//     *
//     * @param imagemURL A URL pública da imagem.
//     * @return true se a imagem existe e é acessível publicamente, false caso 
//     * contrário.
//     */
//    public static boolean verificaExisteImagem(String imagemURL)  {
//        HttpURLConnection connection = null;
//        try {
//            URL url = new URL(imagemURL);
//            connection = (HttpURLConnection) url.openConnection();
//            // Usa o método HEAD para apenas verificar a existência
//            connection.setRequestMethod("HEAD"); 
//            // 5 segundos de timeout de conexão
//            connection.setConnectTimeout(5000); 
//            // 5 segundos de timeout de leitura
//            connection.setReadTimeout(5000);    
//
//            int statusCode = connection.getResponseCode();
//            System.out.println("   -> Status HTTP recebido: " + statusCode + 
//                    " para " + imagemURL);
//
//            // Um status 200 (OK) ou 204 (No Content) geralmente indica sucesso
//            // 404 (Not Found) indica que não existe
//            // 403 (Forbidden) indica que existe mas não há permissão 
//            return (statusCode == HttpURLConnection.HTTP_OK || statusCode == 
//                    HttpURLConnection.HTTP_NO_CONTENT);
//        } catch (Exception ex) {
//            Logger.getLogger(AzureBlobStorageServico.class.getName()).log(
//                    Level.SEVERE, null, ex);
//            return false;
//        } finally {
//            if (connection != null) {
//                connection.disconnect();
//            }
//        }        
//    }    
//}