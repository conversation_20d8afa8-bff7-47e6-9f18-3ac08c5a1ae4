/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Saspw;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SaspwDao {

    public void inserirSasPW(Saspw saspw, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE SasPW \n"
                    + "SET Nome = ?,\n"
                    + "    Situacao = ?,\n"
                    + "    nivelx = ?,\n"
                    + "    codgrupo = ?,\n"
                    + "    codpessoaweb = ?,\n"
                    + "    Operador = ?,\n"
                    + "    Dt_Alter = ?,\n"
                    + "    Hr_Alter = ?\n"
                    + "WHERE CodPessoa = ?\n"
                    + "\n"
                    + "INSERT INTO SasPW (Nome, Situacao, nivelx, codgrupo, codpessoaweb, operador, Dt_Alter, Hr_Alter, CodPessoa)\n"
                    + "SELECT TOP 1 ?, ?, ?, ?, ?, ?, ?, ?, ?\n"
                    + "FROM (SELECT COUNT(*) AS qtde_cadastrado \n"
                    + "                            FROM SasPW\n"
                    + "                            WHERE CodPessoa = ?) AS A\n"
                    + "WHERE A.qtde_cadastrado = 0\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(saspw.getNome());
            consulta.setString(saspw.getSituacao());
            consulta.setString(saspw.getNivelx());
            consulta.setInt(saspw.getCodGrupo());
            consulta.setBigDecimal(saspw.getCodPessoaWeb());
            consulta.setString(saspw.getOperador());
            consulta.setString(saspw.getDt_Alter());
            consulta.setString(saspw.getHr_Alter());
            consulta.setBigDecimal(saspw.getCodPessoa());

            consulta.setString(saspw.getNome());
            consulta.setString(saspw.getSituacao());
            consulta.setString(saspw.getNivelx());
            consulta.setInt(saspw.getCodGrupo());
            consulta.setBigDecimal(saspw.getCodPessoaWeb());
            consulta.setString(saspw.getOperador());
            consulta.setString(saspw.getDt_Alter());
            consulta.setString(saspw.getHr_Alter());
            consulta.setBigDecimal(saspw.getCodPessoa());

            consulta.setBigDecimal(saspw.getCodPessoa());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("SaspwDao.inserirSasPW - " + e.getMessage() + "\r\n"
                    + "UPDATE SasPW \n"
                    + "SET Nome = " + saspw.getNome() + ",\n"
                    + "    Situacao = " + saspw.getSituacao() + ",\n"
                    + "    nivelx = " + saspw.getNivelx() + ",\n"
                    + "    codgrupo = " + saspw.getCodGrupo() + ",\n"
                    + "    codpessoaweb = " + saspw.getCodPessoaWeb() + ",\n"
                    + "    Operador = " + saspw.getOperador() + ",\n"
                    + "    Dt_Alter = " + saspw.getDt_Alter() + ",\n"
                    + "    Hr_Alter = " + saspw.getHr_Alter() + "\n"
                    + "WHERE CodPessoa = " + saspw.getCodPessoa() + "\n"
                    + "\n"
                    + "INSERT INTO SasPW (Nome, Situacao, nivelx, codgrupo, codpessoaweb, operador, Dt_Alter, Hr_Alter, CodPessoa)\n"
                    + "SELECT TOP 1 " + saspw.getNome() + ", " + saspw.getSituacao() + ", " + saspw.getNivelx() + ", " + saspw.getCodGrupo() + ", "
                    + saspw.getCodPessoaWeb() + ", " + saspw.getOperador() + ", " + saspw.getDt_Alter() + ", " + saspw.getHr_Alter() + ", " + saspw.getCodPessoa() + "\n"
                    + "FROM (SELECT COUNT(*) AS qtde_cadastrado \n"
                    + "                            FROM SasPW\n"
                    + "                            WHERE CodPessoa = " + saspw.getCodPessoa() + ") AS A\n"
                    + "WHERE A.qtde_cadastrado = 0\n");
        }
    }

    public List<Saspw> verificaSasPWAc(Saspw saspw, Persistencia persistencia) throws Exception {
        List<Saspw> lSaspw = new ArrayList<>();
        String sql = "select nome from saspw where nome = ? ";
        try {
            Consulta consult = new Consulta(sql, persistencia);

            consult.setString(saspw.getNome());
            consult.select();
            while (consult.Proximo()) {
                Saspw pl = new Saspw();
                pl.setNome(consult.getString("nome"));
                lSaspw.add(pl);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao pesquisar Saspw - " + e.getMessage());
        }
        return lSaspw;
    }

    public void atualizaSasPw(Saspw saspW, Persistencia persistencia) throws Exception {
        String sql = " update saspw set situacao=?, motivo=?, codfil=?, codigo=?, nomecompleto=? "
                + " descricao=?, codgrupo=?, nivelx=?, nivelop=?, pw=?, codpessoa=? "
                + ", operador=?, dt_alter=?, hr_alter=? "
                + "where nome = ? ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(saspW.getSituacao());
            consulta.setString(saspW.getMotivo());
            consulta.setBigDecimal(saspW.getCodFil());
            consulta.setBigDecimal(saspW.getCodigo());
            consulta.setString(saspW.getNomeCompleto());
            consulta.setString(saspW.getDescricao());
            consulta.setInt(saspW.getCodGrupo());
            if ("Administrador".equals(saspW.getNivelOP())) {
                consulta.setString("9");
            } else if ("Gerência".equals(saspW.getNivelOP())) {
                consulta.setString("3");
            } else if ("Manutenção".equals(saspW.getNivelOP())) {
                consulta.setString("2");
            } else if ("Operação".equals(saspW.getNivelOP())) {
                consulta.setString("1");
            }
            consulta.setString(saspW.getNivelOP());
            consulta.setString(saspW.getPW());
            consulta.setBigDecimal(saspW.getCodigo());
            consulta.setString(saspW.getOperador());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.setString(saspW.getNome());
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new RuntimeException("Falha ao inserir Saspw " + e.getMessage());
        }
    }

    public void insereSasPw(Saspw saspW, Persistencia persistencia) throws Exception {
        String sql = " insert into saspw(nome, situacao, motivo, codfil, codigo, nomecompleto "
                + " descricao, codgrupo, nivelx, nivelop, pw, codpessoa "
                + ", operador, dt_alter, hr_alter) "
                + "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(saspW.getNome());
            consulta.setString(saspW.getSituacao());
            consulta.setString(saspW.getMotivo());
            consulta.setBigDecimal(saspW.getCodFil());
            consulta.setBigDecimal(saspW.getCodigo());
            consulta.setString(saspW.getNomeCompleto());
            consulta.setString(saspW.getDescricao());
            consulta.setInt(saspW.getCodGrupo());
            if ("Administrador".equals(saspW.getNivelOP())) {
                consulta.setString("9");
            } else if ("Gerência".equals(saspW.getNivelOP())) {
                consulta.setString("3");
            } else if ("Manutenção".equals(saspW.getNivelOP())) {
                consulta.setString("2");
            } else if ("Operação".equals(saspW.getNivelOP())) {
                consulta.setString("1");
            }
            consulta.setString(saspW.getNivelOP());
            consulta.setString(saspW.getPW());
            consulta.setBigDecimal(saspW.getCodigo());
            consulta.setString(saspW.getOperador());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));

            consulta.insert();
            consulta.close();
        } catch (SQLException e) {
            throw new RuntimeException("Falha ao inserir Saspw " + e.getMessage());
        }
    }

    public List<Saspw> getSasPW(BigDecimal CodFil, String sCriterio, Persistencia persistencia) throws Exception {
        List<Saspw> lSaspw = new ArrayList();
        String sql = null;
        Consulta consult = null;
        try {
            if ("".equals(sCriterio) || sCriterio == null) {
                sql = "select top 50 filiais.codfil, filiais.descricao, saspw.codigo, saspw.codpessoa, saspw.nome, "
                        + "saspw.nivelop, saspw.nomecompleto, saspw.descricao "
                        + ", saspw.situacao, saspw.motivo, saspw.operador, saspw.dt_alter, saspw.hr_alter "
                        + ",saspw.codgrupo "
                        + "from saspw "
                        + "inner join saspwfil on saspw.nome = saspwfil.nome "
                        + "inner join filiais on saspwfil.CodfilAc = filiais.codfil "
                        + "where filiais.codfil = ? and saspw.situacao = 'A'";

            } else {
                sql = "select filiais.codfil, filiais.descricao, saspw.codigo, saspw.codpessoa, saspw.nome, "
                        + "saspw.nivelop, saspw.nomecompleto, saspw.descricao "
                        + ", saspw.situacao, saspw.motivo, saspw.operador, saspw.dt_alter, saspw.hr_alter "
                        + ",saspw.codgrupo "
                        + "from saspw "
                        + "inner join saspwfil on saspw.nome = saspwfil.nome "
                        + "inner join filiais on saspwfil.CodfilAc = filiais.codfil "
                        + "where filiais.codfil = ? and saspw.situacao = 'A' and " + sCriterio;

            }
            consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.select();
            while (consult.Proximo()) {
                Saspw pl = new Saspw();
                pl.setCodFil(consult.getString("codfil"));
                pl.setCodigo(consult.getString("codigo").replace(".0", ""));
                pl.setCodPessoa(consult.getString("codpessoa").replace(".0", ""));
                pl.setNome(consult.getString("nome"));
                pl.setNivelOP(consult.getString("nivelop"));
                pl.setNomeCompleto(consult.getString("nomecompleto"));
                pl.setDescricao(consult.getString("descricao"));
                pl.setSituacao(consult.getString("situacao"));
                pl.setMotivo(consult.getString("motivo"));
                pl.setOperador(consult.getString("operador"));
                pl.setDt_Alter(consult.getString("dt_alter"));
                pl.setHr_Alter(consult.getString("hr_alter"));
                pl.setCodGrupo(consult.getInt("codgrupo"));
                pl.setNomeCodFil(consult.getString("descricao"));
                lSaspw.add(pl);
            }
            consult.Close();
            return lSaspw;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar SasPW - " + e.getMessage());
        }
    }

    /**
     * Busca usuários do sistema pelo login
     *
     * @param Nome - login do usuário
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Saspw> BuscaLogin(String Nome, Persistencia persistencia) throws Exception {
        try {
            List<Saspw> retorno = new ArrayList();
            String sql = "Select Nome, Situacao, PW, CodPessoa, CodPessoaWEB From Saspw where nome = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Nome);
            consult.select();
            Saspw saspw = new Saspw();
            while (consult.Proximo()) {
                saspw.setNome(consult.getString("Nome"));
                saspw.setSituacao(consult.getString("Situacao"));
                saspw.setPW(consult.getString("PW"));
                saspw.setCodPessoa(consult.getString("CodPessoa"));
                saspw.setCodPessoaWeb(consult.getString("CodPessoaWeb"));
                retorno.add(saspw);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("SaspwDao.BuscaLogin - " + e.getMessage() + "\r\n"
                    + "Select Nome, Situacao, PW, CodPessoa, CodPessoaWEB From Saspw where nome = " + Nome);
        }
    }

    /**
     * Busca PW, CodpessoaWeb, Situacao do usuário passado
     *
     * @param CodPessoa - código de pessoa do usuário
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Saspw BuscaPW(String CodPessoa, Persistencia persistencia) throws Exception {
        try {
            Saspw retorno = new Saspw();
            String sql = "Select PW, CodPessoa, CodPessoaWEB, Situacao From Saspw where codpessoa = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodPessoa);
            consult.select();
            while (consult.Proximo()) {
                retorno.setSituacao(consult.getString("Situacao"));
                retorno.setPW(consult.getString("PW"));
                retorno.setCodPessoa(consult.getString("CodPessoa"));
                retorno.setCodPessoaWeb(consult.getString("CodPessoaWeb"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar dados de usuario \r\n" + e.getMessage());
        }
    }
    
        public Saspw buscarPorCodPessoaWEB(String CodPessoa, Persistencia persistencia) throws Exception {
        try {
            Saspw retorno = new Saspw();
            String sql = "Select PW, CodPessoa, CodPessoaWEB, Situacao From Saspw where CodPessoaWeb = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodPessoa);
            consult.select();
            while (consult.Proximo()) {
                retorno.setSituacao(consult.getString("Situacao"));
                retorno.setPW(consult.getString("PW"));
                retorno.setCodPessoa(consult.getString("CodPessoa"));
                retorno.setCodPessoaWeb(consult.getString("CodPessoaWeb"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar dados de usuario \r\n" + e.getMessage());
        }
    }

    public Saspw BuscaLogin(BigDecimal codpessoaWeb, Persistencia persistencia) throws Exception {
        Saspw retorno = new Saspw();
        String sql = "SELECT * FROM saspw WHERE codpessoaweb = ?";

        try {

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoaWeb);
            consulta.select();

            while (consulta.Proximo()) {

                retorno.setNome(consulta.getString("Nome"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setPW(consulta.getString("PW"));
                retorno.setCodPessoa(consulta.getString("CodPessoa"));
                retorno.setCodPessoaWeb(consulta.getString("CodPessoaWeb"));
                retorno.setCodFil(consulta.getString("Codfil"));

            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao carregar dados de login \r\n" + e.getMessage());
        }

        return retorno;
    }
}
