<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet"  media="print"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="../assets/scripts/jquery.mask.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                /*#btGeralHome{
                    display: none !important;
                }*/
                
                input:disabled{
                    background-color: #EEE !important;
                }

                div[id*="tabelaCedulaMoeda"]{
                    max-height: 171px !important;
                }

                [id*="formCadastroComposicao"] .ui-inputfield{
                    width:100% !important;
                }

                @media only screen and (max-width: 3500px) and (min-width: 641px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -2px !important;
                        position: relative !important;
                    }

                    .DataGrid{
                        width:100% !important;
                        border: none !important;
                    }

                    [id*="guias"] .DataGrid thead tr th:nth-child(1),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(1){
                        min-width: 140px !important;
                        width: 140px !important;
                        max-width: 140px !important;
                    }

                    [id*="guias"] .DataGrid thead tr th:nth-child(4),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(4),
                    [id*="guias"] .DataGrid thead tr th:nth-child(5),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(5),
                    [id*="guias"] .DataGrid thead tr th:nth-child(8),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(8),
                    [id*="guias"] .DataGrid thead tr th:nth-child(9),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(9),
                    [id*="guias"] .DataGrid thead tr th:nth-child(13),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(13),
                    [id*="guias"] .DataGrid thead tr th:nth-child(17),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(17),
                    [id*="guias"] .DataGrid thead tr th:nth-child(18),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(18),
                    [id*="guias"] .DataGrid thead tr th:nth-child(19),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(19),
                    [id*="guias"] .DataGrid thead tr th:nth-child(20),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(20),
                    [id*="guias"] .DataGrid thead tr th:nth-child(14),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(14),
                    [id*="guias"] .DataGrid thead tr th:nth-child(17),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(17),
                    [id*="tabelaPedido"] thead tr th:nth-child(2),
                    [id*="tabelaPedido"] tbody tr td:nth-child(2),
                    [id*="tabelaPedido"] thead tr th:nth-child(4),
                    [id*="tabelaPedido"] tbody tr td:nth-child(4),
                    [id*="tabelaPedido"] thead tr th:nth-child(6),
                    [id*="tabelaPedido"] tbody tr td:nth-child(6),
                    [id*="tabelaPedido"] thead tr th:nth-child(13),
                    [id*="tabelaPedido"] tbody tr td:nth-child(13),
                    [id*="tabelaPedido"] thead tr th:nth-child(14),
                    [id*="tabelaPedido"] tbody tr td:nth-child(14),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(18),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(18),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(19),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(19),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(15),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(15),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(16),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(16),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(14),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(14),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(11),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(11),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(9),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(9){
                        min-width:90px !important;
                        width:90px !important;
                        max-width:90px !important;
                    }

                    [id*="guias"] .DataGrid thead tr th:nth-child(5),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(5),
                    [id*="guias"] .DataGrid thead tr th:nth-child(6),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(6),
                    [id*="guias"] .DataGrid thead tr th:nth-child(7),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(7),
                    [id*="guias"] .DataGrid thead tr th:nth-child(10),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(10),
                    [id*="guias"] .DataGrid thead tr th:nth-child(12),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(12),
                    [id*="guias"] .DataGrid thead tr th:nth-child(15),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(15),
                    [id*="guias"] .DataGrid thead tr th:nth-child(16),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(16),
                    [id*="guias"] .DataGrid thead tr th:nth-child(14),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(14),
                    [id*="tabelaPedido"] thead tr th:nth-child(10),
                    [id*="tabelaPedido"] tbody tr td:nth-child(10),
                    [id*="tabelaPedido"] thead tr th:nth-child(13),
                    [id*="tabelaPedido"] tbody tr td:nth-child(13),
                    [id*="tabelaPedido"] thead tr th:nth-child(7),
                    [id*="tabelaPedido"] tbody tr td:nth-child(7),
                    [id*="tabelaPedido"] thead tr th:nth-child(9),
                    [id*="tabelaPedido"] tbody tr td:nth-child(9),
                    [id*="guias"] .DataGrid thead tr th:nth-child(9),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(9),
                    [id*="guias"] .DataGrid thead tr th:nth-child(13),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(13),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(17),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(17),
                    [id*="tabelaPedido"] thead tr th:nth-child(3),
                    [id*="tabelaPedido"] tbody tr td:nth-child(3),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(10),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(10){
                        min-width:120px !important;
                        width:120px !important;
                        max-width:120px !important;
                    }

                    [id*="guias"] .DataGrid thead tr th:nth-child(4),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(4),
                    [id*="guias"] .DataGrid thead tr th:nth-child(3),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(3),
                    [id*="guias"] .DataGrid thead tr th:last-child,
                    [id*="guias"] .DataGrid tbody tr td:last-child,
                    [id*="tabelaPedido"] thead tr th:nth-child(5),
                    [id*="tabelaPedido"] tbody tr td:nth-child(5),
                    [id*="tabelaPedido"] thead tr th:nth-child(8),
                    [id*="tabelaPedido"] tbody tr td:nth-child(8),
                    [id*="tabelaExtrato"] thead tr th:nth-child(1),
                    [id*="tabelaExtrato"] tbody tr td:nth-child(1),
                    [id*="tabelaExtrato"] thead tr th:nth-child(3),
                    [id*="tabelaExtrato"] tbody tr td:nth-child(3),
                    [id*="tabelaExtrato"] thead tr th:nth-child(4),
                    [id*="tabelaExtrato"] tbody tr td:nth-child(4){
                        min-width:250px !important;
                        width:250px !important;
                        max-width:250px !important;
                    }

                    [id*="tabelaExtrato"] thead tr th:not(:nth-child(1)),
                    [id*="tabelaExtrato"] tbody tr td:not(:nth-child(1)){
                        min-width:150px !important;
                        width:150px !important;
                        max-width:150px !important;
                    }

                    [id*="tabelaExtrato"] thead tr th:nth-child(5),
                    [id*="tabelaExtrato"] tbody tr td:nth-child(5),
                    [id*="tabelaExtrato"] thead tr th:nth-child(8),
                    [id*="tabelaExtrato"] tbody tr td:nth-child(8),
                    [id*="tabelaExtrato"] thead tr th:nth-child(9),
                    [id*="tabelaExtrato"] tbody tr td:nth-child(9),
                    [id*="tabelaExtrato"] thead tr th:nth-child(10),
                    [id*="tabelaExtrato"] tbody tr td:nth-child(10),
                    [id*="tabelaExtrato"] thead tr th:nth-child(11),
                    [id*="tabelaExtrato"] tbody tr td:nth-child(11)
                    {
                        min-width:250px !important;
                        width:250px !important;
                        max-width:250px !important;
                    }

                    [id*="guias"] .DataGrid thead tr th:last-child,
                    [id*="guias"] .DataGrid tbody tr td:last-child,
                    [id*="guias"] .DataGrid thead tr th:nth-child(2),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(2){
                        min-width:150px !important;
                        width:150px !important;
                        max-width:150px !important;
                    }

                    [id*="guias"] .DataGrid thead tr th:nth-child(11),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(11),
                    [id*="guias"] .DataGrid thead tr th:nth-child(12),
                    [id*="guias"] .DataGrid tbody tr td:nth-child(12),
                    [id*="tabelaPedido"] thead tr th:nth-child(11),
                    [id*="tabelaPedido"] tbody tr td:nth-child(11),
                    [id*="tabelaPedido"] thead tr th:nth-child(12),
                    [id*="tabelaPedido"] tbody tr td:nth-child(12),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(12),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(12),
                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(10),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(10){
                        min-width:160px !important;
                        width:160px !important;
                        max-width:160px !important;
                    }

                    [id*="tabelaPreOrderDetalhado"] thead tr th:nth-child(13),
                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(13){
                        min-width:350px !important;
                        width:350px !important;
                        max-width:350px !important;
                    }

                    [id*="tabelaPreOrderDetalhado"] tbody tr td:nth-child(13){
                        text-align:left !important;
                    }

                    [id*="tabelaPreOrderDetalhado"] thead tr th,
                    [id*="tabelaPreOrderDetalhado"] thead tr td{
                        background: linear-gradient(to bottom, #202020, #555) !important;
                        border-color:#999 !important;
                    }


                    .DataGrid tbody tr td,
                    .DataGrid tbody tr th{
                        white-space: nowrap;
                        overflow:hidden;
                        text-overflow:ellipsis;
                        text-align: center !important;
                    }
                }

                .ui-expanded-row-content .ui-datatable-scrollable-body{
                    height:auto !important;
                }

                .Transito{
                    background-color:#fffece;
                    color:#B95411;
                }

                .Pendente{
                    background-color:mistyrose;
                    color:red;
                }

                .Finalizado{
                    background-color: #d2f2e9;
                    color:forestgreen;
                }

                .Transito label, .Pendente label, .Finalizado label{
                    width:150px !important;
                    min-width:150px !important;
                    max-width:150px !important;
                    margin-top:3px !important;
                    height:15px !important;
                    font-weight:500 !important;
                    text-align:center !important;
                    padding-left:10px;
                    height:100% !important;
                    cursor:pointer !important;
                    padding-top:4px !important;
                    padding-bottom:3px !important;
                }

                [id*="guias"] .DataGrid thead tr th:nth-child(1),
                [id*="guias"] .DataGrid tbody tr td:nth-child(1){
                    padding:0px !important;
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {  
                    #mapGoogle{
                        width:calc(100% - 15px) !important;
                    }
                }

                .AddGuia,
                .Guia{
                    height:200px;
                    background-color:#82cc8c;
                    color:#deffe2;
                    border-radius: 10px;
                    font-size:10pt !important;
                    cursor:pointer;
                    margin-top:2px !important;
                    margin-bottom:2px !important;
                    border:thin solid #82cc8c;
                    transition: all 0.1s;
                    margin-left: 6px !important;
                    margin-right: 6px !important;
                }

                .AddGuia:hover,
                .Guia{
                    background-color:#c8fcce;
                    color: forestgreen;
                    border-color: #82cc8c;
                }

                .AddGuia i{
                    width:100%;
                    text-align:center;
                    font-size:24pt !important;
                    margin-bottom:8px;
                    cursor:pointer;
                    margin-top:48px
                }

                .AddGuia label{
                    font-weight:bold;
                    width:100%;
                    text-align:center;
                    margin:0px !important;
                    padding:0px !important;
                    cursor:pointer;
                }

                .Guia{
                    padding-left: 2px !important;
                    padding-right: 2px !important;
                }

                #divFormBaixa label,
                #divFormVolume label{
                    width:100% !important;
                    display:block;
                    font-size:8pt !important;
                    font-weight:bold !important;
                    color:dimgray !important;
                    margin:0px 0px -2px 0px !important;
                }

                #divFormBaixa [class*="col-md"],
                #divFormVolume [class*="col-md"]{
                    padding:4px !important;
                }

                .jconfirm-content-pane{
                    height:auto !important;
                }

                .jconfirm-box-container{
                    margin-top:-120px !important;
                }

                .jconfirm-title{
                    color:#000 !important;
                }

                #divFormBaixa input,
                #divFormBaixa select,
                #divFormVolume input,
                #divFormVolume select{
                    border-top:none !important;
                    border-left:none !important;
                    border-right:none !important;
                    box-shadow:none !important;
                    outline:none !important;
                    padding-left:4px !important;
                }

                .ExcluirGuia{
                    position:absolute;
                    right:-5px;
                    top:-8px;
                    color:red;
                    cursor:pointer;
                    font-size:16pt;
                    background-color:#FFF;
                    border-radius:50%;
                }

                .TituloGuia{
                    font-size:10pt; 
                    font-weight:bold !important;
                    width:100%;
                    text-align:center; 
                    padding:3px 0px 3px 0px !important; 
                    border-bottom: thin solid #669F6F !important;  
                    border-top: thin solid #669F6F !important;  
                    color: #FFF;
                    background-color: #82cc8c;
                    border-radius: 11px;
                    padding:0px !important;
                    margin: 2px 0px 0px 0px !important;
                }

                .Titulo{
                    font-size:8pt; 
                    font-weight:bold !important;
                    width: 100%;
                    text-align: left;
                    margin: 0px;
                }

                .Valor{
                    font-size:9pt; 
                    white-space: nowrap;
                    font-weight: 500 !important;
                    width: 100%;
                    text-align: left;
                    margin: -3px 0px 0px 0px;
                    color: #000 !important;
                }

                .FundoVolumes{
                    background-color: #82cc8c;
                    border-radius:9px;
                    height:91px;
                    border:thin solid #669F6F !important; 
                    padding:0px !important;
                }

                .btAddVolumes{
                    width: 100%;
                    background-color: #000;
                    color: #FFF;
                    font-weight: bold;
                    font-size: 8pt;
                    text-align: center;
                    padding: 1px 0px 1px 0px !important;
                    border-radius: 20px;
                    cursor:pointer;
                }

                .lblVolume{
                    padding: 2px 0px 2px 2px !important;
                    text-align: left;
                    width: 100%;
                    position: relative;
                    font-size: 9pt;
                    color:#000;
                    cursor:pointer;
                }

                .lblVolume i{
                    color: red;
                    position:absolute;
                    right: 4px;
                    cursor:pointer;
                    font-size:10pt;
                    background-color: #FFF;
                    text-align: center;
                    padding:0px !important;
                }

                .lblVolume:not(:last-child){
                    border-bottom: thin solid #77BB82 !important;
                }

                .fa-barcode{
                    display: none;
                }

                @media only screen and (max-width: 767px) and (min-width: 10px) {  
                    .fa-barcode{
                        display: initial;
                    }
                    
                    .calendario .ui-inputfield {
                        width: 190px !important;
                        position: absolute;
                        right: 0px;
                        padding: 0px !important;
                        margin-top: 4px !important
                    }

                    .p-datepicker-panel{
                        left: auto !important;
                        right: 0px !important;
                    }

                    /*.ui-icon-calendar,*/
                    a[id*="cabecalho"]:not([title*="Voltar"]),
                    .ui-tabs-nav li:nth-child(2),
                    .ui-tabs-nav li:nth-child(6)/*,
                    [id*="btGeralHome"]*/{
                        display: none !important;
                    }
					
					
					
					body .ui-calendar button.ui-datepicker-trigger>span.ui-icon-calendar{
						top: 8px !important;
					}
					
					.ui-icon-calendar{
						margin-left: -2px !important
					}

                    .ui-tabs-nav li:nth-child(1) a,
                    .ui-tabs-nav li:nth-child(2) a,
                    .ui-tabs-nav li:nth-child(3) a,
                    .ui-tabs-nav li:nth-child(6) a,
                    .ui-tabs-nav li:nth-child(4) a{
                        padding: 13px 0px 0px 0px !important;
                        text-align: center !important;
                        width: 100% !important;
                        margin: 0px !important;
                    }

                    .ui-tabs-nav li:nth-child(5) a{
                        padding: 7px 0px 0px 0px !important;
                        text-align: center !important;
                        width: 100% !important;
                        margin: 0px !important;
                    }

                    .ui-tabs-nav li:nth-child(1) a,
                    .ui-tabs-nav li:nth-child(3) a,
                    .ui-tabs-nav li:nth-child(5) a{
                        margin-left: 4px !Important
                    }

                    .ui-tabs-nav li:nth-child(4) a,
                    .ui-tabs-nav li:nth-child(6) a{
                        margin-left: 3px !Important
                    }



                    [id*="tabela"] tr td:nth-child(1),
                    [id*="tabela"] tr td:nth-child(2){
                        white-space: nowrap !important;
                    }

                    [id*="tabela"] tr td:nth-child(1) > span{
                        margin-left: 2px !important;
                    }

                    .FundoPagina{
                        height: auto !important;
                    }

                    .ui-tabs-panel[id*="mainTabView"]{
                        height: calc(100vh - 280px) !important; 
                        overflow-x: hidden !important;
                    }

                    .ui-tabs-panel[id*="mainTabView"] div{
                        overflow-x: hidden !important;
                    }
                    
                    [id*="paginator_top"]{
                        overflow: hidden !important;
                    }

                    [id*="guias"] .DataGrid thead tr th:nth-child(3) span,
                    [id*="guias"] .DataGrid tbody tr td:nth-child(3) span{
                        font-weight: bold !important;
                    }

                    [id*="guias"] .DataGrid thead tr th:nth-child(4) span,
                    [id*="guias"] .DataGrid tbody tr td:nth-child(4) span{
                        font-weight: bold !important;
                    }

                    [id*="guias"] .DataGrid tbody tr td:nth-child(3) span:nth-child(2){
                        font-size: 12pt !important;
                        margin-left: 0px !important;
                        padding-left: 0px !important;
                    }

                    [id*="guias"] .DataGrid tbody tr td:nth-child(4) span:nth-child(2){
                        font-size: 12pt !important;
                        margin-left: -7px !important;
                        padding-left: 0px !important;
                    }

                    [id*="guias"] .DataGrid tbody tr td:nth-child(1) label{
                        margin-left: -15px !important;
                    }

                    [id*="guias"] .DataGrid thead tr th:nth-child(8) span,
                    [id*="guias"] .DataGrid tbody tr td:nth-child(8) span{
                        font-weight: bold !important;
                    }
                }

                .iframeCompleto{
                    width: 100% !important;
                    border: none !important;
                    padding: 0px !important;
                    height: 100% !important;
                    box-shadow: none !important;
                    outline: none !important;
                    overflow: hidden !important;
                }

                #divQrCode {
                    width: 100%;
                    height: 100%;
                    /*max-width:320px;
                    max-height:320px;*/
                    top: 0 !important;
                    right: 0 !important;
                    bottom: 0 !important;
                    left: 0 !important;
                    margin: 0 auto !important;
                    padding: 0px !important;
                }

                .jconfirm-closeIcon{
                    color: #666 !important;
                }

                .DisponivelEmissao,
                .DisponivelEdicao{
                    position: relative;
                    float: left;
                    font-size: 7pt !important;
                    padding: 2px !important;
                    white-space: nowrap !important;
                    font-weight: bold !important;
                    background-color: red;
                    color: #FFF;
                    text-align: center !important;
                    min-width: 100% !important;
                    width: 100% !important;
                    max-width: 100% !important;
                }

                .DisponivelEdicao{
                    background-color: forestgreen;
                    min-width: 100% !important;
                    width: 100% !important;
                    max-width: 100% !important;
                }
                
                
                @media(max-width: 510px){
                    #divCalendario{
                        position: absolute;
                        top: 46px !important;
                        right: 0px !important;
                    }
                    
                    .footer-user{
                        display: none !important;
                    }
                }
                
                #divCorporativo{
                    top: 0px !important;
                }

            </style>
        </h:head>
        <h:body id="h">
            <script type="text/javascript">
                // <![CDATA[
                var $RefList, $RelVolume;
                var RefParamento = '#{login.pp.empresa}';
                var RefCodPessoa = '#{valores.codpessoa}';
                var RefOperador = '#{valores.operador}';
                var RefCodFil = '#{valores.codfil}';
                let inSequencia, inParada;
                let editarGuia = false;

                function EnviarGuias()
                {
                    let arrayJson = new Array();
                    let arrayJsonGuias = new Array();
                    let arrayJsonVolumes = new Array();

                    $('#divGuiasVolumes .Guia').each(function () {
                        $Guia = $(this);
                        arrayJsonVolumes = new Array();

                        $Guia.find('.lblVolume').each(function () {
                            $Volume = $(this);

                            arrayJsonVolumes.push({
                                'lacre': $Volume.attr('lacre'),
                                'qtvolumes': '1',
                                'valoresLacres': TratarValorEnvio($Volume.attr('valor')),
                                'observacaoLacres': $Volume.attr('obs'),
                                'tipoLacres': $Volume.attr('celula_moeda')
                            });
                        });

                        arrayJsonGuias.push({
                            'guia': $Guia.find('.TituloGuia').text().split('-')[0],
                            'moeda': $Guia.find('[ref="TipoMoeda"]').text(),
                            'serie': $Guia.find('.TituloGuia').text().split('-')[1],
                            'valor': TratarValorEnvio($Guia.find('[ref="ValorTotal"]').text()),
                            'rpv': $Guia.find('[ref="Rpv"]').text(),
                            'volumes': arrayJsonVolumes
                        });
                    });

                    let somenteExclusao = 'N';

                    if ($('.Guia').length == 0) {
                        somenteExclusao = 'S';
                    }

                    arrayJson.push({
                        'param': RefParamento,
                        'codpessoa': RefCodPessoa,
                        'operador': RefOperador.split(' ')[0],
                        'sequencia': inSequencia,
                        'codfil': RefCodFil,
                        'parada': inParada,
                        'er': 'R',
                        'tiposerv': '',
                        'hora1': '',
                        'hrcheg': '',
                        'hrsaidavei': '',
                        'hrsaida': '',
                        'km': '',
                        'obs': '',
                        'guias': arrayJsonGuias,
                        'portalGuias': 'S',
                        'somenteExclusao': somenteExclusao
                    });

                    /*alert(JSON.stringify(arrayJson))
                     return false;*/

                    const urlWeb = 'https://mobile.sasw.com.br/SatWebServiceHomologG/api/ws-rotas/baixaservico';
                    const urlLocal = 'http://localhost:8080/SatWebService/api/ws-rotas/baixaservico';

                    $.ajax({
                        url: urlWeb,
                        method: 'POST',
                        data: JSON.stringify(arrayJson[0])
                    })
                            .done(function (response) {
                                //if (response.resp.toString() === '1') {
                                $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.DadosSalvosSucesso}', function () {
                                    location.reload();
                                });
                                //}
                                /*else
                                 alert('Error: ' + JSON.stringify(response));*/
                            })
                            .fail(function (fail) {
                                //alert('Error: ' + JSON.stringify(fail));
                            });
                }

                function GerarGuiaAutomatica() {
                    let SeqRota = inSequencia;
                    let Parada = inParada;
                    let Ordem = eval($('#divGuiasVolumes').find('.Guia').length) + 1;
                    let Lpad = 0;

                    Lpad = (6 - SeqRota.length);
                    for (I = 0; I < eval(Lpad); I++)
                        SeqRota = '0' + SeqRota;

                    Lpad = (3 - Parada.length);
                    for (I = 0; I < eval(Lpad); I++)
                        Parada = '0' + Parada;

                    Lpad = (2 - Ordem.toString().length);
                    for (I = 0; I < eval(Lpad); I++)
                        Ordem = '0' + Ordem;

                    return ReplaceAll((SeqRota.toString() + Parada.toString() + Ordem.toString()), '.', '');
                }

                function CalcularTotalGuia($obj) {
                    let ValorFloat = 0.0;

                    $obj.find('.lblVolume').each(function () {
                        if ($(this).attr('valor').toString().split(',')[1].length == 2)
                            ValorFloat += parseFloat(ReplaceAll(ReplaceAll($(this).attr('valor').toString(), '.', ''), ',', '.'));
                        else
                            ValorFloat += parseFloat(ReplaceAll($(this).attr('valor').toString(), ',', ''));
                    });

                    var ValorTotal = parseFloat(ValorFloat);

                    if ($obj.find('[ref="TipoMoeda"]').text() === 'BRL')
                        $obj.find('[ref="ValorTotal"]').text(ValorTotal.formatMoney(2, "", ".", ","));
                    else
                        $obj.find('[ref="ValorTotal"]').text(ValorTotal.formatMoney(2, "", ",", "."));
                }

                function TratarValorEnvio(Valor) {
                    let ValorFloat = 0.0;

                    if (Valor.toString().split(',')[1].length == 2)
                        ValorFloat = parseFloat(ReplaceAll(ReplaceAll(Valor.toString(), '.', ''), ',', '.'));
                    else
                        ValorFloat = parseFloat(ReplaceAll(Valor.toString(), ',', ''));

                    let Retorno = ValorFloat.formatMoney(2, "", ",", ".");

                    return ReplaceAll(Retorno, ',', '');
                }

                function TratarValorFormato(Valor) {
                    let ValorFloat = 0.0;

                    if (Valor.indexOf(',') == -1) {

                        if (Valor.indexOf('.') > -1 && Valor.split('.')[1].length == 1) {
                            Valor = Valor + '0';
                        }
                    }

                    ValorFloat = parseFloat(ReplaceAll(Valor.toString(), ',', ''));

                    let Retorno = ValorFloat.formatMoney(2, "", ".", ",");

                    return Retorno;
                }

                function FormularioVolumes() {
                    let HTML = '';

                    HTML += '<div id="divFormVolume" style="width:100% !important; display:block !important">';
                    HTML += '   <div class="col-md-12 col-sm-12 col-xs-12" style="position: relative">';
                    HTML += '      <label>#{localemsgs.Lacre}</label>';
                    HTML += '      <input id="txtVolLacre" type="tel" class="form-control" south-required="S" maxlength="15" />';
                    HTML += '       <i class="fa fa-barcode" aria-hidden="true" style="position: absolute; right: 11px; top: 15px;color: #000 !important; font-size: 25pt !important;"></i>';
                    HTML += '   </div>';
                    HTML += '   <div class="col-md-5 col-sm-5 col-xs-12">';
                    HTML += '      <label>#{localemsgs.Valor}</label>';
                    HTML += '      <input id="txtVolValor" type="tel" class="form-control" south-type="monetario" south-required="S" />';
                    HTML += '   </div>';
                    HTML += '   <div class="col-md-7 col-sm-7 col-xs-12">';
                    HTML += '      <label>#{localemsgs.TipoMoeda}</label>';
                    HTML += '      <select id="cboCelulaMoeda" class="form-control" south-required="S">';
                    HTML += '         <option value="-1">#{localemsgs.Selecione}</option>';
                    HTML += '         <option value="1" selected="selected">#{localemsgs.Cedulas}</option>';
                    HTML += '         <option value="3">#{localemsgs.Moedas}</option>';
                    HTML += '         <option value="2">#{localemsgs.Cheques}</option>';
                    HTML += '         <option value="4">#{localemsgs.MetaisPreciosos}</option>';
                    HTML += '         <option value="5">#{localemsgs.MoedaExtrangeira}</option>';
                    HTML += '         <option value="9">#{localemsgs.OutrosDesc}</option>';
                    HTML += '      </select>';
                    HTML += '   </div>';
                    HTML += '   <div class="col-md-12 col-sm-12 col-xs-12">';
                    HTML += '      <label>#{localemsgs.Obs}</label>';
                    HTML += '      <input id="txtVolObs" type="text" class="form-control" />';
                    HTML += '   </div>';
                    HTML += '</div>';

                    return HTML;
                }

                function PreAbrirFormAddGuias(jSon, sequencia, parada) {
                    AbrirFormAddGuias(sequencia, parada, function () {
                        let HTML = '';

                        if (jSon.length > 0 || editarGuia) {
                            for (qtde = 0; qtde < jSon.length; qtde++) {
                                HTML += '<div class="col-md-3 col-sm-3 col-xs-7 Guia">';
                                if (editarGuia) {
                                    HTML += '  <i class="fa fa-minus-circle ExcluirGuia" title="#{localemsgs.ExcluirGuia}"></i>';
                                }
                                HTML += '  <label class="TituloGuia">' + jSon[qtde].RPV.replace('.0', '') + '-' + jSon[qtde].serie.replace('.0', '') + '</label>';
                                HTML += '  <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; border-bottom: thin dashed #82cc8c !important;">';
                                HTML += '      <div class="col-md-12 col-sm-12 col-xs-12" style="padding:1px !important">';
                                HTML += '          <label class="Titulo">#{localemsgs.Valor}</label>';
                                try {
                                    HTML += '          <label class="Valor" ref="ValorTotal">' + TratarValorFormato(jSon[qtde].valor) + '</label>';
                                } catch (e) {
                                    HTML += '          <label class="Valor" ref="ValorTotal">' + jSon[qtde].valor + '</label>';
                                }
                                HTML += '      </div>';
                                HTML += '  </div>';
                                HTML += '  <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important;">';
                                HTML += '      <div class="col-md-5 col-sm-5 col-xs-5" style="padding:1px !important">';
                                HTML += '          <label class="Titulo">#{localemsgs.Moeda}</label>';
                                HTML += '          <label class="Valor" ref="TipoMoeda">' + jSon[qtde].moeda + '</label>';
                                HTML += '      </div>';
                                HTML += '      <div class="col-md-7 col-sm-7 col-xs-7" style="padding:1px !important">';
                                HTML += '          <label class="Titulo">#{localemsgs.RPV}</label>';
                                HTML += '          <label class="Valor" ref="Rpv">' + (qtde + 1).toString() + '</label>';
                                HTML += '      </div>';
                                HTML += '  </div>';
                                HTML += '  <div class="col-md-12 col-sm-12 col-xs-12 FundoVolumes" style="padding:1px 2px 1px 2px !important;">';
                                HTML += '      <label class="btAddVolumes"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;#{localemsgs.Volumes}</label>';
                                HTML += '      <div ref="List" class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; overflow: auto !important; height: 57px; position:relative; margin-top:-2px !important;">';

                                let btExcluirVolume = '';

                                if (editarGuia) {
                                    btExcluirVolume = '<i class="fa fa-minus-square" title="#{localemsgs.ExcluirVolume}"></i>';
                                }

                                for (qtdeVolumes = 0; qtdeVolumes < jSon[qtde].volumes.length; qtdeVolumes++) {

                                    HTML += '<label class="lblVolume" lacre="' + jSon[qtde].volumes[qtdeVolumes].Lacre + '"';
                                    HTML += '                  celula_moeda="' + jSon[qtde].volumes[qtdeVolumes].Tipo + '"';
                                    HTML += '                         valor="' + jSon[qtde].volumes[qtdeVolumes].Valor.formatMoney(2, "", ".", ",") + '"';
                                    HTML += '                           obs="' + jSon[qtde].volumes[qtdeVolumes].Obs + '" title="#{localemsgs.CliqueParaDetalhes}" style="margin: 0px !important">' + jSon[qtde].volumes[qtdeVolumes].Lacre + btExcluirVolume + '</label>';
                                }
                                HTML += '      </div>';
                                HTML += '  </div>';
                                HTML += '</div>';
                            }

                            $('#divGuiasVolumes').append(HTML);

                            /*$('.Guia').each(function () {
                             CalcularTotalGuia($(this));
                             });*/
                        } else {
                            $('#divGuiasVolumes').html('<label style="color: #AAA; font-size: 18pt !important;font-weight: 500 !important;">Não há Guias!</label>');
                        }
                    });
                }

                function AbrirFormAddGuias(sequencia, parada, callBack) {
                    inSequencia = sequencia;
                    inParada = parada;
                    let HTML = '';

                    if (editarGuia) {
                        HTML = `<div id="divGuiasVolumes" sequencia="${inSequencia}" parada="${inParada}" class="col-md-12 col-sm-12 col-xs-12" style="padding:6px 2px 6px 2px !important; height: 216px; overflow:auto !important; float:initial">
                            <div class="col-md-2 col-sm-2 col-xs-4 AddGuia">
                               <i class="fa fa-plus-circle"></i>
                                   <label>#{localemsgs.Adicionar}</label>
                                   <label>#{localemsgs.Guia}</label>
                               </div>
                           </div>
                        </div>`;
                    } else {
                        HTML = `<div id="divGuiasVolumes" sequencia="${inSequencia}" parada="${inParada}" class="col-md-12 col-sm-12 col-xs-12" style="padding:6px 2px 6px 2px !important; height: 216px; overflow:auto !important; float:initial">
                            
                        </div>`;
                    }

                    $JanelaForm = $.alert({
                        icon: 'fa fa-file fa-lg',
                        type: 'blue',
                        title: '#{localemsgs.Guias}',
                        closeIcon: true,
                        content: HTML,
                        columnClass: 'large',
                        buttons: {
                            cancel: {
                                text: '#{localemsgs.Cancelar}'
                            },
                            ok: {
                                text: '<i class="fa fa-save"></i>&nbsp;&nbsp;' + '#{localemsgs.Salve}',
                                btnClass: 'btn-blue',
                                isHidden: !editarGuia,
                                action: function () {
                                    let ValidarDados = true;

                                    if (ValidarDados) {
                                        /*if ($('.Guia').length == 0) {
                                         $.MsgBoxVermelhoOk('#{localemsgs.Aviso}', '#{localemsgs.InformeGuias}');
                                         return false;
                                         } else {*/
                                        EnviarGuias();
                                        //}
                                    } else {
                                        $.MsgBoxVermelhoOk('#{localemsgs.Aviso}', '#{localemsgs.PrenchaCamposObrigaatorios}');
                                        return false;
                                    }

                                }
                            }
                        },
                        onContentReady: function () {
                            //CriarAtributos(Language);

                            if (typeof callBack == 'function')
                                callBack.call();
                        }
                    });
                }

                function LerQrCode() {
                    var HTML = '';
                    HTML = '<div south-type="js-confirm-content" ref-size="230" style="height:' + eval(($(document).height() - 230)) + 'px; overflow:hidden !important; padding:0px !important; width:100% !important;">';
                    HTML += '   <div id="divQrCode" style="position:relative;">';
                    HTML += '      <img id="imgLoadQrCode" src="../assets/images/Load1.gif" style="position:absolute;top:30px;right:0;bottom:0;left:0;margin:auto;" />';
                    HTML += '      <iframe id="ifrLeitor" src="../leitor_barcode.html" class="iframeCompleto" style="opacity: 0"></iframe>';
                    HTML += '   </div>';
                    HTML += '</div>';

                    frmScanner = $.confirm({
                        dialogClass: "no-titlebar",
                        type: 'blue',
                        containerFluid: true,
                        closeIcon: false,
                        title: '<font color="#1a79b9"><i class="fa fa-barcode"></i>&nbsp;Leitor de Código de Barras</font>',
                        content: HTML,
                        columnClass: 'xlarge',
                        useBootstrap: true,
                        offsetTop: 150,
                        offsetBottom: 0,
                        scrollToPreviousElement: true,
                        alignMiddle: false,
                        buttons: {
                            cancel: {
                                text: '<i class="fa fa-ban"></i>&nbsp;Cancelar'
                            }
                        },
                        onContentReady: function () {
                            setTimeout(function () {
                                $('#imgLoadQrCode').css('display', 'none');
                                $('#ifrLeitor').css('opacity', '1');
                            }, 2000);
                        }
                    });
                }

                function RetornoBarCode(BarCode) {
                    $('#txtVolLacre').val(BarCode);

                    setTimeout(function () {
                        frmScanner.close();
                    }, 500);
                }

                $(document)
                        .on('click', '.ExcluirGuia', function () {
                            $(this).parent('div').remove();
                        })
                        .on('click', '.fa-barcode', function () {
                            $('#divQrCode').css('display', 'inline-block');
                            LerQrCode();
                        })
                        .on('click', '.lblVolume i', function () {
                            $obj = $(this).parent('label').parent('div').parent('.FundoVolumes').parent('.Guia');
                            $(this).parent('label').remove();
                            CalcularTotalGuia($obj);
                        })
                        .on('click', '.lblVolume', function () {
                            $RefList = $(this).parents('[ref="List"]');
                            $objCalc = $(this).parent('div').parent('.FundoVolumes').parent('.Guia');
                            $RelVolume = $(this);
                            const lacreSel = $(this).attr('lacre');

                            setTimeout(function () {
                                if ($RefList && $RefList.html()) {
                                    if ($RelVolume.find('i').attr('class').indexOf('minus') > -1) {
                                        let HTML = FormularioVolumes();

                                        $.ModalDialogCallBack('large',
                                                'fa fa-plus fa-lg',
                                                '#{localemsgs.Volume}',
                                                HTML,
                                                'blue',
                                                'fa fa-save',
                                                '#{localemsgs.Salve}',
                                                '#{localemsgs.Cancelar}',
                                                function () {
                                                    if ($.ValidaForm($('#divFormVolume'), true, '#{localemsgs.Aviso}', '#{localemsgs.PrenchaCamposObrigaatorios}')) {

                                                        if ($('.lblVolume[lacre="' + lacreSel + '"]').length > 0) {
                                                            $RelVolume.attr('lacre', $('#txtVolLacre').val());
                                                            $RelVolume.attr('celula_moeda', $('#cboCelulaMoeda').val());
                                                            $RelVolume.attr('valor', $('#txtVolValor').val());
                                                            $RelVolume.attr('obs', $('#txtVolObs').val());
                                                            $RelVolume.html($('#txtVolLacre').val() + '<i class="fa fa-minus-square" title="#{localemsgs.ExcluirVolume}"></i>');
                                                        } else {
                                                            let HTML = '';

                                                            HTML += '<label class="lblVolume" lacre="' + $('#txtVolLacre').val() + '"';
                                                            HTML += '                  celula_moeda="' + $('#cboCelulaMoeda').val() + '"';
                                                            HTML += '                         valor="' + $('#txtVolValor').val() + '"';
                                                            HTML += '                           obs="' + $('#txtVolObs').val() + '" title="#{localemsgs.CliqueParaDetalhes}" style="margin: 0px !important">' + $('#txtVolLacre').val() + '<i class="fa fa-minus-square" title="#{localemsgs.ExcluirVolume}"></i></label>';

                                                            $RefList.append(HTML);
                                                        }
                                                        CalcularTotalGuia($objCalc);

                                                        $JanelaForm.close();
                                                    }
                                                },
                                                function () {

                                                    if (!editarGuia) {
                                                        $('.jconfirm-buttons .btn-blue').css('display', 'none');
                                                    }
                                                    $('#txtVolLacre').val($RelVolume.attr('lacre'));
                                                    $('#cboCelulaMoeda').val($RelVolume.attr('celula_moeda'));
                                                    $('#txtVolValor').val($RelVolume.attr('valor'));
                                                    $('#txtVolObs').val($RelVolume.attr('obs'));

                                                    $('#txtVolLacre').focus();
                                                },
                                                '#{localeController.number}');
                                    }
                                }
                            }, 300);
                        })
                        .on('click', '.btAddVolumes', function () {
                            if (editarGuia) {
                                $RefList = $(this).parent('div').find('[ref="List"]');
                                $RelVolume = null;

                                let HTML = FormularioVolumes();

                                $.ModalDialogCallBack('large',
                                        'fa fa-plus fa-lg',
                                        '#{localemsgs.Volume}',
                                        HTML,
                                        'blue',
                                        'fa fa-save',
                                        '#{localemsgs.Salve}',
                                        '#{localemsgs.Cancelar}',
                                        function () {
                                            if ($.ValidaForm($('#divFormVolume'), true, '#{localemsgs.Aviso}', '#{localemsgs.PrenchaCamposObrigaatorios}')) {
                                                let HTML = '';

                                                HTML += '<label class="lblVolume" lacre="' + $('#txtVolLacre').val() + '"';
                                                HTML += '                  celula_moeda="' + $('#cboCelulaMoeda').val() + '"';
                                                HTML += '                         valor="' + $('#txtVolValor').val() + '"';
                                                HTML += '                           obs="' + $('#txtVolObs').val() + '" title="#{localemsgs.CliqueParaDetalhes}" style="margin: 0px !important">' + $('#txtVolLacre').val() + '<i class="fa fa-minus-square" title="#{localemsgs.ExcluirVolume}"></i></label>';

                                                $RefList.append(HTML);

                                                CalcularTotalGuia($RefList.parent('.FundoVolumes').parent('.Guia'));

                                                $JanelaForm.close();
                                            }
                                        },
                                        function () {
                                            $('#txtVolLacre').focus();
                                        },
                                        '#{localeController.number}');
                            }
                        })
                        .on('click', '.AddGuia', function () {
                            let HTML = '';

                            HTML += '<div id="divFormBaixa" style="width:100% !important; display:block !important">';
                            HTML += '   <div class="col-md-12 col-sm-12 col-xs-12">';
                            HTML += '      <label>#{localemsgs.TipoGuia}</label>';
                            HTML += '      <select id="cboRPV" class="form-control" south-required="S">';
                            HTML += '         <option value="1" descricao="#{localemsgs.GuiaEletronica}">#{localemsgs.GuiaEletronica}</option>';
                            HTML += '      </select>';
                            HTML += '   </div>';
                            HTML += '   <div class="col-md-9 col-sm-9 col-xs-8">';
                            HTML += '      <label>#{localemsgs.Guia}</label>';
                            HTML += '      <input id="txtVolGuia" type="tel" class="form-control" south-required="S" maxlength="9" />';
                            HTML += '   </div>';
                            HTML += '   <div class="col-md-3 col-sm-3 col-xs-4">';
                            HTML += '      <label>#{localemsgs.Serie}</label>';
                            HTML += '      <input id="txtVolSerie" type="tel" class="form-control" south-required="S" maxlength="2" />';
                            HTML += '   </div>';
                            HTML += '  <div class="col-md-5 col-sm-5 col-xs-12">';
                            HTML += '      <label>#{localemsgs.Valor}</label>';
                            HTML += '      <input id="txtVolValor" type="tel" class="form-control" south-type="monetario" south-required="S" />';
                            HTML += '  </div>';
                            HTML += '   <div class="col-md-7 col-sm-7 col-xs-12">';
                            HTML += '      <label>#{localemsgs.Moeda}</label>';
                            HTML += '      <select id="cboMoeda" class="form-control" south-required="S">';
                            HTML += '         <option value="-1">#{localemsgs.Selecione}</option>';
                            HTML += '         <option value="BRL" descricao="BRL">(BRL) #{localemsgs.MoedaReal}</option>';
                            HTML += '         <option value="USD" descricao="USD">(US$) #{localemsgs.MoedaDolar}</option>';
                            HTML += '         <option value="MXN" descricao="MXN">(MXN) #{localemsgs.MoedaMexico}</option>';
                            HTML += '         <option value="EUR" descricao="EUR">(EUR) #{localemsgs.MoedaEuro}</option>';
                            HTML += '         <option value="GBP" descricao="GBP">(GBP) #{localemsgs.MoedaBritaniva}</option>';
                            HTML += '         <option value="EUR" descricao="CLP">(CLP) #{localemsgs.MoedaPesoChileno}</option>';
                            HTML += '         <option value="EUR" descricao="COB">(COB) #{localemsgs.MoedaPesoColombiano}</option>';
                            HTML += '      </select>';
                            HTML += '   </div>';
                            HTML += '</div>';

                            $.ModalDialogCallBack('large',
                                    'fa fa-plus fa-lg',
                                    '#{localemsgs.Guia}',
                                    HTML,
                                    'green',
                                    'fa fa-save',
                                    '#{localemsgs.Salve}',
                                    '#{localemsgs.Cancelar}',
                                    function () {
                                        if ($.ValidaForm($('#divFormBaixa'), true, '#{localemsgs.Aviso}', '#{localemsgs.PrenchaCamposObrigaatorios}')) {
                                            let HTML = '';

                                            HTML += '<div class="col-md-3 col-sm-3 col-xs-7 Guia">';
                                            HTML += '  <i class="fa fa-minus-circle ExcluirGuia" title="#{localemsgs.ExcluirGuia}"></i>';
                                            HTML += '  <label class="TituloGuia">' + $('#txtVolGuia').val() + '-' + $('#txtVolSerie').val() + '</label>';
                                            HTML += '  <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; border-bottom: thin dashed #82cc8c !important;">';
                                            HTML += '      <div class="col-md-12 col-sm-12 col-xs-12" style="padding:1px !important">';
                                            HTML += '          <label class="Titulo">#{localemsgs.Valor}</label>';
                                            HTML += '          <label class="Valor" ref="ValorTotal"></label>';
                                            HTML += '      </div>';
                                            HTML += '  </div>';
                                            HTML += '  <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important;">';
                                            HTML += '      <div class="col-md-5 col-sm-5 col-xs-5" style="padding:1px !important">';
                                            HTML += '          <label class="Titulo">#{localemsgs.Moeda}</label>';
                                            HTML += '          <label class="Valor" ref="TipoMoeda">' + $('#cboMoeda').val() + '</label>';
                                            HTML += '      </div>';
                                            HTML += '      <div class="col-md-7 col-sm-7 col-xs-7" style="padding:1px !important">';
                                            HTML += '          <label class="Titulo">#{localemsgs.RPV}</label>';
                                            HTML += '          <label class="Valor" ref="Rpv">' + $('#cboRPV').val() + '</label>';
                                            HTML += '      </div>';
                                            HTML += '  </div>';
                                            HTML += '  <div class="col-md-12 col-sm-12 col-xs-12 FundoVolumes" style="padding:1px 2px 1px 2px !important;">';
                                            HTML += '      <label class="btAddVolumes"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;#{localemsgs.Volumes}</label>';
                                            HTML += '      <div ref="List" class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; overflow: auto !important; height: 57px; position:relative; margin-top:-2px !important;"></div>';
                                            HTML += '  </div>';
                                            HTML += '</div>';

                                            $('#divGuiasVolumes').append(HTML);
                                            CalcularTotalGuia($('.Guia:last-child'));
                                            $('.Guia:last-child').find('[ref="ValorTotal"]').text($('#txtVolValor').val());
                                            $JanelaForm.close();
                                        }
                                    },
                                    function () {
                                        $('#cboRPV').change();
                                        $('#txtVolGuia').focus();
                                    },
                                    '#{localeController.number}');
                        })
                        .on('change', '#cboRPV', function () {
                            $('#txtVolGuia, #txtVolSerie').val('');

                            if ($(this).val() === '1') {
                                $('#txtVolGuia').val(GerarGuiaAutomatica()).attr('disabled', 'disabled');
                                $('#txtVolSerie').val('53').attr('disabled', 'disabled');
                            } else
                                $('#txtVolGuia, #txtVolSerie').removeAttr('disabled');
                        })
                        ;
                // ]]>
            </script>

            <script type="text/javascript">
                // <![CDATA[
                var tmrReloadMap;

                function esc() {
                    if (PF('dlgOk').isVisible()) {
                        PF('dlgOk').hide();
                    } else if (PF('dlgUpload').isVisible()) {
                        PF('dlgUpload').hide();
                    } else if (PF('dlgImprimir').isVisible()) {
                        PF('dlgImprimir').hide();
                    } else {
                        window.history.back();
                    }
                }
                ;
                function chartExtender() {
                    Chart.defaults.global.tooltips.callbacks.afterLabel = function (tooltipItem, data) {
                        return '(' + data['datasets'][0]['data'][tooltipItem['index']] + '%)';
                    };
                    Chart.defaults.global.tooltips.callbacks.label = function (tooltipItem, data) {
                        return '(' + data['datasets'][0]['data'][tooltipItem['index']] + '%)';
                    };
                }

                function AbrirJanelaMapa(CodigoSequencia) {
                    if (CodigoSequencia != 'X') {
                        clearInterval(tmrReloadMap);

                        if (!CodigoSequencia)
                            CodigoSequencia = '0';

                        $('[id*="txtSequencia"]').val(CodigoSequencia);
                        $('[id*="btOcultoMapas"]').click();
                    }
                }

                function LancamentoGuias(statusPedidos,
                        sequencia,
                        parada,
                        entregaRecolhimento,
                        hrCheg) {

                    if (entregaRecolhimento == 'E') {
                        $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.ParadaEntrega}');
                    } else {
                        if (entregaRecolhimento == 'R' &&
                                (statusPedidos == 'P' ||
                                        null == hrCheg ||
                                        hrCheg == '' ||
                                        hrCheg == ':' ||
                                        hrCheg == '  :  ' ||
                                        hrCheg == ' : ' ||
                                        ReplaceAll(hrCheg, ' ', '') == ':')) {
                            editarGuia = true;
                        } else {
                            editarGuia = false
                        }

                        rcConsultarGuias([{name: 'seqRota', value: sequencia}, {name: 'parada', value: parada}]);
                        //AbrirFormAddGuias(sequencia, parada);
                    }
                }

                function PreReloadMap() {
                    $('#spnTimer').text('30').parent('label').css('display', 'block');

                    tmrReloadMap = setInterval(function () {
                        ReloadMap();
                    }, 1000);
                }

                function ReloadMap() {
                    if ($('#spnTimer').text() === '0') {
                        clearInterval(tmrReloadMap);
                        AbrirJanelaMapa($('[id*="txtSequencia"]').val());
                    } else {
                        $('#spnTimer').text(eval($('#spnTimer').text()) - 1);
                    }
                }

                function responsividade() {
                    if ($('body').width() <= 640) {
                        $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                        //  $('.ui-tabs-panel[id*="mainTabView"]').css('min-height', ($('body').height() - 240) + 'px').css('height', ($('body').height() - 240) + 'px').css('max-height', ($('body').height() - 240) + 'px');
                    } else {
                        $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                        //   $('.ui-tabs-panel[id*="mainTabView"]').css('min-height', ($('body').height() - 150) + 'px').css('height', ($('body').height() - 150) + 'px').css('max-height', ($('body').height() - 150) + 'px');
                    }
                }

                $(document).ready(function () {
                    setInterval(function () {
                        responsividade();
                    }, 20);
                });

                $(window).resize(function () {
                    if ($('div[south-type="js-confirm-content"]').length > 0)
                        $('div[south-type="js-confirm-content"]').each(function () {
                            $(this).css('height', eval(($(document).height() - eval($(this).attr('ref-size')))) + 'px')
                        });
                });
                // ]]>
            </script>

            <h:form>
                <p:hotkey bind="left" handler="esc();" />
            </h:form>

            <f:metadata>
                <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
                <f:viewAction action="#{guias.Persistencias(login.pp,login.satellite)}" />
            </f:metadata>
            <p:growl id="msgs" />
            <header>
                <h:form id="cabecalho">
                    <div class="ui-grid ui-grid-responsive">
                        <div class="ui-grid-row cabecalho">
                            <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                <img src="../assets/img/icone_satmob_guias_40x40.png" height="40" width="40"/> 
                                <label class="TituloPagina">#{localemsgs.Guias}</label>
                                <label class="TituloDataHora">
                                    <h:outputText value="#{localemsgs.Periodo}: "/>
                                    <span>
                                        <h:outputText value="#{guias.data1}" converter="conversorData" />
                                        <h:outputText value=" - "/>
                                        <h:outputText value="#{guias.data2}" converter="conversorData"/></span>
                                </label>
                            </div>

                            <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-5" style="text-align: center !important; display: flex; align-items: center; justify-content: center;">
                                <!-- Logo do Cliente -->
                                <div style="margin-right: 15px;">
                                    <img src="#{login.getLogo(login.empresa.bancoDados, guias.codFil)}"
                                         alt="Logo #{login.empresa.bancoDados}"
                                         style="max-height: 60px; max-width: 80px; object-fit: contain;"
                                         onerror="this.src='assets/img/logo_satweb.png'"/>
                                </div>

                                <!-- Informações da Filial -->
                                <div style="text-align: left;">
                                    <label class="FilialNome" style="display:#{guias.filiais.descricao == '' || guias.filiais.descricao == null?'none':'block'}">#{guias.filiais.descricao}<label id="btTrocarFilial" style="display:#{guias.voltar ne 'login.xhtml'?'':'none'}">Trocar Filial</label></label>
                                    <label class="FilialEndereco" style="display:#{guias.filiais.descricao == '' || guias.filiais.descricao == null?'none':'block'}">#{guias.filiais.endereco}</label>
                                    <label class="FilialBairroCidade" style="display:#{guias.filiais.descricao == '' || guias.filiais.descricao == null?'none':'block'}">#{guias.filiais.bairro}, #{guias.filiais.cidade}/#{guias.filiais.UF}</label>
                                </div>
                            </div>

                            <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-7" >
                                <p:commandLink action="#{guias.dataAnterior}"  update="main cabecalho totais">
                                    <p:graphicImage url="../assets/img/botao_anterior.png" />
                                </p:commandLink>

                                <p:datePicker id="range" selectionMode="range" readonlyInput="true" 
                                              value="#{guias.datasSelecionadas}"
                                              monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                              pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                              converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                    <p:ajax event="dateSelect" listener="#{guias.selecionarDatas}" update="msgs" />
                                </p:datePicker>

                                <p:commandLink action="#{guias.dataPosterior}" update="main msgs cabecalho totais">
                                    <p:graphicImage url="../assets/img/botao_proximo.png"/>
                                </p:commandLink>
                            </div>

                            <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                <p:commandLink title="#{localemsgs.Voltar}"
                                               onclick="window.history.back();" action="#" rendered="#{guias.voltar ne 'login.xhtml'}">
                                    <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                </p:commandLink>

                                <p:commandLink title="#{localemsgs.Voltar}" oncomplete="PF('dlgOk').show();"
                                               rendered="#{guias.voltar eq 'login.xhtml'}">
                                    <p:graphicImage url="../assets/img/icone_sair.png" height="40"/>
                                </p:commandLink>
                            </div>
                        </div>
                    </div>
                </h:form>
            </header>

            <h:form id="main">
                <p:remoteCommand name="rcConsultarGuias" partialSubmit="true" 
                                 process="@this" 
                                 update="msgs" 
                                 actionListener="#{guias.consultarGuiasPortal}" />  

                <h:inputHidden id="txtSequencia" value="#{valores.sequencia}" ></h:inputHidden>

                <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; overflow-y:auto !important; padding-right:12px !important;">
                    <div class="ui-grid-row">
                        <div class="ui-grid-col-12 divFundoGrid" style="overflow:hidden !important; position:relative; height:100% !important">
                            <p:panel style="display: inline">
                                <p:tabView id="mainTabView" dynamic="true" cache="true">
                                    <p:ajax event="tabChange" listener="#{guias.onTabChange}" update="msgs main:mainTabView"/>
                                    <p:tab title="#{localemsgs.Guias}" id="guias">

                                        <p:dataTable id="tabela" value="#{guias.allGuias}" paginator="true" rows="50" lazy="true"
                                                     rowsPerPageTemplate="5,10,15, 20, 25, 50, 100, 200, 1000"
                                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.GTV}"
                                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                     var="guiacliente"
                                                     styleClass="tabela DataGrid" 
                                                     emptyMessage="#{localemsgs.SemRegistros}"
                                                     selectionMode="single" 
                                                     selection="#{guias.guiaSelecionada}"
                                                     scrollable="true"
                                                     scrollHeight="100%" 
                                                     reflow="true"
                                                     style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;max-width:100% !important; height: calc(100vh - 195px) !important; width:100% !important;">
                                            <p:ajax event="rowDblselect" listener="#{guias.imprimir}" update="impressao msgs"/>   
                                            <p:column headerText="#{localemsgs.StatusGuia}" styleClass="#{guiacliente.statusGuia eq 'P'? 'Pendente':'Finalizado'}">
                                                <label onclick="LancamentoGuias('#{guiacliente.statusGuia}', '#{guiacliente.sequencia}', '#{guiacliente.parada}', '#{guiacliente.ER}', '#{guiacliente.hrCheg}')" style="position:relative; width: 100% !important; height: 25px !important; margin-top: 5px !important; padding-right: 18px !Important; #{guiacliente.statusGuia eq 'P'? 'color: red !important': ''}">
                                                    <i class="#{guiacliente.statusGuia eq 'P'? 'fa fa-warning' : 'fa fa-check-square-o'}" style="position:absolute; left:8px; font-size:14pt !important;"></i> #{guiacliente.statusGuia eq 'P'? localemsgs.Pendente: localemsgs.Emitida}
                                                </label>
                                                <label class="DisponivelEmissao" onclick="LancamentoGuias('#{guiacliente.statusGuia}', '#{guiacliente.sequencia}', '#{guiacliente.parada}', '#{guiacliente.ER}', '#{guiacliente.hrCheg}')" style="position:relative;text-align: center !important; font-weight: bold !Important; padding: 2px !important; display: #{guiacliente.statusGuia eq 'P' and guiacliente.ER eq 'R'? 'block': 'none'} ">
                                                    #{localemsgs.EmissaoDisponivel}
                                                </label>
                                                <label class="DisponivelEdicao" onclick="LancamentoGuias('#{guiacliente.statusGuia}', '#{guiacliente.sequencia}', '#{guiacliente.parada}', '#{guiacliente.ER}', '#{guiacliente.hrCheg}')" style="position:relative;text-align: center !important; min-width: 100% !important;font-weight: bold !Important; padding: 2px !important; display: #{guiacliente.statusGuia ne 'P' and guiacliente.ER eq 'R' and (guiacliente.hrCheg eq null or guiacliente.hrCheg eq '')? 'block': 'none'} ">
                                                    #{localemsgs.EdicaoDisponivel}
                                                </label>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Status} #{localemsgs.Servico}" styleClass="#{guiacliente.classCelula}" style="display: none !important">
                                                <label onclick="AbrirJanelaMapa(#{guiacliente.status == 'T' ? guiacliente.sequencia: 'X'})" style="position:relative">
                                                    <i class="#{guiacliente.classIcon}" style="position:absolute; left:8px; font-size:14pt !important; margin-top:2px;">
                                                    </i>#{guiacliente.status == "F" ? localemsgs.Finalizado : guiacliente.status == "P" ? localemsgs.Pendente : localemsgs.EmTransito}
                                                </label>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.OrigemColeta}">
                                                <h:outputText value="#{guiacliente.origem}" title="#{guiacliente.origem}"/>
                                            </p:column>             
                                            <p:column headerText="#{localemsgs.DestinoEntrega}">
                                                <h:outputText value="#{guiacliente.destino}" title="#{guiacliente.destino}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Data}">
                                                <h:outputText value="#{guiacliente.dtEntrega}" title="#{guiacliente.dtEntrega}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Guia}">
                                                <h:outputText value="#{guiacliente.guia}" title="#{guiacliente.guia}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Serie}">
                                                <h:outputText value="#{guiacliente.serie}" title="#{guiacliente.serie}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Pedido}">
                                                <h:outputText value="#{guiacliente.statusGuia}" title="#{guiacliente.statusGuia}"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Valor}">
                                                <h:outputText value="#{guiacliente.valor}"
                                                              title="#{guiacliente.valor}" converter="conversormoeda"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Rota}">
                                                <h:outputText value="#{guiacliente.rota}" title="#{guiacliente.rota}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{!login.transpCacamba ? localemsgs.Vol : localemsgs.Containers}"
                                                >
                                                <h:outputText value="#{guiacliente.vol}"
                                                              title="#{guiacliente.vol}"/>
                                            </p:column>
                                            <p:column 
                                                headerText="#{!login.transpCacamba ? localemsgs.Lacre : localemsgs.Container}"
                                                >
                                                <h:outputText value="#{guiacliente.lacre}"
                                                              title="#{guiacliente.lacre}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Operacao}">
                                                <h:outputText value="#{guiacliente.operacao}" title="#{guiacliente.operacao}" converter="tradutor"/>
                                            </p:column>                                         
                                            <p:column headerText="#{localemsgs.DtColeta}">
                                                <h:outputText value="#{guiacliente.dtColeta}" title="#{guiacliente.dtColeta}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.HrColeta}">
                                                <h:outputText value="#{guiacliente.hrCheg}" title="#{guiacliente.hrCheg}" converter="conversorHora"/>
                                                <h:outputText value=" - "/>
                                                <h:outputText value="#{guiacliente.hrSaida}" title="#{guiacliente.hrSaida}" converter="conversorHora"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.SB}">
                                                <h:outputText value="#{guiacliente.agSB}" title="#{guiacliente.agSB}"/>
                                            </p:column>


                                            <p:column headerText="#{localemsgs.SBDst}">
                                                <h:outputText value="#{guiacliente.agSBD}" 
                                                              title="#{guiacliente.agSBD}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.HrPrg}">
                                                <h:outputText value="#{guiacliente.hrPrg}" title="#{guiacliente.hrPrg}" converter="conversorHora"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.HrCheg}">
                                                <h:outputText value="#{guiacliente.hrCheg}" title="#{guiacliente.hrCheg}" converter="conversorHora"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.GeradoPor}">
                                                <h:outputText value="#{guiacliente.operador}" title="#{guiacliente.operador}"/>
                                            </p:column>
                                        </p:dataTable>

                                    </p:tab>
                                    <p:tab title="#{localemsgs.Graficos}" id="graficos">
                                        <p:panel id="panelGraficos" style="overflow: auto;">
                                            <p:pieChart id="aliviosNaDataGrafico" model="#{guias.aliviosNaDataGrafico}" style="width: 100%; height: 500px !important;"/>
                                            <p:pieChart id="reforcosNaDataGrafico" model="#{guias.reforcosNaDataGrafico}" style="width: 100%; height: 400px !important;"/>
                                        </p:panel>
                                    </p:tab>
                                    <p:tab title="#{localemsgs.Pedidos}" id="pedidos">
                                        <p:dataTable id="tabelaP" value="#{guias.pedidos}" selection="#{pedido.pedidoSelecionado}" paginator="true" rows="15" lazy="true" 
                                                     reflow="true" rowsPerPageTemplate="5, 10, 15, 20, 25" rendered="#{!login.pp.empresa.contains('CONFEDERAL')}"
                                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Pedidos}"
                                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                     var="lista" styleClass="tabela" selectionMode="single" emptyMessage="#{localemsgs.SemRegistros}" scrollable="true" 
                                                     class="tabela DataGrid" scrollWidth="100%" 
                                                     style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important;
                                                     max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                                     rowStyleClass="#{lista.flag_Excl eq '*' ? 'ciano' 
                                                                      : lista.azul ? ' azul'
                                                                      : lista.situacao eq 'PD' ? 'inativo' 
                                                                      : lista.situacao eq 'OK' ? 'verde' 
                                                                      : null}">
                                            <p:column headerText="#{localemsgs.Numero}" >
                                                <h:outputText value="#{lista.numero}" title="#{lista.numero}" >
                                                    <f:convertNumber pattern="0000" />
                                                </h:outputText>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.CodFil}" >
                                                <h:outputText value="#{lista.codFil}" title="#{lista.codFil}" >
                                                    <f:convertNumber pattern="0000" />
                                                </h:outputText>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Data}" >
                                                <h:outputText value="#{lista.data}" title="#{lista.data}" converter="conversorData" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Tipo}" >
                                                <h:outputText value="#{lista.tipo}" title="#{lista.tipo}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.classificacao} " >
                                                <h:outputText value="#{lista.classifSrv}" title="#{lista.classifSrv}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Origem}" >
                                                <h:outputText value="#{lista.NRed1}" title="#{lista.NRed1}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hora1O}" >
                                                <h:outputText value="#{lista.hora1O}" converter="conversorHora" title="#{lista.hora1O}" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hora2O}" >
                                                <h:outputText value="#{lista.hora2O}" converter="conversorHora" title="#{lista.hora2O}" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Regiao1}" >
                                                <h:outputText value="#{lista.regiao1}" title="#{lista.regiao1}" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Destino}" >
                                                <h:outputText value="#{lista.NRed2}" title="#{lista.NRed2}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Regiao2}" >
                                                <h:outputText value="#{lista.regiao2}" title="#{lista.regiao2}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hora1D}" >
                                                <h:outputText value="#{lista.hora1D}" converter="conversorHora" title="#{lista.hora1D}" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hora2D}" >
                                                <h:outputText value="#{lista.hora2D}" converter="conversorHora" title="#{lista.hora2D}" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Solicitante}" >
                                                <h:outputText value="#{lista.solicitante}" title="#{lista.solicitante}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.PedidoCliente}" >
                                                <h:outputText value="#{lista.pedidoCliente}" title="#{lista.pedidoCliente}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Valor}" >
                                                <h:outputText value="#{lista.valor}" title="#{lista.valor}" converter="conversormoeda"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Obs}" >
                                                <h:outputText value="#{lista.obs}" title="#{lista.obs}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.OS}" >
                                                <h:outputText value="#{lista.OS}" title="#{lista.OS}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Guia}" >
                                                <h:outputText value="#{lista.OS}" title="#{lista.OS}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.QtdeGTV}" >
                                                <h:outputText value="#{lista.OS}" title="#{lista.OS}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.OperIncl} " >
                                                <h:outputText value="#{lista.operIncl}" title="#{lista.operIncl}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Incl}" >
                                                <h:outputText value="#{lista.dt_Incl}" title="#{lista.dt_Incl}" converter="conversorData" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hr_Incl}" >
                                                <h:outputText value="#{lista.hr_Incl}" title="#{lista.dt_Incl}" converter="conversorHora" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Operador} " >
                                                <h:outputText value="#{lista.operador}" title="#{lista.operador}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Alter}" >
                                                <h:outputText value="#{lista.dt_Alter}" title="#{lista.dt_Alter}" converter="conversorData" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hr_Alter}" >
                                                <h:outputText value="#{lista.hr_Alter}" title="#{lista.hr_Alter}" converter="conversorHora" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Situacao} " >
                                                <h:outputText value="#{lista.situacao}" title="#{lista.situacao}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.SeqRota} " >
                                                <h:outputText value="#{lista.seqRota}" title="#{lista.seqRota}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Parada} " >
                                                <h:outputText value="#{lista.parada}" title="#{lista.parada}"/>
                                            </p:column>
                                        </p:dataTable>

                                        <p:dataTable id="tabelaPedido" value="#{guias.allPedidos}" paginator="true" rows="50" lazy="true"
                                                     rowsPerPageTemplate="5,10,15, 20, 25, 50, 100, 200, 1000"
                                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Pedidos}"
                                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                     var="preOrder" rendered="#{login.pp.empresa.contains('CONFEDERAL')}"
                                                     selectionMode="single" 
                                                     selection="#{guias.preOrderSelecionado}"
                                                     styleClass="tabela DataGrid" 
                                                     emptyMessage="#{localemsgs.SemRegistros}"
                                                     scrollable="true"
                                                     scrollHeight="100%" 
                                                     reflow="true"
                                                     style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; 
                                                     max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                                            <p:ajax event="rowDblselect" listener="#{guias.imprimir}" update="impressao msgs"/>   
                                            <p:column style="width:20px">
                                                <p:rowToggler />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Remessa}">
                                                <h:outputText value="#{preOrder.pedidoCliente}" title="#{preOrder.pedidoCliente}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.PreOrderVolOrdem}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.preOrderVolOrdem}" title="#{preOrder.preOrderVolOrdem}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Lacre}" style="width: 55px" rendered="false">
                                                <h:outputText value="#{preOrder.preOrderVolLacre}" title="#{preOrder.preOrderVolLacre}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Qtde}" style="width: 40px" class="celula-right" rendered="false">
                                                <h:outputText value="#{preOrder.preOrderVolQtde}" title="#{preOrder.preOrderVolQtde}" converter="conversor0"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.PreOrderVolTipo}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.preOrderVolTipo}" title="#{preOrder.preOrderVolTipo}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.PreOrderVolValor}" style="width: 80px" class="celula-right" rendered="false">
                                                <h:outputText value="#{preOrder.preOrderVolValor}" title="#{preOrder.preOrderVolValor}" converter="conversormoeda"/>
                                            </p:column>                                    
                                            <p:column headerText="#{localemsgs.Sequencia}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.sequencia}" title="#{preOrder.sequencia}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.CodFil}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.codFil}" title="#{preOrder.codFil}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Banco}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.banco}" title="#{preOrder.banco}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.DtColeta}" style="width: 75px">
                                                <h:outputText value="#{preOrder.dtColeta}" title="#{preOrder.dtColeta}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.CodCli1}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.codCli1}" title="#{preOrder.codCli1}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.SB}">
                                                <h:outputText value="#{preOrder.agenciaOri}/#{preOrder.subAgenciaOri}" 
                                                              title="#{preOrder.agenciaOri}/#{preOrder.subAgenciaOri}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.OrigemColeta}">
                                                <h:outputText value="#{preOrder.NRed1}" title="#{preOrder.NRed1}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.HrColeta}">
                                                <h:outputText value="#{preOrder.hora1O}" title="#{preOrder.hora1O}" converter="conversorHora"/>
                                                <h:outputText value=" - " rendered="#{preOrder.hora1O ne ''}"/>
                                                <h:outputText value="#{preOrder.hora2O}" title="#{preOrder.hora2O}" converter="conversorHora"/>
                                            </p:column>  
                                            <p:column headerText="#{localemsgs.CodCli2}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.codCli2}" title="#{preOrder.codCli2}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.SBDst}">
                                                <h:outputText value="#{preOrder.agencia}/#{preOrder.subAgencia}"
                                                              title="#{preOrder.agencia}/#{preOrder.subAgencia}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.DestinoEntrega}">
                                                <h:outputText value="#{preOrder.NRed2}" title="#{preOrder.NRed2}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.DtEntrega}">
                                                <h:outputText value="#{preOrder.dtEntrega}" title="#{preOrder.dtEntrega}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.HoraEntrega}">
                                                <h:outputText value="#{preOrder.hora1D}" title="#{preOrder.hora1D}" converter="conversorHora"/>
                                                <h:outputText value=" - " rendered="#{preOrder.hora1D ne ''}"/>
                                                <h:outputText value="#{preOrder.hora2D}" title="#{preOrder.hora2D}" converter="conversorHora"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Solicitante}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.solicitante}" title="#{preOrder.solicitante}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Pedido}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.pedido}" title="#{preOrder.pedido}" converter="conversor0"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Valor}" class="celula-right">
                                                <h:outputText value="#{preOrder.valor}" title="#{preOrder.valor}" converter="conversormoeda"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Obs}" style="width: 300px" rendered="false">
                                                <h:outputText value="#{preOrder.obs}" title="#{preOrder.obs}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.RPV}" style="width: 90px" rendered="false">
                                                <h:outputText value="#{preOrder.RPV}" title="#{preOrder.RPV}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Guia}" style="width: 70px" rendered="false">
                                                <h:outputText value="#{preOrder.guia}" title="#{preOrder.guia}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Serie}" style="width: 40px" rendered="false">
                                                <h:outputText value="#{preOrder.serie}" title="#{preOrder.serie}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ClassifSrv}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.classifSrv}" title="#{preOrder.classifSrv}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.OperIncl}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.operIncl}" title="#{preOrder.operIncl}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Incl}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.dt_Incl}" title="#{preOrder.dt_Incl}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hr_Incl}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.hr_Incl}" title="#{preOrder.hr_Incl}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.OS}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.OS}" title="#{preOrder.OS}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ChequesQtde}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.chequesQtde}" title="#{preOrder.chequesQtde}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ChequesValor}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.chequesValor}" title="#{preOrder.chequesValor}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Operador}">
                                                <h:outputText value="#{preOrder.operador}" title="#{preOrder.operador}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Alter}">
                                                <h:outputText value="#{preOrder.dt_Alter}" title="#{preOrder.dt_Alter}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hr_Alter}">
                                                <h:outputText value="#{preOrder.hr_Alter}" title="#{preOrder.hr_Alter}" converter="conversorHora"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.OperExcl}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.operExcl}" title="#{preOrder.operExcl}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Excl}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.dt_Excl}" title="#{preOrder.dt_Excl}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hr_Excl}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.hr_Excl}" title="#{preOrder.hr_Excl}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Situacao}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.situacao}" title="#{preOrder.situacao}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Flag_Excl}" style="width: 80px" rendered="false">
                                                <h:outputText value="#{preOrder.flag_Excl}" title="#{preOrder.flag_Excl}"/>
                                            </p:column>
                                            <p:ajax event="rowToggle" id="rowToggle" listener="#{guias.exibirDetalhesPreOrder}" update="msgs"/>
                                            <p:rowExpansion id="tabelaPreOrderDetalhes">
                                                <p:dataTable id="tabelaPreOrderDetalhado" 
                                                             value="#{guias.preOrderDetalhado}" 
                                                             var="preOrderDetalhado" 
                                                             rowKey="#{preOrderDetalhado.sequencia}"
                                                             selection="#{guias.preOrderSelecionadoDetalhado}" 
                                                             selectionMode="single"
                                                             styleClass="tabela DataGrid" 
                                                             scrollable="true"
                                                             scrollHeight="100%"
                                                             emptyMessage="#{localemsgs.SemRegistros}" 
                                                             style="font-size: 12px; background: transparent;  padding:0px !important; margin:0px !important;max-width:100% !important; width:100% !important;">   
                                                    <p:ajax event="rowDblselect" listener="#{guias.preparaEdicaoLacre}" update="msgs"/>  

                                                    <p:column headerText="#{localemsgs.Remessa}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.pedidoCliente}" title="#{preOrderDetalhado.pedidoCliente}" />
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Lacre}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.preOrderVolLacre}" title="#{preOrderDetalhado.preOrderVolLacre}"  />
                                                    </p:column>                             
                                                    <p:column headerText="#{localemsgs.Sequencia}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.sequencia}" title="#{preOrderDetalhado.sequencia}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.CodFil}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.codFil}" title="#{preOrderDetalhado.codFil}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Banco}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.banco}" title="#{preOrderDetalhado.banco}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.DtColeta}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.dtColeta}" title="#{preOrderDetalhado.dtColeta}" converter="conversorData"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.CodCli1}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.codCli1}" title="#{preOrderDetalhado.codCli1}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.SB}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.agenciaOri}/#{preOrderDetalhado.subAgenciaOri}" 
                                                                      title="#{preOrderDetalhado.agenciaOri}/#{preOrderDetalhado.subAgenciaOri}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.OrigemColeta}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.NRed1}" title="#{preOrderDetalhado.NRed1}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.HrColeta}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.hora1O}" title="#{preOrderDetalhado.hora1O}" converter="conversorHora"/>
                                                        <h:outputText value=" - " rendered="#{preOrderDetalhado.hora1O ne ''}"/>
                                                        <h:outputText value="#{preOrderDetalhado.hora2O}" title="#{preOrderDetalhado.hora2O}" converter="conversorHora"/>
                                                    </p:column>  
                                                    <p:column headerText="#{localemsgs.CodCli2}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.codCli2}" title="#{preOrderDetalhado.codCli2}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.SBDst}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.agencia}/#{preOrderDetalhado.subAgencia}"
                                                                      title="#{preOrderDetalhado.agencia}/#{preOrderDetalhado.subAgencia}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.DestinoEntrega}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.NRed2}" title="#{preOrderDetalhado.NRed2}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.DtEntrega}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.dtEntrega}" title="#{preOrderDetalhado.dtEntrega}" converter="conversorData"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.HoraEntrega}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.hora1D}" title="#{preOrderDetalhado.hora1D}" converter="conversorHora"/>
                                                        <h:outputText value=" - " rendered="#{preOrderDetalhado.hora1D ne ''}"/>
                                                        <h:outputText value="#{preOrderDetalhado.hora2D}" title="#{preOrderDetalhado.hora2D}" converter="conversorHora"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Solicitante}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.solicitante}" title="#{preOrderDetalhado.solicitante}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Pedido}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.pedido}" title="#{preOrderDetalhado.pedido}" converter="conversor0"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Valor}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.valor}" title="#{preOrderDetalhado.valor}" converter="conversormoeda"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Obs}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.obs}" title="#{preOrderDetalhado.obs}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.RPV}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.RPV}" title="#{preOrderDetalhado.RPV}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Guia}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.guia}" title="#{preOrderDetalhado.guia}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Serie}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.serie}" title="#{preOrderDetalhado.serie}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.ClassifSrv}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.classifSrv}" title="#{preOrderDetalhado.classifSrv}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.OperIncl}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.operIncl}" title="#{preOrderDetalhado.operIncl}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Dt_Incl}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.dt_Incl}" title="#{preOrderDetalhado.dt_Incl}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Hr_Incl}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.hr_Incl}" title="#{preOrderDetalhado.hr_Incl}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.OS}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.OS}" title="#{preOrderDetalhado.OS}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.ChequesQtde}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.chequesQtde}" title="#{preOrderDetalhado.chequesQtde}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.ChequesValor}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.chequesValor}" title="#{preOrderDetalhado.chequesValor}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Operador}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.operador}" title="#{preOrderDetalhado.operador}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Dt_Alter}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.dt_Alter}" title="#{preOrderDetalhado.dt_Alter}" converter="conversorData"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Hr_Alter}" style="text-transform: capitalize !important;">
                                                        <h:outputText value="#{preOrderDetalhado.hr_Alter}" title="#{preOrderDetalhado.hr_Alter}" converter="conversorHora"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.OperExcl}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.operExcl}" title="#{preOrderDetalhado.operExcl}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Dt_Excl}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.dt_Excl}" title="#{preOrderDetalhado.dt_Excl}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Hr_Excl}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.hr_Excl}" title="#{preOrderDetalhado.hr_Excl}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Situacao}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.situacao}" title="#{preOrderDetalhado.situacao}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Flag_Excl}" style="width: 80px" rendered="false">
                                                        <h:outputText value="#{preOrderDetalhado.flag_Excl}" title="#{preOrderDetalhado.flag_Excl}"/>
                                                    </p:column>
                                                </p:dataTable>
                                            </p:rowExpansion>
                                        </p:dataTable>
                                    </p:tab>
                                    <p:tab title="#{localemsgs.PreFatura}" id="faturamento" rendered="#{!login.pp.empresa.contains('CONFEDERAL')}">
                                        <div style="width:100% !important; height:calc(100vh - 152px) !important; overflow:hidden !important; padding:0px !important">
                                            <iframe id="ifrPreFatura" src="../relatorio/prefatura.xhtml?faces-redirect=true&amp;portal=S" style="width:100%; height:100%; padding:0px; border:none"></iframe>
                                        </div>
                                        <script type="text/javascript">
                                            // <![CDATA[
                                            $(document).ready(function () {
                                                setInterval(function () {
                                                    $obj = $('#ifrPreFatura').contents();
                                                    $obj.find('#divDadosFilial, #divBotaoVoltar, footer').css('display', 'none');
                                                    $obj.find('#divCalendario').css({
                                                        'position': 'absolute',
                                                        'top': '-2px',
                                                        'right': '20px'
                                                    });

                                                    $obj.find('header').css({
                                                        'margin-top': '6px',
                                                        'border': 'none',
                                                        'box-shadow': '2px 2px 3px #CCC'
                                                    });
                                                }, 10);
                                            });
                                            // ]]>
                                        </script>
                                    </p:tab>
                                    <p:tab title="#{localemsgs.PreFaturaCliente}" id="faturamentoCliFat" rendered="#{!login.pp.empresa.contains('CONFEDERAL')}">
                                        <div style="width:100% !important; height:calc(100vh - 149px) !important; overflow:hidden !important; padding:0px !important">
                                            <iframe id="ifrPreFaturaCliFat" src="../relatorio/prefaturaclifat.xhtml?faces-redirect=true&amp;portal=S" style="width:100%; height:100%; padding:0px; border:none"></iframe>
                                        </div>
                                        <script type="text/javascript">
                                            // <![CDATA[
                                            $(document).ready(function () {
                                                setInterval(function () {
                                                    $obj = $('#ifrPreFaturaCliFat').contents();
                                                    $obj.find('#divDadosFilial, #divBotaoVoltar, footer').css('display', 'none');
                                                    $obj.find('#divCalendario').css({
                                                        'position': 'absolute',
                                                        'top': '-2px',
                                                        'right': '20px'
                                                    });

                                                    $obj.find('header').css({
                                                        'margin-top': '6px',
                                                        'border': 'none',
                                                        'box-shadow': '2px 2px 3px #CCC'
                                                    });
                                                }, 10);
                                            });
                                            // ]]>
                                        </script>
                                    </p:tab>
                                    <p:tab title="#{localemsgs.Extrato}" id="extrato" rendered="false">

                                        <h1 style="color: red; background: yellow; padding: 20px;">TESTE - ABA EXTRATO FUNCIONANDO</h1>
                                        <h2 style="color: blue;">Registros: #{guias.extratoSimples.size()}</h2>

                                        <!-- Filtros do Extrato -->
                                        <p:panel id="panelFiltrosExtrato" style="margin-bottom: 10px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px;">
                                            <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2"
                                                         layout="grid" styleClass="ui-panelgrid-blank">

                                                <h:outputLabel for="filtroNumeroNF" value="Número NF:" style="font-weight: bold;"/>
                                                <p:inputText id="filtroNumeroNF" value="#{guias.filtroNumeroNF}"
                                                             placeholder="Digite o número da NF"
                                                             style="width: 100%"/>

                                                <h:outputLabel for="filtroPracaNF" value="Praça NF:" style="font-weight: bold;"/>
                                                <p:inputText id="filtroPracaNF" value="#{guias.filtroPracaNF}"
                                                             placeholder="Digite a praça da NF"
                                                             style="width: 100%"/>

                                                <p:commandButton value="Filtrar"
                                                                 action="#{guias.aplicarFiltrosExtrato}"
                                                                 update="tabelaExtrato msgs"
                                                                 icon="fa fa-search"
                                                                 styleClass="ui-button-info"
                                                                 style="width: 100%"/>

                                                <p:commandButton value="Limpar"
                                                                 action="#{guias.limparFiltrosExtrato}"
                                                                 update="panelFiltrosExtrato tabelaExtrato msgs"
                                                                 icon="fa fa-eraser"
                                                                 styleClass="ui-button-secondary"
                                                                 style="width: 100%"/>
                                            </p:panelGrid>

                                            <h:outputText value="Obs: Quando filtrar por NF específica, o filtro de período será desconsiderado."
                                                          style="font-style: italic; color: #6c757d; font-size: 12px; display: block; margin-top: 5px;"/>
                                        </p:panel>

                                        <!-- DEBUG: Total de registros: #{guias.extratoSimples.size()} -->
                                        <h:outputText value="DEBUG: Total de registros carregados: #{guias.extratoSimples.size()}" style="color: red; font-weight: bold; display: block; margin: 10px;"/>

                                        <p:dataTable id="tabelaExtrato" value="#{guias.extratoSimples}"
                                                     var="extrato"
                                                     emptyMessage="Nenhum registro encontrado"
                                                     style="width: 100%; border: 2px solid red;"
                                                     >
                                            <p:column headerText="NF">
                                                <h:outputText value="#{extrato.NF}"/>
                                            </p:column>

                                            <p:column headerText="Data">
                                                <h:outputText value="#{extrato.data}"/>
                                            </p:column>

                                            <p:column headerText="Total">
                                                <h:outputText value="#{extrato.total}"/>
                                            </p:column>



                                        </p:dataTable>
                                    </p:tab>

                                    <p:tab title="Extrato" id="extratoNovo" rendered="#{!login.pp.empresa.contains('CONFEDERAL')}">

                                        <!-- Hotkey para mostrar/ocultar filtros com tecla P -->
                                        <p:hotkey bind="p" actionListener="#{guias.toggleFiltrosExtrato}" oncomplete="toggleFiltrosExtratoJS()"/>

                                        <div style="height: calc(100vh - 150px); overflow: hidden; display: flex; flex-direction: column;">

                                            <!-- Filtros do Extrato -->
                                            <div style="flex-shrink: 0; margin-bottom: 10px;">

                                                <!-- Painel de filtros -->
                                                <div id="painelFiltrosExtrato" style="#{guias.mostrarFiltrosExtrato ? 'display: block;' : 'display: none;'} background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin-bottom: 10px;">
                                                    <h4 style="margin-top: 0; color: #495057;">Filtros do Extrato</h4>

                                                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                                        <div>
                                                            <label for="filtroNF" style="font-weight: bold; display: block; margin-bottom: 5px;">NF:</label>
                                                            <p:inputText id="filtroNF" value="#{guias.filtroNumeroNF}"
                                                                         placeholder="Digite o número da NF"
                                                                         style="width: 100%"/>
                                                        </div>

                                                        <div>
                                                            <label for="filtroPracaExtrato" style="font-weight: bold; display: block; margin-bottom: 5px;">Praça:</label>
                                                            <p:inputText id="filtroPracaExtrato" value="#{guias.filtroPracaNF}"
                                                                         placeholder="Digite a praça"
                                                                         style="width: 100%"/>
                                                        </div>

                                                        <div>
                                                            <label for="filtroGuia" style="font-weight: bold; display: block; margin-bottom: 5px;">Guia:</label>
                                                            <p:inputText id="filtroGuia" value="#{guias.filtroGuia}"
                                                                         placeholder="Digite a guia"
                                                                         style="width: 100%"/>
                                                        </div>

                                                        <div>
                                                            <label for="filtroSerie" style="font-weight: bold; display: block; margin-bottom: 5px;">Série:</label>
                                                            <p:inputText id="filtroSerie" value="#{guias.filtroSerie}"
                                                                         placeholder="Digite a série"
                                                                         style="width: 100%"/>
                                                        </div>
                                                    </div>

                                                    <div style="margin-bottom: 10px;">
                                                        <p:commandButton value="Filtrar"
                                                                         action="#{guias.aplicarFiltrosExtratoNovo}"
                                                                         update="tabelaExtratoNovo msgs"
                                                                         icon="fa fa-search"
                                                                         styleClass="ui-button-info"/>

                                                        <p:commandButton value="Limpar Filtros"
                                                                         action="#{guias.limparFiltrosExtratoNovo}"
                                                                         update="tabelaExtratoNovo msgs"
                                                                         icon="fa fa-eraser"
                                                                         styleClass="ui-button-secondary"
                                                                         style="margin-left: 8px;"
                                                                         oncomplete="limparFiltrosExtratoJS()"/>

                                                        <p:commandButton value="Ocultar Filtros"
                                                                         action="#{guias.toggleFiltrosExtrato}"
                                                                         icon="fa fa-times"
                                                                         styleClass="ui-button-warning"
                                                                         style="margin-left: 8px;"
                                                                         oncomplete="toggleFiltrosExtratoJS()"/>
                                                    </div>

                                                    <div>
                                                        <small style="color: #6c757d; font-style: italic;">
                                                            Obs: Quando filtrar por NF específica, o filtro de período será desconsiderado.
                                                        </small>
                                                    </div>
                                                </div>

                                                <!-- Botão para mostrar filtros (quando ocultos) -->
                                                <div id="botaoMostrarFiltros" style="#{guias.mostrarFiltrosExtrato ? 'display: none;' : 'display: block;'} margin-bottom: 10px;">
                                                    <p:commandButton value="Mostrar Filtros (Pressione P)"
                                                                     action="#{guias.toggleFiltrosExtrato}"
                                                                     icon="fa fa-filter"
                                                                     styleClass="ui-button-info"
                                                                     oncomplete="toggleFiltrosExtratoJS()"/>
                                                </div>
                                            </div>

                                            <!-- Tabela do Extrato -->
                                            <div style="flex: 1; overflow: hidden; position: relative;">
                                                <div style="width: 100%; height: 100%; overflow: auto; border: 1px solid #ddd;">
                                                    <p:dataTable id="tabelaExtratoNovo" value="#{guias.extratosFiltrados}" paginator="true" rows="15"
                                                                 rowsPerPageTemplate="5,10,15,20,25,50"
                                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} registros"
                                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                                 var="extrato" styleClass="tabela DataGrid" emptyMessage="Nenhum registro encontrado"
                                                                 scrollable="false"
                                                                 style="font-size: 12px; background: white; width: 1800px; min-width: 1800px;">

                                                    <p:column headerText="NF" style="width: 100px; min-width: 100px;">
                                                        <h:outputText value="#{extrato.NF}" title="#{extrato.NF}"/>
                                                    </p:column>

                                                    <p:column headerText="Praça" style="width: 80px; min-width: 80px;">
                                                        <h:outputText value="#{extrato.praca}" title="#{extrato.praca}"/>
                                                    </p:column>

                                                    <p:column headerText="Data" style="width: 100px; min-width: 100px;">
                                                        <h:outputText value="#{extrato.data}" title="#{extrato.data}" converter="conversorData"/>
                                                    </p:column>

                                                    <p:column headerText="Tipo Serviço" style="width: 120px; min-width: 120px;">
                                                        <h:outputText value="#{extrato.tipoSrv}" title="#{extrato.tipoSrv}"/>
                                                    </p:column>

                                                    <p:column headerText="Guia" style="width: 120px; min-width: 120px;">
                                                        <h:outputText value="#{extrato.guia}" title="#{extrato.guia}"/>
                                                    </p:column>

                                                    <p:column headerText="Série" style="width: 80px; min-width: 80px;">
                                                        <h:outputText value="#{extrato.serie}" title="#{extrato.serie}"/>
                                                    </p:column>

                                                    <p:column headerText="Montante" style="width: 120px; min-width: 120px;">
                                                        <h:outputText value="#{extrato.montante}" title="#{extrato.montante}" converter="conversormoeda"/>
                                                    </p:column>

                                                    <p:column headerText="Embarques" style="width: 120px; min-width: 120px;">
                                                        <h:outputText value="#{extrato.embarques}" title="#{extrato.embarques}" converter="conversormoeda"/>
                                                    </p:column>

                                                    <p:column headerText="Ad Valorem" style="width: 120px; min-width: 120px;">
                                                        <h:outputText value="#{extrato.adValorem}" title="#{extrato.adValorem}" converter="conversormoeda"/>
                                                    </p:column>

                                                    <p:column headerText="Tempo Espera" style="width: 120px; min-width: 120px;">
                                                        <h:outputText value="#{extrato.tempoEspera}" title="#{extrato.tempoEspera}"/>
                                                    </p:column>

                                                    <p:column headerText="Custódia" style="width: 120px; min-width: 120px;">
                                                        <h:outputText value="#{extrato.custodia}" title="#{extrato.custodia}" converter="conversormoeda"/>
                                                    </p:column>

                                                    <p:column headerText="Milheiros" style="width: 120px; min-width: 120px;">
                                                        <h:outputText value="#{extrato.milheiros}" title="#{extrato.milheiros}" converter="conversormoeda"/>
                                                    </p:column>

                                                    <p:column headerText="Envelopes" style="width: 120px; min-width: 120px;">
                                                        <h:outputText value="#{extrato.envelopesMalotes}" title="#{extrato.envelopesMalotes}" converter="conversormoeda"/>
                                                    </p:column>

                                                    <p:column headerText="ISS/ICMS/Repasse" style="width: 150px; min-width: 150px;">
                                                        <h:outputText value="#{extrato.ISS}" title="#{extrato.ISS}" converter="conversormoeda"/>
                                                    </p:column>

                                                    <p:column headerText="Total" style="width: 120px; min-width: 120px;">
                                                        <h:outputText value="#{extrato.total}" title="#{extrato.total}" converter="conversormoeda"/>
                                                    </p:column>

                                                </p:dataTable>
                                                </div>
                                            </div>
                                        </div>
                                    </p:tab>

                                    <p:tab title="#{localemsgs.NFiscal}" id="nfiscal" rendered="false">
                                        <div style="width:100% !important; height:calc(100vh - 152px) !important; overflow:hidden !important; padding:0px !important">
                                            <iframe id="ifrNFiscal" src="../relatorio/nfiscal.xhtml?faces-redirect=true&amp;portal=S" style="width:100%; height:100%; padding:0px; border:none"></iframe>
                                        </div>
                                        <script type="text/javascript">
                                            // <![CDATA[
                                            $(document).ready(function () {
                                                setInterval(function () {
                                                    $obj = $('#ifrNFiscal').contents();
                                                    $obj.find('#divDadosFilial, #divBotaoVoltar, footer').css('display', 'none');
                                                    $obj.find('#divCalendario').css({
                                                        'position': 'absolute',
                                                        'top': '-2px',
                                                        'right': '20px'
                                                    });

                                                    $obj.find('header').css({
                                                        'margin-top': '6px',
                                                        'border': 'none',
                                                        'box-shadow': '2px 2px 3px #CCC'
                                                    });
                                                }, 10);
                                            });
                                            // ]]>
                                        </script>
                                    </p:tab>
                                    <p:tab title="Notas Fiscais" id="notasfiscais" rendered="#{!login.pp.empresa.contains('CONFEDERAL')}">

                                        <!-- Hotkey para mostrar/ocultar filtros com tecla P -->
                                        <p:hotkey bind="p" actionListener="#{guias.toggleFiltrosNF}" oncomplete="toggleFiltrosNFJS()"/>

                                        <div style="height: calc(100vh - 150px); overflow: hidden; display: flex; flex-direction: column;">

                                            <!-- Filtros das Notas Fiscais -->
                                            <div style="flex-shrink: 0; margin-bottom: 10px;">

                                                <!-- Painel de filtros -->
                                                <div id="painelFiltrosNF" style="#{guias.mostrarFiltrosNF ? 'display: block;' : 'display: none;'} background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin-bottom: 10px;">
                                                    <h4 style="margin-top: 0; color: #495057;">Filtros das Notas Fiscais</h4>

                                                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                                        <div>
                                                            <label for="filtroNumeroNF_NF" style="font-weight: bold; display: block; margin-bottom: 5px;">Número NF:</label>
                                                            <p:inputText id="filtroNumeroNF_NF" value="#{guias.filtroNumeroNF_NF}"
                                                                         placeholder="Digite o número da NF"
                                                                         style="width: 100%"/>
                                                        </div>

                                                        <div>
                                                            <label for="filtroTipoServicoNF" style="font-weight: bold; display: block; margin-bottom: 5px;">Tipo Serviço:</label>
                                                            <p:inputText id="filtroTipoServicoNF" value="#{guias.filtroTipoServicoNF}"
                                                                         placeholder="Digite o tipo de serviço"
                                                                         style="width: 100%"/>
                                                        </div>

                                                        <div>
                                                            <!-- Espaço vazio para manter layout -->
                                                        </div>

                                                        <div>
                                                            <!-- Espaço vazio para manter layout -->
                                                        </div>
                                                    </div>

                                                    <div style="margin-bottom: 10px;">
                                                        <p:commandButton value="Filtrar"
                                                                         action="#{guias.aplicarFiltrosNF}"
                                                                         update="tabelaNotasFiscais msgs"
                                                                         icon="fa fa-search"
                                                                         styleClass="ui-button-info"/>

                                                        <p:commandButton value="Limpar Filtros"
                                                                         action="#{guias.limparFiltrosNF}"
                                                                         update="tabelaNotasFiscais msgs"
                                                                         icon="fa fa-eraser"
                                                                         styleClass="ui-button-secondary"
                                                                         style="margin-left: 8px;"
                                                                         oncomplete="limparFiltrosNFJS()"/>

                                                        <p:commandButton value="Ocultar Filtros"
                                                                         action="#{guias.toggleFiltrosNF}"
                                                                         icon="fa fa-times"
                                                                         styleClass="ui-button-warning"
                                                                         style="margin-left: 8px;"
                                                                         oncomplete="toggleFiltrosNFJS()"/>
                                                    </div>

                                                    <div>
                                                        <small style="color: #6c757d; font-style: italic;">
                                                            Filtros aplicados nas notas fiscais do período selecionado.
                                                        </small>
                                                    </div>
                                                </div>

                                                <!-- Botão para mostrar filtros (quando ocultos) -->
                                                <div id="botaoMostrarFiltrosNF" style="#{guias.mostrarFiltrosNF ? 'display: none;' : 'display: block;'} margin-bottom: 10px;">
                                                    <p:commandButton value="Mostrar Filtros (Pressione P)"
                                                                     action="#{guias.toggleFiltrosNF}"
                                                                     icon="fa fa-filter"
                                                                     styleClass="ui-button-info"
                                                                     oncomplete="toggleFiltrosNFJS()"/>
                                                </div>
                                            </div>

                                            <!-- Tabela das Notas Fiscais -->
                                            <div style="flex: 1; overflow: hidden;">
                                                <div style="width: 100%; height: 100%; overflow: auto; border: 1px solid #ddd;">
                                                    <p:dataTable id="tabelaNotasFiscais" value="#{guias.notasFiscaisFiltradas}" paginator="true" rows="15"
                                                     rowsPerPageTemplate="5,10,15,20,25,50"
                                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} Notas Fiscais"
                                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                     var="notaFiscal"
                                                     styleClass="tabela DataGrid"
                                                     emptyMessage="#{localemsgs.SemRegistros}"
                                                     scrollable="false"
                                                     style="font-size: 12px; background: white; width: 1400px; min-width: 1400px;">

                                            <p:column headerText="Praça" style="width: 80px">
                                                <h:outputText value="#{notaFiscal.praca}" title="#{notaFiscal.praca}"/>
                                            </p:column>

                                            <p:column headerText="Número" style="width: 120px">
                                                <h:outputText value="#{notaFiscal.numero}" title="#{notaFiscal.numero}"/>
                                            </p:column>

                                            <p:column headerText="Série" style="width: 80px">
                                                <h:outputText value="#{notaFiscal.serie}" title="#{notaFiscal.serie}"/>
                                            </p:column>

                                            <p:column headerText="Data" style="width: 100px">
                                                <h:outputText value="#{notaFiscal.data}" title="#{notaFiscal.data}" converter="conversorData"/>
                                            </p:column>

                                            <p:column headerText="NRed" style="width: 120px">
                                                <h:outputText value="#{notaFiscal.NRed}" title="#{notaFiscal.NRed}"/>
                                            </p:column>

                                            <p:column headerText="Cliente">
                                                <h:outputText value="#{notaFiscal.nomeCliente}" title="#{notaFiscal.nomeCliente}"/>
                                            </p:column>

                                            <p:column headerText="Tipo Serviço">
                                                <h:outputText value="#{notaFiscal.tipoServico}" title="#{notaFiscal.tipoServico}"/>
                                            </p:column>

                                            <p:column headerText="Valor" style="width: 120px">
                                                <h:outputText value="#{notaFiscal.valor}" title="#{notaFiscal.valor}" converter="conversormoeda"/>
                                            </p:column>

                                            <p:column headerText="Valor Líquido" style="width: 120px">
                                                <h:outputText value="#{notaFiscal.valorLiq}" title="#{notaFiscal.valorLiq}" converter="conversormoeda"/>
                                            </p:column>

                                            <p:column headerText="Dt. Vencimento" style="width: 120px">
                                                <h:outputText value="#{notaFiscal.dtVenc}" title="#{notaFiscal.dtVenc}" converter="conversorData"/>
                                            </p:column>

                                            <p:column headerText="Ações" style="width: 80px; text-align: center">
                                                <p:commandButton value="Ver" title="Visualizar Nota Fiscal"
                                                                 action="#{guias.selecionarNotaFiscal(notaFiscal)}"
                                                                 update=":formDetalhesNF"
                                                                 oncomplete="PF('dlgDetalhesNF').show();"
                                                                 styleClass="ui-button-secondary"
                                                                 style="margin-right: 5px"/>
                                            </p:column>
                                        </p:dataTable>
                                                </div>
                                            </div>
                                        </div>
                                    </p:tab>
                                </p:tabView>
                            </p:panel>
                        </div>
                    </div>
                </div>

                <p:panel style="position: fixed; z-index: 1; right: 5px; bottom: 100px !important; background: transparent; height: 300px" id="botoes">
                    <div style="padding-bottom: 10px">
                        <p:commandLink title="#{localemsgs.Imprimir}" action="#{guias.imprimir}"
                                       update="impressao msgs">
                            <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="40"/>
                        </p:commandLink>
                    </div>
                    <div style="padding-bottom: 10px">
                        <p:commandLink title="#{localemsgs.UploadPreOrder}" rendered="#{login.pp.empresa.contains('CONFEDERAL')}"
                                       action="#{guias.verificaClienteOrigemPedido}"
                                       update="formUpload msgs">
                            <p:graphicImage url="../assets/img/icone_upload.png" height="40"/>
                        </p:commandLink>
                        <p:commandLink title="#{localemsgs.UploadPedido}"
                                       rendered="#{!login.pp.empresa.contains('CONFEDERAL')}"
                                       action="#{guias.preImportacaoPedido}"
                                       update="formImportarPedidos msgs">
                            <p:graphicImage url="../assets/img/icone_upload.png" height="40"/>
                        </p:commandLink>
                    </div>
                    <div style="padding-bottom: 10px"> 
                        <p:commandLink title="#{login.pp.empresa.contains('CONFEDERAL')? localemsgs.PreOrder:localemsgs.Novo}"
                                       actionListener="#{guias.preCadastro()}" oncomplete="PF('dlgPedido').show();" update="pedido">
                            <p:graphicImage url="../assets/img/icone_solicitarpedidos.png" height="40"/>
                        </p:commandLink>
                    </div>
                    <div style="padding-bottom: 10px">
                        <p:commandLink title="#{localemsgs.Pesquisar}"
                                       oncomplete="PF('dlgPesquisar').show()" update="formPesquisar cabecalho">
                            <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                        </p:commandLink>
                    </div>
                    <div style="padding-bottom: 10px; display:none">
                        <p:commandLink action="pedidos.xhtml?faces-redirect=true" title="#{localemsgs.Pedidos}">
                            <p:graphicImage url="../assets/img/icone_pedidos.png" height="40"/>
                        </p:commandLink>
                    </div>
                    <div style="padding-bottom: 10px;">
                        <p:commandLink title="#{localemsgs.ManifestosDisponiveis}" update="msgs" 
                                       actionListener="#{guias.listarManifestosDisponiveis}">
                            <p:graphicImage url="../assets/img/icone_satmob_supervisoesrecentes.png" height="40"/>
                        </p:commandLink>
                    </div>
                    <div style="padding-bottom: 10px;">
                        <h:commandLink id="xlsx">
                            <p:graphicImage url="../assets/img/icone_xls.png"/>
                            <p:dataExporter target="main:mainTabView:tabela" type="xlsx"
                                            fileName="#{localemsgs.Guias}-#{guias.data1}-#{guias.data2}"/> 
                        </h:commandLink>
                    </div>

                    <div style="padding-bottom: 10px">
                        <p:commandLink title="#{localemsgs.LimparFiltros}"
                                       update="msgs main cabecalho totais" actionListener="#{guias.limparFiltros}">

                            <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                        </p:commandLink>
                    </div>

                    <div style="padding-bottom: 10px; display:none !important;"> 
                        <p:commandLink id="btOcultoMapas" title="Botao oculto para mapa" update="msgs" 
                                       actionListener="#{valores.listarTrajetoExecucaoGuia}">
                        </p:commandLink>
                    </div>
                </p:panel>
            </h:form>

            <!-- Dialog de Detalhes da Nota Fiscal -->
            <h:form id="formDetalhesNF">
                <p:dialog widgetVar="dlgDetalhesNF" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="true" id="dlgDetalhesNF"
                          style="max-height:90% !important; min-width:80% !important; max-width:90% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important; border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">

                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_guias_40x40.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="Detalhes da Nota Fiscal" style="color:#022a48;" />
                    </f:facet>

                    <p:panel id="panelDetalhesNF" style="background-color: transparent" styleClass="cadastrar">

                        <!-- Informações da Nota Fiscal -->
                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="margin-bottom: 20px;">

                            <h:outputLabel value="Número:" style="font-weight: bold;"/>
                            <h:outputText value="#{guias.nfiscalSelecionada.numero}" style="font-size: 14px;"/>

                            <h:outputLabel value="Série:" style="font-weight: bold;"/>
                            <h:outputText value="#{guias.nfiscalSelecionada.serie}" style="font-size: 14px;"/>

                            <h:outputLabel value="Praça:" style="font-weight: bold;"/>
                            <h:outputText value="#{guias.nfiscalSelecionada.praca}" style="font-size: 14px;"/>

                            <h:outputLabel value="Data:" style="font-weight: bold;"/>
                            <h:outputText value="#{guias.nfiscalSelecionada.data}" converter="conversorData" style="font-size: 14px;"/>

                            <h:outputLabel value="Cliente:" style="font-weight: bold;"/>
                            <h:outputText value="#{guias.nfiscalSelecionada.nomeCliente}" style="font-size: 14px;"/>

                            <h:outputLabel value="Tipo Serviço:" style="font-weight: bold;"/>
                            <h:outputText value="#{guias.nfiscalSelecionada.tipoServico}" style="font-size: 14px;"/>

                            <h:outputLabel value="Valor:" style="font-weight: bold;"/>
                            <h:outputText value="#{guias.nfiscalSelecionada.valor}" converter="conversormoeda" style="font-size: 14px;"/>

                            <h:outputLabel value="Dt. Vencimento:" style="font-weight: bold;"/>
                            <h:outputText value="#{guias.nfiscalSelecionada.dtVenc}" converter="conversorData" style="font-size: 14px;"/>

                        </p:panelGrid>

                        <!-- Seção de Boletos -->
                        <p:fieldset legend="Boletos Relacionados" style="margin-top: 20px;">
                            <p:dataTable id="tabelaBoletos" value="#{guias.boletosNF}" var="boleto"
                                         styleClass="tabela DataGrid"
                                         emptyMessage="Nenhum boleto encontrado"
                                         style="font-size: 12px; background: white; padding:0px !important; margin:0px !important; width:100% !important;">

                                <p:column headerText="Número NF" style="width: 120px">
                                    <h:outputText value="#{boleto.numero}" title="#{boleto.numero}"/>
                                </p:column>

                                <p:column headerText="Praça" style="width: 80px">
                                    <h:outputText value="#{boleto.praca}" title="#{boleto.praca}"/>
                                </p:column>

                                <p:column headerText="Nosso Número" style="width: 150px">
                                    <h:outputText value="#{boleto.nossoNumero}" title="#{boleto.nossoNumero}"/>
                                </p:column>

                                <p:column headerText="Banco" style="width: 100px">
                                    <h:outputText value="#{boleto.banco}" title="#{boleto.banco}"/>
                                </p:column>

                                <p:column headerText="Sequência" style="width: 100px">
                                    <h:outputText value="#{boleto.sequencia}" title="#{boleto.sequencia}"/>
                                </p:column>

                                <p:column headerText="Ordem" style="width: 80px">
                                    <h:outputText value="#{boleto.ordem}" title="#{boleto.ordem}"/>
                                </p:column>

                                <p:column headerText="Valor" style="width: 120px">
                                    <h:outputText value="#{boleto.valor}" title="#{boleto.valor}" converter="conversormoeda"/>
                                </p:column>

                                <p:column headerText="Dt. Vencimento" style="width: 120px">
                                    <h:outputText value="#{boleto.dataVencimento}" title="#{boleto.dataVencimento}" converter="conversorData"/>
                                </p:column>

                                <p:column headerText="Ações" style="width: 80px; text-align: center">
                                    <p:commandButton value="Ver" title="Visualizar Boleto"
                                                     action="#{guias.visualizarBoleto(boleto)}"
                                                     update="msgs"
                                                     styleClass="ui-button-secondary"/>
                                </p:column>
                            </p:dataTable>
                        </p:fieldset>

                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form class="form-inline" id="formUpload">
                <p:dialog widgetVar="dlgUpload" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                          style="max-height:95% !important;min-width:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.UploadPreOrder}" style="color:#022a48;" /> 
                    </f:facet>
                    <p:panel id="panelUpload" style="background-color: transparent" styleClass="cadastrar">

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9 origem" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="origem" value="#{localemsgs.OrigemPedido}: "/>
                            <p:inputText id="origem" value="#{guias.nomecli}" style="width: 100%" readonly="true"/>
                        </p:panelGrid>

                        <p:fileUpload id="espacoUpload" auto="true" skinSimple="true" label="#{localemsgs.SelecioneArquivo}"
                                      update="formUpload:panelUpload formUpload:arquivos formUpload:listaPedidos msgs formPreOrders" 
                                      onstart="PF('pfBlock').show();" oncomplete="PF('dlgUpload').initPosition();PF('pfBlock').hide();"
                                      class="upload" multiple="true" dragDropSupport="true"
                                      fileUploadListener="#{guias.realizarUpload}" mode="advanced" 
                                      allowTypes="/(\.|\/)(txt)$/" previewWidth="10" invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                      style="height:40px;">
                        </p:fileUpload>
                        <div style="height:170px !important; overflow-y:auto !important; width:100%; position:relative;">
                            <p:dataTable value="#{guias.arquivosPedidos}" scrollHeight="100%" scrollWidth="100%" 
                                         scrollable="true" rendered="#{!guias.arquivosPedidos.isEmpty()}"
                                         var="listaDocumentos" 
                                         id="arquivos" 
                                         styleClass="tabelaArquivos DataGrid"
                                         style="font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                                <f:facet name="header">
                                    <h:outputText value="#{localemsgs.ArquivosRecentes}:"/>
                                </f:facet>
                                <p:column headerText="#{localemsgs.Nome}" style="width: 80%">
                                    <h:outputText value="#{listaDocumentos.name}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Tamanho}" style="width: 20%; text-align: center">
                                    <h:outputText value="#{listaDocumentos.length()}" converter="conversorKB"/>
                                </p:column>
                            </p:dataTable>
                        </div>
                        <p:dataTable value="#{guias.pedidosRecentes}" scrollHeight="120" 
                                     scrollable="true" 
                                     resizableColumns="true"
                                     var="listaPedidos" 
                                     id="listaPedidos" 
                                     styleClass="tabela DataGrid"
                                     style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                            <f:facet name="header">
                                <h:outputText value="#{localemsgs.PedidosRecentes}:"/>
                            </f:facet>
                            <p:column headerText="#{localemsgs.Agencia}" style="width: 50px">
                                <h:outputText value="#{listaPedidos.agencia}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.SubAgencia}" style="width: 92px">
                                <h:outputText value="#{listaPedidos.subAgencia}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Pedido}" style="width: 60px">
                                <h:outputText value="#{listaPedidos.pedidoCliente}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DtColeta}" style="width: 82px">
                                <h:outputText value="#{listaPedidos.dtColeta}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DtEntrega}" style="width: 82px">
                                <h:outputText value="#{listaPedidos.dtEntrega}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hora}" style="width: 100px">
                                <h:outputText value="#{listaPedidos.hora1D}" converter="conversorHora"/>
                                -
                                <h:outputText value="#{listaPedidos.hora2D}" converter="conversorHora"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Valor}" style="width: 100px;">
                                <h:outputText value="#{listaPedidos.valor}" converter="conversormoeda"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Obs}">
                                <h:outputText value="#{listaPedidos.obs}"/>
                            </p:column>
                        </p:dataTable>

                        <p:commandLink oncomplete="PF('dlgUpload').hide();" update="main"
                                       title="#{localemsgs.Enviar}">
                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                        </p:commandLink>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="mapa" class="form-inline">                   
                <p:dialog widgetVar="dlgMapa" id="telaMapa" positionType="absolute" responsive="true"
                          draggable="false" styleClass="dialogo" modal="true" closable="true" resizable="false"
                          dynamic="true" showEffect="drop" hideEffect="drop" closeOnEscape="true"
                          style="max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important;">
                    <p:ajax event="close" oncomplete="clearInterval(tmrReloadMap);" />

                    <f:facet name="header">
                        <img src="../assets/img/icone_redondo_mapa.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.TrajetoAndamento}" style="color:#022a48"/>
                        <label style="position:absolute; left:60px; top:42px; font-size:8pt; color:#888 !important; display:none">#{localemsgs.AtualizarMapaAuto}&nbsp;<span id="spnTimer"></span>&nbsp;#{localemsgs.Segundos}</label>
                    </f:facet>
                    <p:panel id="cadastrar" class="cadastrar" style="background-color: transparent; height: 280px; overflow:hidden;">
                        <div style="width:100%; height: 280px !important;padding-left:0px !important; padding-right:0px !important; overflow:hidden;">
                            <div class="col-md-6 col-sm-6 col-xs-12" style="padding:0px 4px 0px 4px !important;">
                                <label style="text-align:center;text-shadow:1px 1px 1px #FFF !important;color:#505050; text-transform:capitalize; display:block; font-weight:bold; font-size:8pt; margin-left:6px; height:10px; border-radius:30px; background-color:#EEE; padding:2px 6px 2px 6px !important; border:thin solid #DDD; height:23px;"><i class="fa fa-user" style="font-size:12pt;"></i>&nbsp;#{localemsgs.ChEquipe}</label>
                                <label style="text-align:center;text-transform:uppercase; display:block; font-size:10pt; margin-left:6px;">#{valores.chefeEquipe}</label>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-12" style="padding:0px 4px 0px 4px !important;">
                                <label style="text-align:center;text-shadow:1px 1px 1px #FFF !important;color:#505050; text-transform:capitalize; display:block; font-weight:bold; font-size:8pt; margin-left:6px; height:10px; border-radius:30px; background-color:#EEE; padding:2px 6px 2px 6px !important; border:thin solid #DDD; height:23px;"><i class="fa fa-clock-o" style="font-size:12pt;"></i>&nbsp;#{localemsgs.UltimaComunicacao}</label>
                                <label style="text-align:center;text-transform:uppercase; display:block; font-size:10pt; margin-left:6px;"><h:outputText value="#{valores.ultimaPosicaoData}" converter="conversorData"/> - <h:outputText value="#{valores.ultimaPosicao}" converter="conversorHora"/></label>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="height:calc(100% - 50px); border:thin solid #DDD; margin-top:8px; padding:0px !important">
                                <div id="mapGoogle" style="height:100%; width:100%;"></div>
                            </div>
                        </div>
                    </p:panel>
                    <script type="text/javascript">
                        // <![CDATA[
                        var map;
                        var directionsDisplayNext;

                        function initMap() {
                            map = new google.maps.Map(document.getElementById('mapGoogle'), {
                                zoom: 6,
                                center: #{valores.centro},
                                gestureHandling: 'cooperative'
                            });

                            var directionsServiceNext = new google.maps.DirectionsService;
                            directionsDisplayNext = new google.maps.DirectionsRenderer({polylineOptions: {strokeColor: "#006633", strokeOpacity: 0.9, strokeWeight: 5},
                                suppressMarkers: true});


                        #{valores.markers}
                            directionsDisplayNext.setMap(map);

                            var onChangeHandler = function () {
                                calculateAndDisplayRouteNext(directionsServiceNext, directionsDisplayNext);
                            };
                            onChangeHandler();
                        }

                        function calculateAndDisplayRouteNext(directionsService, directionsDisplay) {
                            directionsService.route({
                        #{valores.directionsTrajetoProximo}
                                travelMode: 'DRIVING'
                            } function (response, status) {
                                if (status === 'OK') {
                                    directionsDisplay.setDirections(response);
                                } else {

                                }
                                console.log(response);
                            });
                          }

                        // ]]>
                    </script>
                    <script src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}&amp;callback=initMap"></script>
                </p:dialog>
            </h:form>

            <h:form id="formImportarPedidos">
                <p:dialog widgetVar="dlgImportarPedidos" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                          style="max-height:95% !important;min-width:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.UploadPedido}" style="color:#022a48;" /> 
                    </f:facet>
                    <p:panel id="panelUpload" style="background-color: transparent" styleClass="cadastrar">

                        <p:fileUpload id="espacoUpload" auto="true" skinSimple="true" label="#{localemsgs.SelecioneArquivo}"
                                      update="formImportarPedidos:panelUpload formImportarPedidos:arquivos msgs formPreOrders" 
                                      onstart="PF('pfBlock').show();" oncomplete="PF('dlgImportarPedidos').initPosition();PF('pfBlock').hide();"
                                      class="upload" multiple="true" dragDropSupport="true"
                                      fileUploadListener="#{guias.realizarUploadPedido}" mode="advanced" 
                                      allowTypes="/(\.|\/)(txt)$/" previewWidth="10" invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                      style="height:40px;">
                        </p:fileUpload>
                        <div style="height:170px !important; overflow-y:auto !important; width:100%; position:relative;">
                            <p:dataTable value="#{guias.arquivosPedidos}" scrollHeight="100%" scrollWidth="100%" 
                                         scrollable="true" rendered="#{!guias.arquivosPedidos.isEmpty()}"
                                         var="listaDocumentos" 
                                         id="arquivos" 
                                         styleClass="tabelaArquivos DataGrid"
                                         style="font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                                <f:facet name="header">
                                    <h:outputText value="#{localemsgs.ArquivosRecentes}:"/>
                                </f:facet>
                                <p:column headerText="#{localemsgs.Nome}" style="width: 80%">
                                    <h:outputText value="#{listaDocumentos.name}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Tamanho}" style="width: 20%; text-align: center">
                                    <h:outputText value="#{listaDocumentos.length()}" converter="conversorKB"/>
                                </p:column>
                            </p:dataTable>
                        </div>

                        <p:commandLink oncomplete="PF('dlgImportarPedidos').hide();" update="main"
                                       title="#{localemsgs.Enviar}">
                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                        </p:commandLink>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="formPedidosImportados">
                <p:dialog widgetVar="dlgPedidosImportados" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                          style="max-height:95% !important;min-width:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.UploadPedido}" style="color:#022a48;" /> 
                    </f:facet>
                    <p:panel id="panelPreOrders" style="background-color: transparent" styleClass="cadastrar">

                        <h:outputText value="#{guias.mensagemImportacao}"/>

                        <p:dataTable value="#{guias.pedidosImportados}" scrollHeight="400" 
                                     scrollable="true" resizableColumns="true"
                                     var="listaPedidos" id="listaPedidos" styleClass="tabela DataGrid"
                                     style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;
                                     min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; 
                                     width:100% !important;">
                            <p:ajax event="rowToggle" oncomplete="PF('dlgPedidosImportados').initPosition();"/>
                            <p:column style="width:16px">
                                <p:rowToggler rendered="#{listaPedidos.composicoes.size() gt 0}"  />
                            </p:column>
                            <p:column headerText="#{localemsgs.Data}">
                                <h:outputText value="#{listaPedidos.pedido.dtColeta}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Horario}">
                                <h:outputText value="#{listaPedidos.pedido.hora1D}" converter="conversorHora"/>
                                <h:outputText value=" - "/>
                                <h:outputText value="#{listaPedidos.pedido.hora2D}" converter="conversorHora"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Valor}">
                                <h:outputText value="#{listaPedidos.pedido.valor}" converter="conversormoeda"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Origem}">
                                <h:outputText value="#{listaPedidos.pedido.NRed1}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Destino}">
                                <h:outputText value="#{listaPedidos.pedido.NRed2}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Obs}">
                                <h:outputText value="#{listaPedidos.pedido.obs}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Composicoes}">
                                <h:outputText value="#{listaPedidos.composicoes.size()}"/>
                            </p:column>

                            <p:rowExpansion>
                                <p:dataTable value="#{listaPedidos.composicoes}"
                                             resizableColumns="true"
                                             var="listaLacres" id="listaLacres" styleClass="tabela DataGrid"
                                             style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                                    <p:column headerText="#{localemsgs.TipoCedula}" style="width: 75px">
                                        <h:outputText value="#{listaLacres.lacre}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Qtde}" style="width: 75px">
                                        <h:outputText value="#{listaLacres.qtde}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Valor}" style="width: 130px">
                                        <h:outputText value="#{listaLacres.valor}" converter="conversormoeda"/>
                                    </p:column>
                                </p:dataTable>
                            </p:rowExpansion>
                        </p:dataTable>

                        <p:commandLink actionListener="#{guias.inserirPedidos}" 
                                       update="formImportarPedidos:panelUpload formImportarPedidos:arquivos msgs"
                                       title="#{localemsgs.Prosseguir}">
                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                        </p:commandLink>
                        <p:commandLink oncomplete="PF('dlgPedidosImportados').hide();"
                                       title="#{localemsgs.Cancelar}">
                            <p:graphicImage url="../assets/img/icone_fechar.png" width="40" height="40" />
                        </p:commandLink>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="formPreOrders">
                <p:dialog widgetVar="dlgPreOrders" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                          style="max-height:95% !important;min-width:95% !important;max-width:95% !important; border:thin solid #666 !important;
                          box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;
                          border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important;
                          padding:0px 0px 20px 0px !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.UploadPreOrder}" style="color:#022a48;" /> 
                    </f:facet>
                    <p:panel id="panelPreOrders" style="background-color: transparent" styleClass="cadastrar">

                        <h:outputText value="#{guias.mensagemImportacao}"/>

                        <p:dataTable value="#{guias.listaAgencias}" scrollHeight="400" 
                                     scrollable="true" resizableColumns="true"
                                     var="listaPedidos" id="listaPedidos" styleClass="tabela DataGrid"
                                     style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                            <p:column style="width:16px">
                                <p:rowToggler />
                            </p:column>
                            <p:column headerText="#{localemsgs.Agencia}">
                                <h:outputText value="#{listaPedidos.agencia}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.SubAgencia}">
                                <h:outputText value="#{listaPedidos.subAgencia}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DtColeta}">
                                <h:outputText value="#{listaPedidos.dtColeta}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.DtEntrega}">
                                <h:outputText value="#{listaPedidos.dtEntrega}" converter="conversorData"/>
                            </p:column>

                            <p:rowExpansion>
                                <p:dataTable value="#{listaPedidos.listaMalotes}"
                                             resizableColumns="true"
                                             var="listaLacres" id="listaLacres" styleClass="tabela DataGrid"
                                             style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">
                                    <p:column headerText="#{localemsgs.Pedido}" style="width: 75px">
                                        <h:outputText value="#{listaLacres.pedidoCliente}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Lacre}" style="width: 75px">
                                        <h:outputText value="#{listaLacres.lacre}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Horario}" style="width: 70px">
                                        <h:outputText value="#{listaLacres.horario}" converter="conversorHora"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Valor}" style="width: 130px">
                                        <h:outputText value="#{listaLacres.valor}" converter="conversormoeda"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Obs}" style="white-space: normal">
                                        <h:outputText value="#{listaLacres.obs}"/>
                                    </p:column>
                                </p:dataTable>
                            </p:rowExpansion>
                        </p:dataTable>

                        <p:commandLink actionListener="#{guias.verificarExistenciaPreOrder}" 
                                       update="formUpload:panelUpload formUpload:arquivos formUpload:listaPedidos msgs panelConfirmacaoPreOrders"
                                       title="#{localemsgs.Prosseguir}">
                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                        </p:commandLink>
                        <p:commandLink oncomplete="PF('dlgPreOrders').hide();"
                                       title="#{localemsgs.Cancelar}">
                            <p:graphicImage url="../assets/img/icone_fechar.png" width="40" height="40" />
                        </p:commandLink>
                    </p:panel>
                </p:dialog>

                <p:dialog positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;"
                          header="#{localemsgs.Opcoes}" widgetVar="dlgConfirmacao">
                    <f:facet name="header">
                        <img src="../assets/img/icone_upload.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.UploadPreOrder}" style="color:#022a48;" /> 
                    </f:facet>
                    <p:panel id="panelConfirmacaoPreOrders" style="background-color: transparent; text-align: center" styleClass="editar">

                        <div class="form-inline">
                            <h:outputText value="#{guias.mensagemImportacao}" style="text-align: center"/>
                            <p:spacer height="20px"/>
                        </div>

                        <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4," 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:commandButton action="#{guias.inserirNovoLotePreOrders}" 
                                             update="formUpload:panelUpload formUpload:arquivos formUpload:listaPedidos msgs"
                                             title="#{localemsgs.NovaImportacao}" value="#{localemsgs.NovaImportacao}" />

                            <p:commandButton action="#{guias.inserirPreOrders}" 
                                             update="formUpload:panelUpload formUpload:arquivos formUpload:listaPedidos msgs"
                                             title="#{localemsgs.Atualizar}" value="#{localemsgs.Atualizar}"/>

                            <p:commandButton oncomplete="PF('dlgConfirmacao').hide();PF('dlgPreOrders').hide();"
                                             title="#{localemsgs.Cancelar}" value="#{localemsgs.Cancelar}"/>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form class="form-inline" id="formManifestosDisponiveis">    
                <p:dialog widgetVar="dlgManifestosDisponiveis" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgUpload"
                          style="max-height:95% !important;min-width:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_solicitarpedidos.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.ManifestosDisponiveis}" style="color:#022a48;" /> 
                    </f:facet>
                    <p:panel id="panelManifestosDisponiveis" style="background-color: transparent" styleClass="cadastrar">

                        <p:dataTable value="#{guias.pedidosRecentes}" scrollHeight="120"  selectionMode="single" 
                                     selection="#{guias.preOrderSelecionado}" rowKey="#{listaPedidos.dtColeta}#"
                                     scrollable="true" resizableColumns="true"
                                     var="listaPedidos" id="listaPedidos" styleClass="tabela DataGrid"
                                     style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;">

                            <p:ajax event="rowDblselect" listener="#{guias.listarManifestoPreOrder}" update="impressao msgs"/>
                            <p:column headerText="#{localemsgs.CodFil}" style="width: 50px">
                                <h:outputText value="#{listaPedidos.codFil}" converter="conversorCodFil"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.DtColeta}" style="width: 82px">
                                <h:outputText value="#{listaPedidos.dtColeta}" converter="conversorData"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.QtdGuiasPreOrder}" style="width: 75px">
                                <h:outputText value="#{listaPedidos.RPV}"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Valor}" style="width: 140px">
                                <h:outputText value="#{listaPedidos.valor}" converter="conversormoeda"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.Lote}" style="width: 65px">
                                <h:outputText value="#{listaPedidos.lote}"/>
                            </p:column>
                        </p:dataTable>

                        <p:commandLink title="#{localemsgs.ListarManifestos}" action="#{guias.listarManifestoPreOrder}"
                                       update="impressao msgs">
                            <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="40"/>
                        </p:commandLink>
                    </p:panel>
                </p:dialog>
            </h:form>

            <p:dialog positionType="absolute" responsive="true"
                      draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                      style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;"
                      header="#{localemsgs.Opcoes}" widgetVar="dlgOk">
                <h:form>
                    <div class="form-inline">
                        <h:outputText value="#{localemsgs.EscolherOpcao}:" style="text-align: center"/>
                    </div>
                    <p:spacer height="30px"/>
                    <div class="form-inline">
                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px"
                                     rendered="#{login.clientes.size() gt 1}">
                            <p:commandLink oncomplete="PF('dlgSelecionarCliente').show()">
                                <p:graphicImage url="../assets/img/icone_satmob_clientes.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink oncomplete="PF('dlgSelecionarCliente').show()">
                                <h:outputText  value="#{localemsgs.TrocarCliente}"/>
                            </p:commandLink> 
                        </h:panelGrid>

                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px"
                                     rendered="#{login.permissaoControleAcessos}">
                            <p:commandLink action="/configuracoes/acessos.xhtml?faces-redirect=true">
                                <p:graphicImage url="../assets/img/icone_usuarios.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink action="/configuracoes/acessos.xhtml?faces-redirect=true">
                                <h:outputText value="#{localemsgs.Usuarios}"/>
                            </p:commandLink> 
                        </h:panelGrid>

                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px">
                            <p:commandLink oncomplete="PF('dlgTrocarSenha').show()">
                                <p:graphicImage url="../assets/img/icone_configuracoes.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink oncomplete="PF('dlgTrocarSenha').show()" >
                                <h:outputText   value="#{localemsgs.TrocarSenha}"/>
                            </p:commandLink> 
                        </h:panelGrid>

                        <h:panelGrid columns="1" style="text-align: center; float: left; width: 80px">
                            <p:commandLink action="#{login.logOutRH}">
                                <p:graphicImage url="../assets/img/icone_sair.png" height="40"/>
                            </p:commandLink>
                            <p:commandLink action="#{login.logOutRH}">
                                <h:outputText value="#{localemsgs.Sair}"/>
                            </p:commandLink> 
                        </h:panelGrid>
                    </div>

                </h:form>
            </p:dialog>


            <h:form id="cliente">
                <p:dialog widgetVar="dlgSelecionarCliente" positionType="absolute" id="dlgClientes"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">  
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panel id="tabelaClientes" style="width: 440px; background: transparent">
                        <div class="form-inline">
                            <p:dataTable id="tabela" value="#{login.clientes}" emptyMessage="#{localemsgs.SemRegistros}"
                                         var="cli" resizableColumns="true" selection="#{login.selecionado}" rowKey="#{cli.codCli}"
                                         scrollable="true" scrollHeight="200" selectionMode="single" widgetVar="tabela"
                                         style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                         styleClass="tabela DataGrid" filteredValue="#{login.clientesFiltrados}">
                                <f:facet name="footer">
                                    <div class="ui-grid-row ui-grid-responsive">
                                        <div class="container col-md-12 row" style="white-space:nowrap">
                                            <div class="col-md-6" style="color:black; text-align: left; font-weight: normal; white-space:nowrap; padding-left:0px !important;">
                                                <h:outputText value="#{localemsgs.VerTodos}: "/>
                                                <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                            </div>
                                            <div class="col-md-6" style="color:black; text-align: right; font-weight: normal; white-space:nowrap; padding-left:0px !important;">
                                                <h:outputText value="#{localemsgs.Buscar}: " />
                                                <p:inputText id="globalFilter" onkeypress="if (event.keyCode == 13) {
                                PF('tabela').filter();
                                return false;
                            }"
                                                             style="width:150px;"/>
                                            </div>
                                        </div>
                                    </div>
                                </f:facet>
                                <p:ajax event="rowDblselect" listener="#{login.dblSelectGTV}" update="msgs"/>
                                <p:column rendered="#{!login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.Empresa}" style="width: 145px" filterBy="#{cli.operador}"
                                          filterMatchMode="contains">
                                    <h:outputText value="#{cli.operador}" title="#{cli.operador}">
                                    </h:outputText>
                                </p:column>
                                <p:column rendered="#{login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.Pref}" style="width: 60px" filterBy="#{cli.agencia}"
                                          filterMatchMode="contains">
                                    <h:outputText value="#{cli.agencia}" title="#{cli.agencia}">
                                    </h:outputText>
                                </p:column>
                                <p:column rendered="#{login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.NSOP}" style="width: 60px" filterBy="#{cli.subAgencia}"
                                          filterMatchMode="contains">
                                    <h:outputText value="#{cli.subAgencia}" title="#{cli.subAgencia}">
                                    </h:outputText>
                                </p:column>
                                <p:column headerText="#{login.pp.empresa.contains('CONFEDERAL') ? localemsgs.Agencia: localemsgs.Cliente}" filterBy="#{cli.nomeCli}"
                                          filterMatchMode="contains">
                                    <h:outputText value="#{cli.nomeCli}" title="#{cli.nomeCli}"/>
                                </p:column>
                            </p:dataTable>
                        </div>
                        <div class="form-inline">
                            <p:commandLink id="btnSelecionar" action="#{login.selecionarClienteGTV}" 
                                           title="#{localemsgs.Selecionar}">
                                <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="trocarsenha">
                <p:dialog widgetVar="dlgTrocarSenha" positionType="absolute" id="dlgTrocarSenha"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">  
                    <f:facet name="header">
                        <img src="../assets/img/icone_configuracoes.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.TrocarSenha}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panel id="panelSenha" style="width: 440px; background: transparent">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:outputLabel for="atual" value="#{localemsgs.SenhaAtual}" />
                                </div>
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:password id="atual" value="#{guias.senhaAtual}" label="#{localemsgs.SenhaAtual}" required="true" 
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.SenhaAtual}"/>
                                </div>
                            </div>
                        </div>

                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:outputLabel for="nova1" value="#{localemsgs.NovaSenha}" />
                                </div>
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:password id="nova1" value="#{guias.senhaNova}" match="nova2"
                                                label="#{localemsgs.NovaSenha}" required="true" feedback="true"
                                                promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                                goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NovaSenha}">
                                        <f:validateRegex pattern="^[0-9]{5,20}$" for="nova1"/>
                                    </p:password>
                                </div>
                            </div>
                        </div>

                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:outputLabel for="nova2" value="#{localemsgs.confirmarNovaSenha}" />
                                </div>
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:password id="nova2" value="#{guias.senhaNova}" label="#{localemsgs.SenhaAtual}" required="true" 
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Confirmacao}"/>
                                </div>
                            </div>
                        </div>

                        <div class="form-inline">
                            <p:commandLink id="btnSelecionar" action="#{guias.TrocarSenha}" 
                                           title="#{localemsgs.Concluido}" update="msgs">
                                <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="impressao">
                <p:dialog widgetVar="dlgImprimir" positionType="absolute" id="dlgImprimir" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                    <f:facet name="header">
                        <div class="ui-grid-col-8">
                            <img src="../assets/img/icone_satmob_guias_40x40.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Imprimir}"/>
                        </div>
                        <div class="ui-grid-col-2" style="text-align: right">
                            <p:commandLink title="#{localemsgs.Imprimir}">
                                <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="40"/>
                                <p:printer target="guiaimpressa"/>
                            </p:commandLink>
                        </div>
                        <div class="ui-grid-col-2" style="text-align: right">
                            <p:commandLink title="#{localemsgs.Download}" update="msgs"
                                           ajax="false"
                                           actionListener="#{guias.gerarGuiaDownload}">
                                <p:graphicImage url="../assets/img/icone_pdf.png" height="40"/>
                                <p:fileDownload value="#{guias.arquivoDownload}"/>
                            </p:commandLink>
                        </div>
                    </f:facet>

                    <p:panel id="guiaimpressa" class="guiaimpressa" styleClass="guiaimpressa" style="height: 75vh; background: transparent;">

                        <h:outputText value="#{guias.html}" escape="false"/>
                        <!--                        <ui:include src="guia.xhtml"/>-->
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="pedido" class="form-inline">
                <p:dialog widgetVar="dlgPedido" id="telaPedido" positionType="absolute" responsive="true"
                          draggable="false" styleClass="dialogo" modal="true" closable="true" resizable="false"
                          dynamic="true" showEffect="drop" hideEffect="drop" closeOnEscape="true"
                          style="max-height:90% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                    <script>
                        $(document).ready(function () {
                            //first unbind the original click event
                            PF('dlgPedido').closeIcon.unbind('click');
                            //register your own
                            PF('dlgPedido').closeIcon.click(function (e) {
                                $("#pedido\\:botaoFechar").click();
                                //should be always called
                                e.preventDefault();
                            });
                        })
                    </script>
                    <p:commandButton widgetVar="botaoFechar" style="display: none"
                                     oncomplete="PF('dlgPedido').hide()" id="botaoFechar">
                        <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert"/> 
                    </p:commandButton>
                    <f:facet name="header">
                        <img src="../assets/img/icone_solicitarpedidos.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{login.pp.empresa.contains('CONFEDERAL')? localemsgs.PreOrder:localemsgs.Pedido}" style="color:#022a48"/>
                    </f:facet>
                    <p:panel id="cadastrar" style="background-color: transparent; overflow:auto !important; max-height:600px" class="cadastrar">
                        <p:confirmDialog global="true">
                            <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                            <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                        </p:confirmDialog>

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="solicitante" value="#{localemsgs.Solicitante}:"/>
                            <p:inputText id="solicitante" value="#{guias.pedido.solicitante}"   style="width: 100%">
                                <p:watermark for="solicitante" value="#{localemsgs.Solicitante}"/>
                            </p:inputText>

                            <p:outputLabel for="tipoServico" value="#{localemsgs.Servico}:"/>
                            <p:selectOneRadio id="tipoServico" value="#{guias.tipoPedido}" style="width: 100%">
                                <f:selectItem itemLabel="#{localemsgs.Recolhimento}"  itemValue="recolhimento"/>
                                <f:selectItem itemLabel="#{localemsgs.Suprimento}"  itemValue="suprimento"/>
                                <p:ajax event="change" listener="#{guias.limparOrigemDestinoPortalPedido}" update="pedido:cadastrar"/>
                            </p:selectOneRadio>

                            <p:outputLabel for="origem" value="#{localemsgs.Origem}:" rendered="#{guias.tipoPedido eq 'recolhimento' or !login.transpCacamba}"/>
                            <p:outputLabel for="origem" value="#{localemsgs.Destino}:" rendered="#{guias.tipoPedido eq 'suprimento' and !login.transpCacamba}"/>
                            <p:autoComplete id="origem" value="#{guias.os_vig}" completeMethod="#{guias.buscarPedidos}" 
                                            styleClass="origem" style="width: 100%" label="#{localemsgs.Origem}" disabled="#{guias.tipoPedido eq 'suprimento' and login.transpCacamba or guias.tipoPedido eq 'suprimento' and login.transpCacamba}"
                                            forceSelection="true" required="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{guias.tipoPedido eq 'recolhimento' ? localemsgs.Origem : localemsgs.Destino}}"
                                            minQueryLength="3" scrollHeight="200" var="cont" itemValue="#{cont}" itemLabel="#{cont.NRed}">
                                <p:column style="width: 50%">
                                    #{cont.NRed}
                                </p:column>
                                <p:column>
                                    #{cont.NRedDst}
                                </p:column>
                                <p:watermark for="origem" value="#{localemsgs.Origem}"/>
                                <p:ajax event="itemSelect" listener="#{guias.selecionarDest}" update="pedido:origem pedido:destino"/>
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{guias.listaOsVig}" />
                            </p:autoComplete>

                            <p:outputLabel for="destino" value="#{localemsgs.Destino}:" rendered="#{guias.tipoPedido eq 'recolhimento' or !login.transpCacamba}"/>
                            <p:outputLabel for="destino" value="#{localemsgs.Origem}:" rendered="#{guias.tipoPedido eq 'suprimento' and login.transpCacamba}"/>
                            <p:autoComplete id="destino" value="#{guias.os_vig}" completeMethod="#{guias.buscarPedidos}" 
                                            minQueryLength="3" styleClass="origem" style="width: 100%" label="#{localemsgs.Origem}" disabled="#{guias.tipoPedido eq 'recolhimento'  and login.transpCacamba or guias.tipoPedido eq 'recolhimento'  and !login.transpCacamba}" forceSelection="true"
                                            scrollHeight="200" var="cont" itemValue="#{cont}" itemLabel="#{cont.NRedDst}">
                                <p:column style="width: 50%">
                                    #{cont.NRedDst}
                                </p:column>
                                <p:column>
                                    #{cont.NRed}
                                </p:column>
                                <p:watermark for="destino" value="#{localemsgs.Destino}"/>
                                <p:ajax event="itemSelect" listener="#{guias.selecionarDest}" update="pedido:origem pedido:destino"/>
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{guias.listaOsVig}" />
                            </p:autoComplete>
                        </p:panelGrid>

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="valor" value="#{localemsgs.Valor}:"/>
                            <p:inputText id="valor" value="#{guias.valor}"  style="width: 100%">
                                <p:watermark for="valor" value="#{localemsgs.Valor}"/>
                            </p:inputText>
                            <p:outputLabel for="pedidoCliente" value="#{localemsgs.Pedido} #{localemsgs.Cliente}:"/>
                            <p:inputText id="pedidoCliente" value="#{guias.pedido.pedidoCliente}"  style="width: 100%">
                                <p:watermark for="pedidoCliente" value="#{localemsgs.Pedido} #{localemsgs.Cliente}"/>
                            </p:inputText>
                        </p:panelGrid>
                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3, ui-grid-col-3, ui-grid-col-3, ui-grid-col-3"
                                     layout="grid">
                            <p:outputLabel for="data" value="#{localemsgs.Data}:"/>
                            <p:calendar pattern="#{mascaras.padraoData}" value="#{guias.data}"  converter="conversorData"
                                        required="true" 
                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}" id="data" style="width: 100%">
                                <p:watermark for="data" value="#{localemsgs.Data}"/>
                            </p:calendar>

                            <p:outputLabel for="dataEntrega" value="#{localemsgs.DataEntrega}:"/>
                            <p:calendar value="#{guias.datad}" converter="conversorData" id="dataEntrega" pattern="#{mascaras.padraoData}"
                                        class="calendariopedido" required="true"
                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataEntrega}">
                                <p:watermark for="dataEntrega" value="#{localemsgs.DataEntrega}"/>
                            </p:calendar>

                            <p:outputLabel for="hora" value="#{localemsgs.Hora}:"/>
                            <p:inputMask mask="#{mascaras.mascaraHora}" converter="conversorHora" value="#{guias.hora1o}" 
                                         id="hora" maxlength="4" style="width: 100%" required="true"
                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}">
                                <p:watermark for="hora" value="#{localemsgs.Hora}"/>
                            </p:inputMask>

                            <p:outputLabel for="horaEntrega" value="#{localemsgs.HoraEntrega}:"/>
                            <p:inputMask mask="#{mascaras.mascaraHora}" converter="conversorHora" value="#{guias.hora1d}" 
                                         id="horaEntrega" maxlength="4" style="width: 100%" required="true"
                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.HoraEntrega}">
                                <p:watermark for="horaEntrega" value="#{localemsgs.HoraEntrega}"/>
                            </p:inputMask>
                        </p:panelGrid>
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3, ui-grid-col-9" layout="grid">
                            <p:outputLabel for="obs" value="#{localemsgs.Obs}:"/>
                            <p:inputTextarea id="obs" value="#{guias.pedido.obs}" label="#{localemsgs.Obs}" style="width: 100%" rows="2" maxlength="80"/>
                            <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                        </p:panelGrid>  

                        <div class="col-md-12" style="display:inline; border:thin solid #DDD; padding:2px 2px 0px 2px !important; height:200px !important; margin-top:-7px !important">
                            <label style="position:absolute; z-index:2; top:2px; left:2px; width:calc(100% - 4px); background:linear-gradient(to bottom, #505050, #202020); text-align: center; font-weight:bold; font-size:9pt; padding:5px 6px 7px 6px; color:#FFF">#{localemsgs.Composicoes}</label>
                            <div class="col-md-11" style="display:inline; padding:0px !important;margin-top:31px; height:163px;width:calc(100% - 60px) !important;">
                                <p:panel style="overflow:hidden !important; padding:0px !important;">
                                    <p:dataTable
                                        id="tabelaCedulaMoeda"
                                        value="#{guias.listaPedidoCedulaMoeda}"
                                        rowKey="#{lista.numero}"
                                        paginator="false"
                                        paginatorTemplate="false"
                                        lazy="true"
                                        reflow="true"
                                        var="lista"
                                        selection="#{guias.pedidoCedulaMoeda}"
                                        styleClass="tabela"
                                        selectionMode="single"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        scrollable="true"
                                        class="tabela DataGrid"
                                        scrollWidth="100%"
                                        style="display: flex; flex-direction: column; font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important; background-color:#FFF !important;margin-top:-10px !important"
                                        >
                                        <p:ajax listener="#{guias.onRowComposicaoSelect}" event="rowSelect" />
                                        <p:ajax listener="#{guias.dblSelectComposicao}" event="rowDblselect" update="formCadastroComposicao:cadastrar msgs" />

                                        <p:column headerText="#{localemsgs.Tipo}" class="text-center">
                                            <h:outputText value="#{lista.tipoDesc}" class="text-center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CedulaMoeda}" class="text-center">
                                            <h:outputText value="#{lista.codigo}" converter="conversormoeda" class="text-center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Qtde}"  class="text-center">
                                            <h:outputText value="#{lista.qtde}"  class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Valor}" class="text-center">
                                            <h:outputText value="#{lista.valor}" converter="conversormoeda" class="text-center"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:panel>
                            </div>
                            <div class="col-md-1" style="display:inline; padding:0px !important;margin-top:31px; height:163px; width:58px !important; margin-left:2px; border:thin solid #DDD;background-color:#EEE;">
                                <p:panel id="botoesCadastro" style="width:100% !important; width:100%; text-align:center; margin-top:10px !important; background-color: #EEE">
                                    <p:commandLink title="#{localemsgs.Adicionar}"
                                                   partialSubmit="true" process="@this"
                                                   update="formCadastroComposicao:cadastrar tabelaCedulaMoeda msgs"
                                                   actionListener="#{guias.buttonActionComposicaoNovo}" >
                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40" />
                                    </p:commandLink>

                                    <p:commandLink title="#{localemsgs.Editar}" actionListener="#{guias.buttonActionComposicao}"
                                                   update="formCadastroComposicao:cadastrar msgs">
                                        <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="40" height="40" style="margin-top:10px;" />
                                    </p:commandLink>

                                    <p:commandLink title="#{localemsgs.Excluir}" actionListener="#{guias.excluirItemComposicao}"
                                                   update="tabelaCedulaMoeda msgs">
                                        <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="40" height="40" style="margin-top:10px;" />
                                    </p:commandLink>
                                </p:panel>
                            </div>
                        </div>

                        <div class="form-inline" style="margin-top:20px !important">
                            <p:commandLink id="cadastro" action="#{guias.cadastrarPedido}" title="#{localemsgs.Cadastrar}" update="msgs main cadastrar">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </div>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- Form cadastro de composições -->
            <h:form class="form-inline" id="formCadastroComposicao">
                <p:dialog widgetVar="dlgCadastroComposicao" positionType="absolute" responsive="true" focus="cedulaMoeda"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastroComposicao" styleClass="dialogo"
                          style="height: auto; max-height:95% !important; min-width:250px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                    <f:facet name="header">
                        <h:outputText value="#{localemsgs.Composicoes}" style="color:#022a48"/>
                    </f:facet>
                    <p:panelGrid id="cadastrar" columns="1" columnClasses="ui-grid-col-12"
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <p:outputLabel for="tipoComposicao" value="#{localemsgs.Tipo}" />
                        <p:selectOneMenu value="#{guias.pedidoCedulaMoedaNovo.tipo}" style="width: 100%" required="true"
                                         id="tipoComposicao">
                            <p:ajax event="itemSelect" update="formCadastroComposicao:cadastrar"/>
                            <f:selectItem itemLabel="#{localemsgs.Cedula}" itemValue="C" />
                            <f:selectItem itemLabel="#{localemsgs.Moeda}" itemValue="M" />
                        </p:selectOneMenu>


                        <p:outputLabel for="cedulaMoeda" value="#{guias.pedidoCedulaMoedaNovo.tipo eq 'C'? localemsgs.Cedula: localemsgs.Moeda}" />
                        <p:inputNumber id="cedulaMoeda" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.CedulaMoeda}" value="#{guias.pedidoCedulaMoedaNovo.codigo}" decimalPlaces="#{guias.pedidoCedulaMoedaNovo.tipo eq 'C'? 0: 2}" style="text-align:center !important;" maxlength="3"></p:inputNumber>

                        <p:outputLabel for="qtde" value="#{localemsgs.Qtde}" />
                        <p:inputNumber id="qtde" value="#{guias.pedidoCedulaMoedaNovo.qtde}" decimalPlaces="0" style="text-align:center !important;" maxlength="3"></p:inputNumber>

                        <p:outputLabel for="valorCedulaCadastro" value="#{localemsgs.Valor}" rendered="false" />
                        <p:inputNumber id="valorCedulaCadastro" value="#{guias.pedidoCedulaMoedaNovo.valor}" style="text-align:center !important;" rendered="false"></p:inputNumber>

                    </p:panelGrid>
                    <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                 layout="grid" styleClass="ui-panelgrid-blank" style="margin-top:8px">
                        <p:commandLink title="#{localemsgs.Salve}" id="cadastrarDieta" action="#{guias.salvarDadosComposicao}"
                                       update="pedido:tabelaCedulaMoeda msgs"
                                       style="width:100%">
                            <label class="btn btn-lg btn-success" style="width:100% !important;margin-left: 0px;"><i class="fa fa-save"></i>&nbsp;#{localemsgs.Salve}</label>
                        </p:commandLink>
                    </p:panelGrid>
                    <script>
                        $(document)
                                .on('keyup', '.ui-inputfield[id*="qtde"]', function () {
                                    ;
                                    if ($(this).val().trim() != '')
                                        $('.ui-inputfield[id*="valorCedulaCadastro"]').val('').attr('disabled', 'disabled');
                                    else
                                        $('.ui-inputfield[id*="valorCedulaCadastro"]').val('').removeAttr('disabled');
                                })
                                .on('keyup', '.ui-inputfield[id*="valorCedulaCadastro"]', function () {
                                    ;
                                    if ($(this).val().trim() != '')
                                        $('.ui-inputfield[id*="qtde"]').val('').attr('disabled', 'disabled');
                                    else
                                        $('.ui-inputfield[id*="qtde"]').val('').removeAttr('disabled');
                                });
                    </script>
                </p:dialog>
            </h:form>

            <!--Pesquisar guias -->
            <h:form id="formPesquisar">
                <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/> 
                <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                           draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                           showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                           style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">

                    <f:facet name="header">
                        <img src="../assets/img/icone_clientes.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Pesquisar}" style="color:#022a48" /> 

                    </f:facet>
                    <p:panel id="pesquisar" style="background: transparent">

                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 25%; float: left">
                                <p:outputLabel for="calendario1" value="#{localemsgs.DataInicial}:" title="#{localemsgs.DataInicial}"/>
                            </div>
                            <div style="width: 75%; float: left">
                                <p:calendar id="calendario1" styleClass="calendario" 
                                            value="#{guias.dataSelecionada1}" mask="true"
                                            title="#{localemsgs.DataInicial}" label="#{localemsgs.DataInicial}"
                                            pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"/>
                            </div>
                        </div>

                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 25%; float: left">
                                <p:outputLabel for="calendario2" value="#{localemsgs.DataFinal}:" title="#{localemsgs.DataFinal}"/>
                            </div>
                            <div style="width: 75%; float: left">
                                <p:calendar id="calendario2" styleClass="calendario"
                                            value="#{guias.dataSelecionada2}" mask="true"
                                            title="#{localemsgs.DataFinal}" label="#{localemsgs.DataFinal}"
                                            pattern="#{mascaras.getPadraoDataS()}" locale="#{localeController.getCurrentLocale()}"/>
                            </div>
                        </div>                        

                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 25%; float: left">
                                <p:outputLabel for="guia" value="#{localemsgs.Guia}"/>
                            </div>
                            <div style="width: 75%; float: left">
                                <p:inputText id="guia" value="#{guias.guiaPesquisa.guia}" label="#{localemsgs.Guia}"
                                             style="width: 100%" maxlength="60">
                                    <p:watermark for="guia" value="#{localemsgs.Guia}"/>
                                </p:inputText>
                            </div>
                        </div>

                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 25%; float: left">
                                <p:outputLabel for="lacre" value="#{localemsgs.Lacre}"/>
                            </div>
                            <div style="width: 75%; float: left">
                                <p:inputText id="lacre" value="#{guias.guiaPesquisa.lacre}" label="#{localemsgs.lacre}"
                                             style="width: 100%" maxlength="60">
                                    <p:watermark for="lacre" value="#{localemsgs.Lacre}"/>
                                </p:inputText>
                            </div>
                        </div>

                        <div class="form-inline" style="z-index:3 !important">
                            <p:commandLink id="pesquisa" action="#{guias.pesquisar}" oncomplete="PF('dlgPesquisar').hide()"
                                           update=" :main :msgs :cabecalho totais"
                                           title="#{localemsgs.Pesquisar}">
                                <p:hotkey bind="1" oncomplete="PF('dlgPesquisar').hide()"/> 
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </div>            
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="formEditarLacre" class="form-inline">
                <p:dialog widgetVar="dlgEditarLacre" id="dlgEditarLacre" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                          showEffect="drop" hideEffect="drop" closeOnEscape="false"
                          style="background-image: url('assets/img/menu_fundo.png');
                          background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_pedidos.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.EditarLacre}" style="color:black" />
                    </f:facet>
                    <p:panel id="editar" style="background-color: transparent" styleClass="editar">
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="origem" value="#{localemsgs.Origem}: " />
                            <p:inputText id="origem" value="#{guias.preOrderSelecionadoDetalhado.agenciaOri}/#{guias.preOrderSelecionadoDetalhado.subAgenciaOri} #{guias.preOrderSelecionadoDetalhado.NRed1}"
                                         disabled="true" style="width: 100%; text-align: right" />
                            <p:outputLabel for="destino" value="#{localemsgs.Destino}: " />
                            <p:inputText id="destino" value="#{guias.preOrderSelecionadoDetalhado.agencia}/#{guias.preOrderSelecionadoDetalhado.subAgencia} #{guias.preOrderSelecionadoDetalhado.NRed2}"
                                         disabled="true" style="width: 100%; text-align: right; font-weight: bold" />

                            <p:spacer/>
                            <p:inputText value="#{guias.preOrderSelecionadoDetalhado.obs}"
                                         disabled="true" style="width: 100%; text-align: right;" />

                            <p:outputLabel for="lacre" value="#{localemsgs.Lacre}: " />
                            <p:inputText id="lacre" value="#{guias.preOrderSelecionadoDetalhado.preOrderVolLacre}"
                                         disabled="true" style="width: 100%; text-align: right" />

                            <p:outputLabel for="novoLacre" value="#{localemsgs.NovoLacre}: " />
                            <p:inputText id="novoLacre" value="#{guias.novoLacre}" style="width: 100%; text-align: right" />
                        </p:panelGrid>

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:commandLink actionListener="#{guias.editarLacre}" 
                                           title="#{localemsgs.Editar}" update="msgs">     
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <p:ajaxStatus onstart="PF('pfBlock').show();" oncomplete="PF('pfBlock').hide();">
                <p:dialog  widgetVar="pfBlock" header="#{localemsgs.ProcessandoAguarde}" modal="true"
                           draggable="false" closable="false" resizable="false">  
                    <p:progressBar widgetVar="progressoIndeterminado" id="progressoIndeterminado"
                                   styleClass="progresso" mode="indeterminate"/>
                </p:dialog>
            </p:ajaxStatus>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="ui-grid ui-grid-responsive">
                        <div id="divCorporativo" style="position:absolute; bottom:29px; left:5px; max-height:10px !important;">
                            <p:panel id="totais" style="background-color:transparent !important; width:100%">
                                <div class="ui-grid-col-12" style="font-weight: bold; padding-top: 2px !important; color:#FFF !important;">
                                    #{localemsgs.QtdGuias}: #{guias.total}
                                </div>
                                <div class="ui-grid-col-12" style="font-weight: bold; padding-top: 2px  !important; color:#FFF !important;">
                                    #{localemsgs.ValorGuias}: <h:outputText value="#{guias.valorGuias}" converter="conversormoeda"/>
                                </div>
                                <div class="ui-grid-col-12" style="font-weight: bold; padding-top: 2px  !important; color:#FFF !important;">
                                    #{localemsgs.QtdVolumes}: #{guias.volumesGuias}
                                </div>
                            </p:panel>
                        </div>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important; margin-top:5px !important;">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td style="#{login.usuario.nome eq null or login.usuario.nome eq ''?'display:none':''}">#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <!-- JavaScript para controlar filtros do extrato -->
            <script type="text/javascript">
            //<![CDATA[
                function toggleFiltrosExtratoJS() {
                    var painel = document.getElementById('painelFiltrosExtrato');
                    var botao = document.getElementById('botaoMostrarFiltros');

                    if (painel && botao) {
                        if (painel.style.display === 'none' || painel.style.display === '') {
                            painel.style.display = 'block';
                            botao.style.display = 'none';
                        } else {
                            painel.style.display = 'none';
                            botao.style.display = 'block';
                        }
                    }
                }

                function limparFiltrosExtratoJS() {
                    // Limpar os campos de filtro
                    var campos = ['filtroNF', 'filtroPracaExtrato', 'filtroGuia', 'filtroSerie'];
                    campos.forEach(function(id) {
                        var campo = document.getElementById('main:mainTabView:' + id);
                        if (campo) {
                            campo.value = '';
                        }
                    });
                }

                // Garantir que o estado inicial esteja correto quando a página carrega
                document.addEventListener('DOMContentLoaded', function() {
                    // Aguardar um pouco para garantir que os componentes estejam renderizados
                    setTimeout(function() {
                        var painel = document.getElementById('painelFiltrosExtrato');
                        var botao = document.getElementById('botaoMostrarFiltros');

                        if (painel && botao) {
                            // Verificar o estado inicial baseado na visibilidade do painel
                            var painelVisivel = painel.style.display !== 'none';

                            if (painelVisivel) {
                                botao.style.display = 'none';
                            } else {
                                painel.style.display = 'none';
                                botao.style.display = 'block';
                            }
                        }
                    }, 500);
                });

                // Funções para controlar filtros das Notas Fiscais
                function toggleFiltrosNFJS() {
                    var painel = document.getElementById('painelFiltrosNF');
                    var botao = document.getElementById('botaoMostrarFiltrosNF');

                    if (painel && botao) {
                        if (painel.style.display === 'none' || painel.style.display === '') {
                            painel.style.display = 'block';
                            botao.style.display = 'none';
                        } else {
                            painel.style.display = 'none';
                            botao.style.display = 'block';
                        }
                    }
                }

                function limparFiltrosNFJS() {
                    // Limpar os campos de filtro das NFs
                    var campos = ['filtroNumeroNF_NF', 'filtroTipoServicoNF'];
                    campos.forEach(function(id) {
                        var campo = document.getElementById('main:mainTabView:' + id);
                        if (campo) {
                            campo.value = '';
                        }
                    });
                }
            //]]>
            </script>

            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>