package br.com.sasw.managedBeans;

import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.GTVeAcesso;
import SasBeans.Pessoa;
import SasDaos.ClientesDao;
import SasDaos.PessoaDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import java.io.Serializable;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;

/**
 * Managed Bean para confirmação de senha de novos usuários
 * <AUTHOR>
 */
@Named(value = "confirmarSenha")
@ViewScoped
public class ConfirmarSenhaMB implements Serializable {
    
    private String chaveAcesso;
    private String senhaTemporaria;
    private String novaSenha;
    private String confirmarNovaSenha;
    private boolean senhaConfirmada;
    private GTVeAcesso gtveAcesso;
    private Pessoa pessoa;
    
    private Persistencia persistenciaLocal;
    private Persistencia persistenciaCentral;
    
    public ConfirmarSenhaMB() {
        this.senhaConfirmada = false;
    }
    
    /**
     * Método chamado ao carregar a página
     */
    public void inicializar() {
        try {
            // Inicializar persistências usando o pool
            SasPoolPersistencia pool = new SasPoolPersistencia();

            // Buscar parâmetro da chave para determinar qual banco usar
            if (this.chaveAcesso != null && !this.chaveAcesso.trim().isEmpty()) {
                // Primeiro conectar ao satellite para buscar a chave
                this.persistenciaCentral = pool.getConexao("SATELLITE");

                // Buscar informações da chave para determinar o banco local
                ClientesDao clientesDao = new ClientesDao();
                List<GTVeAcesso> chaves = clientesDao.buscarChaveAcessoPorChave(this.chaveAcesso, this.persistenciaCentral);

                if (chaves != null && !chaves.isEmpty()) {
                    String parametro = chaves.get(0).getParametro();
                    this.persistenciaLocal = pool.getConexao(parametro);
                }
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Erro ao inicializar página: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }
    
    /**
     * Confirmar senha e ativar usuário
     */
    public void confirmarSenha() {
        try {
            // Validar campos
            if (!validarCampos()) {
                return;
            }
            
            // Buscar chave de acesso na base central
            if (!buscarChaveAcesso()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                    "Chave de acesso não encontrada ou inválida", null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                return;
            }
            
            // Buscar pessoa na base local pela chave
            if (!buscarPessoaLocal()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                    "Usuário não encontrado", null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                return;
            }
            
            // Validar senha temporária
            if (!validarSenhaTemporaria()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                    "Senha temporária incorreta", null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                return;
            }
            
            // Atualizar senha na base local
            atualizarSenhaLocal();
            
            // Marcar como confirmado
            this.senhaConfirmada = true;
            
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, 
                "Senha confirmada com sucesso!", null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                "Erro ao confirmar senha: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }
    
    /**
     * Validar campos do formulário
     */
    private boolean validarCampos() {
        if (this.chaveAcesso == null || this.chaveAcesso.trim().isEmpty()) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                "Chave de acesso é obrigatória", null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            return false;
        }
        
        if (this.senhaTemporaria == null || this.senhaTemporaria.trim().isEmpty()) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                "Senha temporária é obrigatória", null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            return false;
        }
        
        if (this.novaSenha == null || this.novaSenha.trim().isEmpty()) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                "Nova senha é obrigatória", null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            return false;
        }
        
        if (this.confirmarNovaSenha == null || this.confirmarNovaSenha.trim().isEmpty()) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                "Confirmação de senha é obrigatória", null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            return false;
        }
        
        if (!this.novaSenha.equals(this.confirmarNovaSenha)) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                "Nova senha e confirmação não conferem", null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            return false;
        }
        
        return true;
    }
    
    /**
     * Buscar chave de acesso na base central
     */
    private boolean buscarChaveAcesso() throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            List<GTVeAcesso> chaves = clientesDao.buscarChaveAcessoPorChave(this.chaveAcesso, this.persistenciaCentral);
            
            if (chaves != null && !chaves.isEmpty()) {
                this.gtveAcesso = chaves.get(0);
                return true;
            }
            return false;
        } catch (Exception e) {
            throw new Exception("Erro ao buscar chave de acesso: " + e.getMessage());
        }
    }
    
    /**
     * Buscar pessoa na base local
     */
    private boolean buscarPessoaLocal() throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            this.pessoa = pessoaDao.buscarPorCodPessoaWEB(this.gtveAcesso.getCodPessoa(), this.persistenciaLocal);

            return this.pessoa != null;
        } catch (Exception e) {
            throw new Exception("Erro ao buscar pessoa local: " + e.getMessage());
        }
    }
    
    /**
     * Validar senha temporária
     */
    private boolean validarSenhaTemporaria() {
        return this.pessoa.getPWWeb() != null && this.pessoa.getPWWeb().equals(this.senhaTemporaria);
    }
    
    /**
     * Atualizar senha na base local
     */
    private void atualizarSenhaLocal() throws Exception {
        try {
            this.pessoa.setPWWeb(this.novaSenha);
            this.pessoa.setOperador("CONFIRMACAO");
            this.pessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.pessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
            
            PessoaDao pessoaDao = new PessoaDao();
            pessoaDao.atualizaSenhaSatMob(this.pessoa, this.persistenciaLocal);
            
        } catch (Exception e) {
            throw new Exception("Erro ao atualizar senha: " + e.getMessage());
        }
    }
    
    /**
     * Redirecionar para página de login
     */
    public String irParaLogin() {
        return "/index.xhtml?faces-redirect=true";
    }
    
    // Getters e Setters
    public String getChaveAcesso() {
        return chaveAcesso;
    }
    
    public void setChaveAcesso(String chaveAcesso) {
        this.chaveAcesso = chaveAcesso;
    }
    
    public String getSenhaTemporaria() {
        return senhaTemporaria;
    }
    
    public void setSenhaTemporaria(String senhaTemporaria) {
        this.senhaTemporaria = senhaTemporaria;
    }
    
    public String getNovaSenha() {
        return novaSenha;
    }
    
    public void setNovaSenha(String novaSenha) {
        this.novaSenha = novaSenha;
    }
    
    public String getConfirmarNovaSenha() {
        return confirmarNovaSenha;
    }
    
    public void setConfirmarNovaSenha(String confirmarNovaSenha) {
        this.confirmarNovaSenha = confirmarNovaSenha;
    }
    
    public boolean isSenhaConfirmada() {
        return senhaConfirmada;
    }
    
    public void setSenhaConfirmada(boolean senhaConfirmada) {
        this.senhaConfirmada = senhaConfirmada;
    }
}
