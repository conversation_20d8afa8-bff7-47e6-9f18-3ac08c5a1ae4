Portugues=Portuguese
Ingles=English
Salve=Save
Nnacionalidade=Nationality
Titulo=JSF
Senha=Password
Saudacao=Welcome to SatMob
Manual=Guide
PrimeiroAcesso=First access
ValidarCC=Validate paycheck
CandidateSe=Apply
EsqueciSenha=I forgot the password
AcessoSistema=System access
Espanhol=Spanish
Entrar=Login
Voltar=Back
Empresa=Company
SelecioneEmpresa=Select a Company
Usuario=User
FolhaPonto=Point Sheet
ContraCheque=Paycheck
TrocarSenha=Change Password
SelecionePeriodo=Select a Period
SelecioneData=Select a Date
DigiteSenha=Enter a Password
DigiteSenhaNovamente=Enter the Password Again
Inicio=Home
PropostaEmprego=Job Offer
SenhaAtual=Current Password
NovaSenha=New Password
confirmarNovaSenha=Confirm New Password
RecuperarSenha=Recover Password
FalhaCarregarEmpresas=Failed to load Companies
UsuarioPermissaoAcessarFiliais=User not allowed to access branches
UsuarioPermissaoContateResponsavelSistema=User without permission, please contact your system
UsuarioInativo=Inactive User
SenhaIncorreta=Wrong Password
EmailInvalido=Invalid Email
dadosNaoConfereTenteNovamente=Data does not match, try again
emaiInexistenteUsuarioInativo=Email nonexistent, or Invalid user
SenhaAlteradaSucesso=Password successfully changed
SenhaAtualNaoConfere=Current password does not match
camposSenhaConfirmarSenhaPrecisamIguais=Fields: Password and Confirm Password must match
SenhaCadastradaSucesso=Password registered successfully
UsuarioPossuiAcessoFavorRecuperarSenha=User already has access, please recover password
CodigoPreenchidoErro=Wrong code
Filial=Branch
SelecioneFilial=Select a Branch Office
SemSenha=No password, click on First Access
Cagedonline=CAGED Online
Caged=CAGED
RecursosHumanos=Humam Resources
FGTS=FGTS
SeguroDesemprego=Unemployment Insurance
Tesouraria=Treasury
Cofre=Vault
CadastrarPessoa=Person Registration
Idioma=Language
UsuarioEmpresa=User/Companies
Fechar=Close
Adicionar=Add
Gerar=Generate
Novo=New
AlterarSenha=Change password
ExcluidoSucesso=Successfully deleted
UsuarioSenha=Users/Passwords
UsuarioFiliais=User/Branches
Filtrar=Filter
RemoveFiltro=Remove
login.falhageral<message>login.usuarioerrado=User Not Found
login.usuariosemacessocadastrado=User Without Access
Operacoes=Operations
Configuracoes=Configuration
Cadastros=Registration
Relatorios=Reports
Sair=Logout
EscalaDia=Day Scale
Supervisao=Supervision
Pessoas=People
Funcionarios=Employees
Clientes=Customer
PostoServicos=Service Sites
Filiais=Branches
Usuarios=Users
Importador=Importer
Exportador=Exporter
CodFil=Branch
Secao=Code
CodCli=CliCode
Local=Location
Situacao=Situation
Dt_Situacao=Situation date
InterfExt=InterfExt
Operador=User
Dt_Alter=Change date
Hr_Alter=Change time
Cadastrar=Register
Editar=Edit
PstServ=Service Sites
Funcion=Employee
Matr=Registry
Nome=Name
CPF=SSN
RG=ID
Dt_Nasc=Birth date
Dt_Admis=Admission date
CodPessoaWeb=WebPersonCode
Insc_Munic=County Registration
Excluir=Delete
arquivoCliente=Customer's File
Obrigatorio=Missing mandatory field 
ValorAtual=Current amount
NovoValor=New amount
Nome_Guer=Nickname
Posto=Station
Dt_Situac=Date of the situation
NRed=ShortName
Ende=Address
Bairro=District
Cidade=City
Estado=State
CEP=ZIP Code
Fone1=Phone1
Fone2=Phone2
CGC=FEIN
IE=IE
Latitude=Latitude
Codigo=Code
Longitude=Longitude
Pesquisar=Search
Cliente=Client
Data=Date
Hora=Time
Supervisoes=Supervisions
Detalhes=Details
Distancia=Distance
Historico=History
TipoPosto=Type
Resp=Answer
Qst=Question
Descricao=Description
NomeCliente=Client's Name
TipoPostoDesc=Type Description
RazaoSocial=Company Name
Cadastro=Registration
UF=ST
QtdEntrevistas=Number of Interviews
Qtd=#INTW
SupervisoesRecentes=Recent Supervisions
Entrevistas=Interviews
Checklist=Checklist
Galeria=Galery
Funcion=Employee
Supervisor=Supervisor
Horario=Time
Agencia=Agency
NivelOP=OPLevel
NomeCompleto=Full Name
Motivo=Reason
Grupo=Group
Confirmacao=Confirmation
Pessoa=Person
Grupos=Groups
Permissoes=Permissions
Nivel=Level
Login=Login
Operacao=Operation
Manutencao=Maintenance
Gerencia=Management
Administrador=Administrator
Ativo=Active
Bloqueado=Blocked
RGOrg=Department
Obs=Note
Funcao=Function
Sexo=Gender
Sistema=System
Inclusao=Inclusion
Alteracao=Change
Exclusao=Exclusion
Altura=Height (cm)
Candidato=C - Candidate
Prestador=P - Service provider
Autonomo=A - Autonomous
Funcionario=F - Employee
Diretor=D - Director
Socio=S - Partner
Visitante=V - Visitor
BBloqueado=B - Blocked
Visitanteweb=W - Web visitor
Selecione=Select
Masculino=Male
Peso=Weight
Feminino=Female
OOutros=O-Not Declared
CadastrarCliente=Customer registration
PessoaJuridica=Company
PessoaFisica=Person
EditarFuncion=Edit Staff
EditarFilial=Edit Branch
CadastrarFuncion=Employee Registration
CadastrarCliente=Client Registration
EditarPessoa=Edit People
EditarCliente=Edit Client
EditarUsuario=Edit User
CadastrarUsuario=User Registration
CadastrarFilial=Branch Registration
QtdFotos=Number of Photos
Endereco=Address
RotasSup=Supervision Routes
Rota=Route
Hr_Largada=Start time
Hr_Chegada=Arrival time
Hr_IntIni=Start time interval
Hr_IntFim=End time Interval
Hr_Total=Total amount of hours
Viagem=Travel
ATM=ATM
Bacen=Treasury
Aeroporto=Airport
Sequencia=Sequence
Flag_Excl=Exclusion flag
TpVeic=Vehicle type
Intervalo=Interval
Selecionar=Select
Parada=Stop
Hora1=Hour1
ER=E-Delivery R-Pickup
CodCli1=Code customer 1
Regiao=Region
CodCli2=Code customer 2
DPar=Destination stop
Hora1d=Hour destination 1
Valor=Amount
Pedido=Order
OperIncl=Inclusion user
Dt_Incl=Date added
Hr_Incl=Time added
OperExcl=User deleted
Dt_Excl=Deleted date
Hr_Excl=Deleted time
Trajetos=Paths
Calcular=Calculate
CadastrarRota=Route Registration
FotosLocal=Photos of the Location
Tipo=Type
Mapa=Map
Contrato=Contract
#Trajeto
CadastrarTrajeto=Path Registration
AtualizarCEP=Would you like to update the address?
Sim=Yes
Nao=No
QtdPostos=Number of Sites
CadastrarPstServ=Service Station Registration
PesquisarPstServ=Site Search
QtdSupervisoes=Number of Supervisions
SelecionePstServ=Select a Sevice Site
SemSupervisaoRecente=No forward supervisions
SemSupervisaoAntiga=No previous supervisions
SelecioneSupervisao=Select a Supervision
SupervisaoAnterior=Previous Supervision 
SupervisaoPosterior=Next Supervision
QtdUsuarios=Number of Users
QtdPessoas=Number of People
QtdClientes=Number of Clients
QtdFuncion=Number of Employees
QtdRotas=Number of Routes
AdicionarFilial=Add Branch
Email=Email
SenhaFraca=Weak password
SenhaBoa=Good password
SenhaForte=Strong password
SenhaSomenteNumerosMinimoSeis=The password must have only numbers and at least 6 digits
Corporativo=Corporative
PesquisarFuncion=Employee Search
TbVal=Questions
QtdTbVal=Number of Questions
QtdFiliais=Number of Branches
MostrarTodasFiliais=Show all branches
SomenteAtivos=Active Only
PesquisarCliente=Client Search
PesquisarPessoa=Person Search
PesquisarUsuario=User Search
OK=OK
CompletarEndereco=Automatic address, please complete with addtional information.
Aviso=Warning
desejaSeguir=Proceed with this action?
SemEnderecoCadastrado=No Registered Address
EdicaoSucesso=Successful Changes
CadastroSucesso=Successful registration
SelecioneCliente=Select a Client
SelecioneFuncion=Select an Employee
SelecioneSituacao=Select a Situation
SelecionePessoa=Select a Person
SelecionePermissao=Select a Permission
SelecioneUsuario=Select an User
CPFInvalido=Invalid SSN
FuncionarioCadastrado=Employee Already Registered
MatriculaInvalida=Invalid Registry
EnderecoNaoEncontrado=Address Not Found
CNPJInvalido=Invalid FEIN
ClienteNaoEncontrado=Client not found
PessoaInvalida=Invalid Person
tipoServ=Service type
filialRota=Branch of the route
ImpossivelConectarSatellite=Not abble to connect to Satellite
LimparFiltros=Remove Filters
login.falhageral<message>login.senhaerrada=Wrong Password
ImpossivelConectarBanco=Not abble to connect to
AlturaSemVirgula=Comma not allowed
PesoSemVirgula=Comma not allowed
filialRota=Branch of the route
secao=Section
AdicionarPermissoesGrupo=Add all permissions to the group
javax.faces.validator.RegexValidator.NOT_MATCHED=The password must contain only numbers and at least 5 characters
primefaces.password.INVALID_MATCH=Password don't match
QtdPermissoes=Number of Permissions
SemRegistros=No Records Found
ExibirSenha=Show password
Placa=License plate
Hora2=Hour2
Hora3=Hour3
Hora4=Hour4
hs_interv=Hs_Break
QtdEscalas=Number of Scales
CadastrarEscala=Scale Registration
Periodo=Period
PesquisarFilial=Branch Search
FilialInvalida=Invalid Branch
senhaMobile=Mobile password
permissaoRotas=Routes permissions
ProximaFoto=Next Photo
Foto=Photo
De=out of
FotoAnterior=Previous Photo
SemMaisFotosInicio=No More Photos
SemMaisFotosFim=No More Photos
NaoSeAplica=Not applicable
Concluido=Done
Pendente=Pending
EmAndamento=In progress
Demitido=Fired
Inativo=Inactive
PesquisarSupervisao=Supervision Search
DataInvalida=Invalid Date
a=to
numero=Number
SelecioneRota=Select a Route
SelecioneTrajeto=Select a Path
ExclusaoSucesso=Successfully deleted
ExcluirRota=Delete this route?
ExcluirTrajeto=Delete this path?
VerifiqueHorario=Check the time
SemRotas=There are no routes avaliable
numero=Number
Escala=Scale
excluirEscala=Deleting the scale will make you lose all the information about it 
naoListar=It was not possible to find the data
Assinado=Signed 
Guia=Receipt
listaguaistitulo=Receipt list
Volume=Volume
ExcluirEscala=Delete this scale?
SelecioneEscala=Select a Scale
QtdGuias=Number of Receipts
Serie=Series
TempoEspera=Waiting time
EscalaExistente=Scale already exists
RotaInexistente=This route is missing for this date
CadastrarQuestoes=Questions Registration
CadastrarQuestao=Question Registration
Questoes=Questions
SelecioneQuestao=Select a Question
ExcluirQuestao=Delete this question?
ExisteServico=There is a job already at this hour
HoraInvalida=The hour typed is invalid
IntervaloInvalido=The interval typed is invalid
GTV=Eletronic Receipt
DiaSemana=Day of the week
$=US$
Segunda=Monday
Ter\u00e7a=Tuesday
Quarta=Wednesday
Quinta=Thursday
Sexta=Friday
Sabado=Saturday
Domingo=Sunday
DetalhesGuia=Receipt details
Lacre=Seal
Guias=Receipts
SelecioneGuia=Select a Receipt
AdicionarCliente=Add a Client
trajetoExcluido=Path already deleted
exclusaoVisivel=Visible exclusions
escalaExcluida=Scale deleted
VerTodos=See all
DataInicial=Start Date
DataFinal=End Date
Hr_Saida=Departure Time
EscolherOpcao=Select an option
TrocarCliente=Switch Clients
Cedulas=Banknotes
Cheques=Bank Check
Moedas=Coins
MetaisPreciosos=Precious Metal
MoedaExtrangeira=Foreign Currency
Outros=Others
Exportar=Export
AssistenciaTecnica=Technical Assistance
Entrega=Delivery
Recolhimento=Pick up
ExibirExcluidos=Show deleted
escolhaTipoArquivo=Choose the type of the file
pdf=PDF
xls=XLS
html=HTML
xml=XML
nomeArquivo=File name
erroExportar=Error exporting
pagina=Page
de=of
Origem=Origin
Destino=Destination
QtdVolumes=Number of Volumes
ValorGuias=Total Value of Receipts
SATELLITE=SATELLITE
SASW=SASW
SAS=SAS
SATLOYAL=LOYAL
SATTRANSVIG=TRANSVIG
SATINVLMT=INVIOLAVEL MT
CONFEDERAL=TRANSFEDERAL
CONFEDERALGO=TRANSFEDERAL GO
SATCONFEDERALBSB=CONFEDERAL
SATCONFEDERALGO=CONFEDERAL GO
SATCORPVSPE=CORPVS PE
SATTRANSVIP=TRANSVIP
SATPRESERVE=PRESERVE
VSG=VSG
SATTSEG=TECNOSEG
SATAGIL=AGIL
AGILSERV=AGIL SERV
SATAGILVIG=AGIL VIG
SATAGILCOND=AGIL COND
SATINVLRS=INVIOLAVEL RS
SASEX=SASEX
SATGSI=GSI
SATTRANSEXCEL=TRANSEXCEL
SATRODOB=RODOBAN
SATRODOBAN=RODOBAN
SATTAMEME=TAMEME
SATCOMETRA=COMETRA
SATSASEX=SASEX
EAGSATI=EAGLE
EAGSAS=EAGLE
CofreInteligente=Smart Vault
TotalCreditos=Day Total Credits
CreditoDia=Day Credit
CreditoProxDU=Next day credit
ChefeEquipe=Team Leader
ValorRecolhido=Amount Collected
SaldoCustoDia=Balance Cost Day
SaldoCofre=Safe Balance
TotalVlrRecolhido=Total Amount Collected
TotalSalCustDia=Total Balance Cost Day
TotalSaldoCofre=Total Balance Safe
SelecioneCofre=Select a Vault
Feriado=Holiday
ValorRecD0=Amount Collected D0
HoraRecD0=Hour Collected D0
DepDiaAntAposCorte=Deposit Previous Day After Cutting
ValorCorteD0=Cut Value D0
TotalCredDia=Total Credit Day
ValorRecDia=Amount Collected Day
ValorRecJaCreditado=Amount Collected Already Credited
ValorRecACreditar=Amount Collected on Credit
SaldoCofreTotal=Total Safe Balance
DepositoJaCreditado=Deposit already credited
DepositoProxDU=Deposit next business day
SaldoFisCst=Physical balance Custody
SenhaDia=Type the day password 
Mostrando=Showing
SenhaSucesso=Password updated!
Arquivo=File
Cancelar=Cancel
Enviar=Upload
ArrasteArquivo=Drag the file or search
ApenasXLS=Only the template file
QtdArquivosInvalida=Number of files exceeded
SATCORPVS=CORPVS
Buscar=Search
ConfirmarSaida=Are you sure you want to quit? All unsaved progress will be lost.
SelecioneArquivo=Select a File
PossuoCodigo=I Have a Code
SatMOB=SatMOB
Template=Template
UsuarioSemAutorizacao=User without authorization. To change the password, contact your system administrator.
#tradu\u00e7\u00e3o do google translate
DicaDownload=Download the template file, fill in the values of the fields and upload it to import the new value.
DadosOrigem=Sender's Info
Remetente=Sender
Veiculo=Vehicle
Chegada=Arrival
Saida=Departure
DadosDestino=Recipient's Info
Destinatario=Recipient
DiscriminacaoValorIdentificacao=Discrimination, value and identification of cargo
ValorDeclarado=Declared value
IdentificacaoMalote=Bag Identification
Lacres=Seals/Bag Serial
Composicoes=Compositions
TermoAssGuia=We receive the volumes quoted in this Receipt, declare and acknowledge as deliveries without a trace of violation, in perfect conditions, and especially intact the respective security seals - numbered seals/bag serial described.
AssRemetente=Sender's Signature
AssDestinatario=Recipient's Signature
InscEstadual=State Registration
OS=SO
GETV=ELETRONIC MANIFEST
Telefone=Phone Number
QtdParadas=Number of Stops
CamposExportacao=Select the fields you want to export
CodPessoa=PersonCode
Postos=Sites
Imprimir=Print
FuncaoIndisponivel=Unavailable Option
Opcoes=Options
ImportacaoCompleta=Import Done
ArquivoInvalido=Invalid File
ImportacaoCompletaComErros=Import complete with errors
ImportadosSucesso=successfully imported
ImportadasSucesso=successfully imported
ErrosImportacao=Import errors
Erros=Erros
ArrasteAqui=Drag and drop the file here
Escalas=Scales
ViaImportacao=Entry by import
BuscarPeriodo=Search by Period
Cofres=Safes
BemVinda=Welcome
FolhaDePonto=Time Sheet
Contracheque=Paycheck
FuncoesAdm=Administrative Tools
AssinarGTV=Sign GTV-e
PortalRH=portal time
Matricula=Registration Number
Mensagens=Messages
ListaDatas=List Dates
BemVindo=Welcome
RGn=RG (only digits)
CPFn=CPF (only digits)
Acesso1=1st Access
Dt_Nascn=Birth date
CidadeRes=City of residence
Contatos=Contacts
QtdContatos=Number of Contacts
NomeFantasia=Fantasy Name
PesquisarContato=Contact Search
CadastrarContato=Contact Registration
IndicacaoCliente=Customer Indication
HrRecDia=collected time day
IndicacaoFornecedor=Supplier Indication
Propaganda=Ads
JornaisRevistas=News and Magazines
Internet=Internet
ProspeccaoComercial=Commercial Prospecting
ContatoViaSac=Customer Service
CapturaMobile=Mobile Capture
TpCli=Client Type
PAB=PAB
TA=TA
TAC=TAC
OutrasTransp=Other Companies
Prospect=Prospect
AguardandoContato=Awaiting Contact
ApresentacaoServicos=Presentation of Services
Negociacao=Negotiation
PilotoServicos=In Service Pilot
FormalizacaoContratual=On Contract Formalization
ClienteAtivo=Active Client
Contato=Contact
login.senhaerrada=Wrong Password
SemFolhasDePonto=No Time Sheets
RH=Human Resources
InformeRendimentos=Income Report
DadosPessoais=Personal Data
Documentos=Files
Dados=Data
CodigoInvalido=Invalid Code
Comercial=Commercial
Arquivos=Files
ModificadoEm=Modified in
ExcluirDocumento=Delete this document?
CadastrarProdutos=Products Registration
Aplicacao=Application
Preco=Price
Produtos=Products
QtdProdutos=Number of Products
PesquisarProduto=Product Search
SelecioneProduto=Select a Product
Numero=Number
Propostas=Proposals
QtdPropostas=Number of Proposals
Referencia=Reference
Consultor=Consultant
Validade=Expiration
DtValidade=Expiration Date
ProdutosSelecionados=Selected Products
Status=Status
Tickets=Tickets
Responsavel=Responsible
Fazer=To Do
FilaPTeste=Test Queue
Teste=Test
Implantar=Implant
Feito=Done
Desenvolver=Develop
CadastrarTicket=Ticket Registration
QtdTickets=Number of Tickets
Ordem=Sequence
PesquisarTickets=Tickets Search
MarcarDesmarcar=Check/Uncheck All
Ticket=Ticket
CadastrarProposta=Proposal Registration
ValorParcial=Partial Value
Produto=Product
Quantidade=Quantity
ArrasteOuAdicione=Drag and Drop or hit the + button to add an item
FormasPagamento=Form of Payment
Comentarios=Comments
AdicionarComentario=Add a Comment
SalveAlteracoes=Save your changes before proceeding.
Descontos=Discounts
ResumoProposta=Proposal Summary
Total=Total
Subtotal=Subtotal
CustosAdicionaisDescontos=Additional costs / Discounts
SemFoneContato=No phone # registered.
PrecoAjustado=Adjusted Price
MAY=MAY
APRIL=APRIL
JANUARY=JANUARY
FEBRUARY=FEBRUARY
MARCH=MARCH
JUNE=JUNE
JULY=JULY
AUGUST=AUGUST
SEPTEMBER=SEPTEMBER
OCTOBER=OCTOBER
NOVEMBER=NOVEMBER
DECEMBER=DECEMBER
SATQUALIFOCO=QUALIFOCO
PrazoEntregaImplantacao=Deliver Time/Implantation
Garantia=Warranty
Outro=Other
Opcao=Option
CondicoesPagamento=Payment Conditions
DeveConter=must contain
Digitos=digits
SenhaJaCadastrada=Password already registered.
SenhaInvalida=Invalid password. Try a different one
SenhaNumerica6Digitos=New password must contain only numbers and 6 digits.
DigiteASenha=Type your passowrd
SelecioneTicket=Select a Ticket
ExportarProposta=Export Proposal
Modelo=Model
NomeArquivo=File Name
ArquivoNaoEncontrado=File not found
SenhaEnviadaPara=Password sent to
NaoPossuoSenhaDia=I don't have daily password
EmpresaMatricula=company@registration#
UsuarioEmailInvalio=The registered email is not valid. Please contact support.
MatriculaAcessouSistema=This registration number has already accessed the system. Log in with company@registration# and usual password.
ErroValidacao=Validation error. Try again.
Frequencia=Frequency
Faturamento=Billing
DataCompetencia=Date of Competence
ValorDeposito=Deposit Amount
TipoDeposito=Deposit Type
Entradas=enter
ConfirmarEdicaoTicket=Do you really want to change the ticket status?
SATINTERFORT=INTERFORT
SATGLOVAL=GLOVAL
ClienteP=Customer
RotasValores=CIT Routes
Autenticacao=Authentication
ValorTotal=Total
HistoricoObrigatorio=Every ticket must have a history detailing the description!
AdicionarContato=Add Contact
MeusTickets=My Tickets
Notas=Invoices
Hora1D=Hr1D
SATIBL=IBL
Ambiente=Environment
ExportarESocial=ESocial Export
Evento=Event
Homologacao=homologation
Producao=Production
ESocial=ESocial / Reinf
CadastrarESocial=eSocial Registration
CadastroInicial=Initial Registration
XMLEnvio=XML Shipping
XMLRetorno=XML Return
ProtocoloEnvio=Shipping Protocol
InicioValidade=Validity Start
Dt_Envio=Send date
Hr_Envio=Shipping Time
Dt_Retorno=Return date
Hr_Retorno=Return Time
Eventos=Events
Certificado=Certificate
QtdEventos=Number of Events
Resposta=Answer
GerarXML=generate XML
ConsultarRetorno=See Return
ConsultaPendentes=consult pending
VerificarProcessamento=Verify Processing
Dt_UltimoEnvio=Date Last Shipped
Prosseguir=Proceed
Emails=Emails
Para=To
Assunto=Subject
DeRemetente=From
Mensagem=Message
ReenviarEmail=Send Email Again
SelecioneEmail=Select an Email
EnviarEmail=Send Email
ManifestoPara=Send manifest to
EmailFilaEnvio=Email in queue to be sent
Batidas=Check ins/Check outs
Cargo=Position
SenhasNaoConferem=Passwords don't match
Fotos=Photo
SelecioneRelatorio=Select a Report
SATPROSECUR=PROSECUR
SatMobEW=SatMobEW
MatrAut=Automatic Registration
SATSERVITE=SERVITE
NomeExisteSaspw=Username already in use
NomeExisteFuncion=Employee username already in use
PstDepen=Dependencies
SATCOGAR=COGAR
Tag=Tag
Batida=Check Ins
Dependencia=Dependency
Rondas=Tours
Relatorio=Report
Vigilante=Vigilant
Ocorrencia=Event
BuscarData=Search by Date
UsuarioNaoAutorizado=Unauthorized User
ValorTotalRotas=Total amount of all routes:
Paradas=Stops
Volumes=Volumes
HrCheg=HrArrival
HrSaida=HrDeparture
CheckIn=Supervisor Check In
Officer=Officer
DataHora=Date - Time
InicioRonda=Start of Tours
FimRonda=End of Tours
ReportEvento=Event Report
ReportRonda=Tour Report
CheckInReport=Check In Report
CheckOutReport=Check Out Report
QtdRelatorios=Number of Reports
CheckOut=Supervisor Check Out
UsuarioSemClientes=User without authorized clients
InicioAfastamento=Departure Home
Identificador=Identifier
Validacao=Validation
MuitosEventosValidar=Too many events to validate, sending automatically.
RelatorioPublico=Public Report
RelatorioTornadoPublico=Report made public
RelatorioTornadoPrivado=Report made private
Ronda=Tour
Conformidade=Compliance
Completas=Complete
NomeTag=Tag Name
OrdemLeitura=Order Read
TagTour=Tag # in Tour
Indicacao=Indication
Dt_FormIni=Initial formation date
Dt_FormFim=Final formation date
LocalForm=formation Location
NCertificado=N\u00ba Certified
Dt_Recicl=Recycle Date
Dt_VenCurs=Expiration Date Course
Reg_PF=Record PF
Reg_PFUF=UF
Reg_PFDt=registration date
CNH=license
CNHDtVenc=expiration date license
ExtensoesTV=Extensions CIT
ExtTV=Transport
ExtSPP=personal safety extension
ExtEscolta=Escort
DadosFormacao=Training Data
ClockIn=Clock In
ClockOut=Clock Out
ClockOutReport=Clock Out Report
ClockInReport=Clock In Report
Deletar=Delete
AdicionarEmail=Add E-mail
EmailCadastrado=E-mail already registered
ClienteNaoCadastrado=Unregistered Client!
Pergunta=Question
SimNao=Yes/No
Texto=Text
CapturaVideo=Video Capture
CapturaFoto=Photo Capture
AdicionarInspecao=Add Inspection
Inspecoes=Inspections
SelecioneInspecao=Select an Inspection
PstInspecao=Station Inspections
QtdPstInspecao=Number of Inspections
RelatorioInspecao=Inspection Report
Inspecao=Inspection
Inspecionado=Inspected
Assinatura=Signature
ClockInClockOut=Clock In/Clock Out
RelatorioSupervisor=Supervisor Report
CheckInCheckOut=Supervisor Check In/Check Out
Remover=Delete
A3=A3
SenhaIndisponivelProcureRH=Function currently unavailable. Contact your Human Resources department to change the password.
Chamado=Call
Recusar=Refuse
Aceitar=accept
descricao=description
detalhe1=Detail 1
detalhe2=Detail 2
detalhe4=Detail 4
detalhe3=Detail 3
detalhe5=Detail 5
detalhe6=Detail 6
detalhe7=Detail 7
detalhe8=Detail 8
detalhe9=Detail 9
detalhe10=Detail 10
detalhe11=Detail 11
detalhe12=Detail 12
detalhe13=Detail 13
detalhe14=Detail 14
detalhe15=Detail 15
detalhe16=Detail 16
detalhe17=Detail 17
detalhe18=Detail 18
detalhe19=Detail 19
detalhe20=Detail 20
detalhe21=Detail 21
detalhe22=Detail 22
detalhe23=Detail 23
detalhe24=Detail 24
detalhe25=Detail 25
detalhe26=Detail 26
detalhe27=Detail 27
detalhe28=Detail 28
detalhe29=Detail 29
detalhe30=Detail 30
tipoarquivo=File type
idarquivo=ID Archive
datahorageracaoarquivo=File Generation Date / Time
comunicacao=Communication
idgruposuportedemandante=ID Demanding Support Group
gruposuportedemandante=Demanding Support group
no_req=No Request
no_wo=No WO
no_inc=No Inc
no_crq=No Crq
numero_serie=Serial Number
idreq=ID Request
nomereq=request name
idfornecedor=Id Provider
prioridade=Priority
nomefornecedor=Supplier Name
categ_op_n1=Op Category n1
categ_op_n2=Op Category n2
categ_op_n3=Op Category n3
categ_prod_n1=Product Category n1
categ_prod_n2=Product Category n2
categ_prod_n3=Product Category n3
nomeproduto=Product Name
modeloproduto=Product Template
fabricante=Manufacturer
codigodobanco=Bank Code
tipounidade=Unit Type
codigounidade=Unit Code
siglaunidade=acronyms unity
nomeunidade=Name Unit
enderecounidade=Unit Address
cidadeunidade=City Unit
ufunidade=state
cep=Postal Code
idsolicitante=ID Requester
contatotelefone=Contact Phone
contatonome=Contact Name
contatoemail=Contact Email
RecusarChamado=Decline Call
AceitarChamado=Accept Call




previsaoatendimento=Service Forecast
Confirmar=Confirm
tiporetorno=Return Type
chamadofornecedor=Call by Supplier
responsavelatendimento=Responsible Service
Grafico=Graphics
Faltas=Absences
Suspensao=Suspensions
Atestado=Sick note
HorasExtras=Overtime
EvoHoras=Overtime Evolution
Horas50=Overtime 50% 
Horas100=Overtime 100% 
Compet=Competence
FaltasUltimoMes=Absences
EvoFaltas=Evolution of Absences
SuspUltimoMes=Suspensions
EvoSusp=Evolution of Suspensions
AtestadoUltimoMes=Sick Note
EvoAtestados=Evolution of sick note
Competencia=Competency
RelatorioAuditoria=User Audit Report
ControleUsuarios=Users Management
CadastrarGrupos=Groups Registration
DetalhesGrupo=Group Details
SATTRANSPORTER=TRANSPORTER
Said=Dptr
NRedFat=Billing Customer
LocalParada=Stop Location
AssinaturaDestino=Destination Signature
UploadPedido=Order Upload
ArquivosRecentes=Recent Files
Tamanho=Size
OrigemPedido=Order Source
PedidosRecentes=Recent Orders
SubAgencia=Sub Agency
ProcessandoAguarde=PROCESSING...PLEASE WAIT
SelecioneUnicoCliente=Select a single client to perform file import.
DtColeta=Date Collection
DtEntrega=Delivery date
SelecionarCliente=Select the type of client
HoraEntrega=Delivery Time
Solicitante=Requester
Pedidos=Orders
QtdPedidos=Number of Orders
classificacao=Ranking
DataEntrega=Delivery date
Servico=Service
Suprimento=Supply
MensagemImportacaoPreOrder=Were found %s1 seals for %s2 orders. Do you want to proceed with the import?
MensagemConfirmacaoPreOrder=Found orders for date %s1. Do you want to remove them and perform a new import?
ReportRota=Route Summary Report
InicioRota=Route Start
UltimoServico=Last Service
RotaPausada=Route Paused
RotaRetomada=Route Resumed
RelatorioPrestador=Service Provider Report
RotaPrestador=Service Provider Route
SATECOVISAO=ECOVISAO
TipoVeiculoInvalido=Invalid vehicle type
Forte=Armored
Leve=Car
Moto=Moto
Pesado=Truck
Aeronave=Aircraft
Nenhum=None
EntregaRecolhimento=Delivery/Pick-up
Transbordo=Transfer
Rotineiro=Routine
Eventual=Eventual
Especial=Special
AssistTecnica=Technical Assistance
Intermediaria=Intermediate
Preliminar=Preliminary
ProjetoEspecial=Special Project
ExisteTrajetoMesmoHorarioEntrega=There is another stop at the same time as delivery
SelecioneCliOri=Select source client
SelecioneCliDst=Select Destination Client
ExisteTrajetoMesmoHorario=There is another stop at the same time
HorarioDentroIntervalo=Invalid Time: Within route range
HorarioForaRota=Invalid Time: Out of route time
RecolhimentoAnteriorEntrega=Invalid Time: Pre-delivery Pickup
SemInfoCliFat=No customer information to bill
SemGuiasParaManifest=No guide information for submitting manifest
MotoristaDiferenteVeiculo=01 - Driver other than standard.
Pref=Pref
NSOP=N\u00ba SOP
RelatorioUsuarios=User Tracking Report
AtualizarGrupos=Update Groups
Motorista=Driver
ChEquipe=Team Leader
Vigilante1=Vigilant1
Vigilante2=Vigilant2
Vigilante3=Vigilant3
FuncionariosFolga=time off worker
AceitarFuncionariosFolga=Employees below off-duty locations. Do you want to proceed?
AceitarFuncionarioFolga=The employee below is off. Do you want to proceed?
SenhaMobile=Mobile Password
PermissaoRotas=Permission Routes
PessoaJaEscalada=Person already climbing
DataAnteriorEdicaoBloqueada=Pre-current date scale. Edit blocked.
MotoristaJaEscalado=Driver already climbed
ChEquipeJaEscalado=Team Leader already cast
Vigilante1JaEscalado=Vigilant 1 already climbed
Vigilante2JaEscalado=Vigilant 2 already climbed
Vigilante3JaEscalado=Vigilant 3 already climbed
HrMotInvalida=invalid driver time
PessoaNaoMotorista=This person is not a Driver
PessoaNaoChEquipe=This person is not a Team Leader
VeiculoJaEscalado=Vehicle already climbed
HrCheInvalida=invalid arrival time
HrVig1Invalida=vigilant time 1 invalidates
HrVig2Invalida=vigilant time 2 invalidates
HrVig3Invalida=vigilant time 3 invalidates
MovimentacaoContainer=Container Handling
Container=Container
HrServico=service schedule
Containers=Containers
decimalSeparator=.
thousandSeparator=,
DSEIndividual=DSE Individual
Colaborador=collaborator
DtInicio=Start date
DtFim=final date
DestinoEntrega=Destination Delivery
OrigemColeta=Origin Collection
SelecioneParada=Select stop for manifest printing
ManifestosDisponiveis=Available Manifests
SB=sub agency
Vol=Vol
HrColeta=collection time
HrEntrega=delivery time
SBDst=destination sub agency
QtdGuiasPreOrder=Amount of receipts
Graficos=Graphics
RPV=RPV
Remessa=Shipping
EditarLacre=Edit Seal
NovoLacre=New seal
NovaImportacao=New Import
Atualizar=Update
MensagemConfirmacaoPreOrders=Found orders for date %s1 in %s2 lots. Do you want to update or perform a new import?
OSRtg=OSRtg
HrPrg=HrPrg
DtEnt=DtEnt
GuiaIndisponivel=Guide not available for printing
ErroPrimeiroAcesso=Unable to finalize registration. Try to login with email and password.
Identif=Identification
Dt_Inicio=Start date
Dt_Termino=End date
CliFat=Customer to bill
ContratoCli=client contract
RefArq=reference file
GrpReajuste=readjustment group
GrpPagamento=payment group
OBS=Note
Processo=Process
Contratos=Contracts
QtdContratos=Number of Contracts
IdentificacaoCliente=Customer Identification
TipoContrato=Contract Type
ClienteContratante=Contracting Client
Identificacao=Identification
ContratoAssinado=Signed contract
Determinado=Determined
Indeterminado=Undetermined
Transporte=Transport
Geral=General
Cancelado=Canceled
MostrarSomenteAtivos=Show Assets Only
ContratosAVencer=Outstanding Contracts
login.falhageral<message>login.metodoerrado=Incorrect login form. Try to login using email and password.
EventoExcluir=Event to Exclude
login.falhageral<message>Falha\ ao\ carregar\ filiais\ -\ UsuarioSemPermissao=User without permission
NomeCli=Client Name
SelecioneContrato=Select a Contract
OutrasBases=Other Bases
TrocarFilial=Change Branch
SessaoExpirada=Session Expired
CliqueParaRedirecionar=Click here to return to the homepage.
DadosGerais=General Data
CarNacVig=National Vigilant Wallet
DtValCNV=expiry date of the national vigilant card
Mae=Mother
ExclusaoCargoSucesso=Successful deletion of position
CargoSucesso=Successfully added position
CargoPretendido=Intended position
PIS=PIS
Historicos=Historical
ColetaAntesCorte=Collect Before Cutting
Credito=Credit
RecolhimentoDepoisCorte=Gathering After Cutting
NoCofre=In the vault
ProximoDia=Next day
Custodia=Custody
PesquisarNFiscal=Search Invoice
Praca=plaza
Ativa=Active
Cancelada=Canceled
NFiscais=Invoices
QtdNFiscais=Quantity of Invoices
UploadNFiscal=Upload File XML
NRedPraca=Reduced Number Square
SelecioneNFiscal=Select an Invoice
ErroOcorrido=There was an error processing the order.
CliqueParaRecarregar=Click to reload page
Movimentacoes=Moves
Rotas=Routes
SATGETLOCK=GETLOCK
SATVANTEC=VANTEC
SATNORSERV=NORSERV
DadosCliente=Client's data
IdentificacaoContainer=Container Identification
Download=Download
PortaSuperior=Upper Door
PortaCofre=vault Door
Cassete=Cassette
Fechadura=lock
Validadora=Validator
Impressao=Printer
Fechada=Closed
Fechado=Closed
Aberta=Open
Aberto=Open
Conectada=Connected
Desconectada=Disconnected
Marca=mark
Serial=Serial
IMEI=IMEI
InfoCofre=vault Information
DepositosAte16=Deposits Up 16:00
DepositosApos16=Deposits After 16:00
Depositos=Deposits
Coletas=collect
CofresGerenciados=Managed vaults
DataLocacao=Rental date
HoraLocacao=Rental time
TempoDias=Time
TipoMovimentacao=Movement Type
Atividade=Activity
CNPJCPF=federal registry
IERG=federal registry
Retencoes=Retention
RetemImpostos=Y - Withholds taxes as per parameters
NaoFazRetencoes=N - Make no holds
AgendarColeta=Schedule Collection
Retorno=Return
Envelope=Envelope
LimiteSeguro=Safe Limit
Cheque=Check
CodCofre=Safe Code
Patrimonio=Equity Number
DadosATM=ATM Data
Interface=Interface
CodExt=Reference Code 2
CodPtoCli=Point Code
Normal=Normal
TransportadoraValores=Securities Carrier
MapaIndisponivel=Map Unavailable
CodigoRegiao=Region Code
Abrangencia=Scope
TipoLocalizacao=Location Type
Regioes=Regions
Urbano=Urban
Interurbano=Interurban
PrazoColeta=Collection Deadline
ColetasVencidas=Expired Collections
ColetasHoje=Today's Collections
ColetasVencer=Winning Collections
SemPrazoColeta=No Collection Deadline
TempoCliente=Client Time
Servicos=services
PedidoCliente=Customer Order
HoraDe=to time
Ate=until
SomenteDataSelecionada=Selected Date Only
Todos=All
Dias=Days
Mais15Dias=More Than 15 Days
ClienteServico=Customer - Service
MovimentacaoDiaria=Daily Movement
buscaMovimentacao=Movimenta\u00e7\u00e3o
ClienteServico=Customer - Service
TrajetoAndamentoExecutado = Track in Progress - Executed
TrajetoAndamentoRestante = Track in Progress - Remaining
TrajetoExecutado = Track Executed
TrajetoProgramado = Programmed Path
OrigemMapa=Map origin
AtualizarMapa=Reload map
TodasRotas=List of Routes
Rastreador=Tracker
Manha=Morning
Tarde=Afternoon
HorarioPosicoes=Time of the positions
SATBRASIFORT=BRASIFORT
ProgramacaoGeral=General Schedule
QtdeCacambas=Buckets Quantity
Orcamento=Budget
InicioHorario=Start time
FinalHorario=End time 
ExclusaoPedidoNaoPermitida=Exclusion allowed for pending orders only
ServicoNaoDisponivel=Service Unavailable
Atencao=Attention
ConfirmaExclusao=Confirm deletion?
ClienteNaoEncontradoOuSemCacambas=Client not found or without container
EstabelecimentoComercialResidencia=Commercial establishment or residence
Gravar=Record
QtdTrajetos=Number of Paths
FecharAtualizar=Close and Update
ValorDepositadoCXF=Amount deposited in Vault
VlrEntDir=Value direct deliveries
HorarioPosicao=Position Time
Transferencias=Transfers
TotalemRota=Total en Route
R=Collect
E=Delivery
Acessar=Access
EmpresaServico=Company / Service
SelecioneEmpresaServico=Select Company and Service
ClientesCadastrados=Registered Customers
TodosUsuarios=User List
AdicionarServico=Add Service
BancoDados=Database
Bancos=Banks
QtdBancos=Bank Quantity
AdicionarBanco=Add Database
CodigoLogin=Login Code
JanelaHorario=Time Window
CliqueParaDetalhes=Click for Details
Desconectado=Disconnected
SomenteAlertas=Alerts Only
NomeCofre=Vault Name
acesso.falhageral<message>EmailEmUso=Email already linked to someone else.
NumeroCofre=vault Number
RelatorioMovimentacaoGeralFuncionario=General Turnover Report by Employee
Noite=Night
Atrasados=Late
COLETA=Collect
DEPOSITO=Deposit
MOVIMENTACAO_INDIVIDUAL_FUNCIONARIO=Individual Movement of Employees
MOVIMENTACAO_DIARIA=daily movement
MOVIMENTACAO_GERAL_FUNCIONARIO=general drive by Employee
GerarRelatorio=Generate report
Coleta=Collect
Deposito=Deposit
RelatorioDeposito=Deposit Report
RelatorioMovimentacaoIndividualFuncionario=Employee Movement Report
RelatorioMovimentacaoDiaria=Daily Movement Report
RelatorioColeta=Collection Report
TotalDeposito=Total Deposits
TotalColeta=Total in Collections
SaldoAnterior=Previous balance
SaldoDia=Balance of the Day
SemDepositosData=There are no deposits for the selected date.
SemColetasData=There are no collections for the selected date.
SemMovimentacoesData=There are no users moving to the selected date.
SelecioneOperador=Select an User
SelecioneDeposito=Select a Deposit
SelecioneColeta=Select a Collection
SATCIT=CIT
TotalParadas=Total Stops
TotalGuias=Total Receipts
TotalKm=Total Miles
Mes=Month
Ano=Year
ParadasGuias=Stops and Receipts
AnaliseProd=Productivity Analysis Per Day
AnalseValores=Transported Amount \u200b\u200bAnalysis
TotalTransportado=Total Carried per Day
Dashboard=Dashboard
Horas=Hours
AnaliseValoresTransp=Transported Amount \u200b\u200bAnalysis
TotalValoresTransp=Total Amounts Carried by Route
AnaliseTrajetoExec=Run Path Analysis
TotalKmPerc=Total miles Traveled Per Day
TotalParadasRota=Total Stops by Route
MediaProdDia=Average Productivity per Day
ConsiderandoDados=Considering Data From First to Last Day of Month
MaximoRotas=Maximum Routes
AnaliseParada=Stop Analysis
OutrosDesc=Others
Saidas=Exits
EmServico=In Service
ForaServico=Out Of Service
SATSHALOM=SHALOM
TipoOS=SO type
SelecioneOS=Select a SO
CliDst=Client destination
ClienteFaturar=Client Invoice
DiaFechaFat=Invoice closing day
Agrupador=Switcher
MsgExtrato=Extract message
CodSrv=Service code
KM=Distance (miles)
KMTerra=Miles Dirt Road
Aditivo=Addition
CCusto=Cost Center
OSGrp=Switcher II
GTVQtde=CIT quantity
GTVEstMin=CITEstMin
SitFiscal=Fiscal Situation
CofresAtivos=Active vaults
TotalDepositos=Total Deposits
TotalColetas=Total Collections
Dia=Day
CofresAtualizados=Updated vaults
StatusComunicacao=Communication Status
StatusBateria=Battery Status
Saldo=Balance
UltimaMovimentacao=last movement
Bateria=Battery
Versao=Version
Movimentacao=Movement
MovimentacoesRecentes=Recent moves
UltimasMovimentacoes=Latest Movements
AnaliseMovimentacoes=Motion Analysis
TotalMovimentacaoHora=Total Movement Per Hour
Online=Online
Offline=Offline
SemComunicacaoRecente=No Recent Communication
Atualizados=Updated
Desatualizados=Outdated
SaldoCofres=vaults Balance
InfoCofres=Safes Information: battery, version, sensors
StatusCofres=vault status
VersaoAtual=Current version
InformacoesGerais=General information
MovimentacoesDiaHistoricoCompleto=Movements Full Day History
RelatorioColetas=Collections report
GerarRelatorioAntes=Generate the report before exporting it
HISTORICO_COLETAS=Collection History
HISTORICO_DEPOSITOS=Deposit History
RelatorioDepositos=Deposits Report
DownloadPDF=Download PDF
DownloadCSV=Download CSV
SemGuias=No Receipts
Lote=Shipping
CTPS_Nro=ERB Number
CTPS_Serie=ERB Series
RegistroPresenca=Attendance record
PostoRegistro=Registered Site
HoraInicio=Start Time
HoraFim=End Time
PreOrder=Pre Order
UploadPreOrder=Pre Order Upload
EmTransito=In Transit
Finalizado=Finished
Recolhimentos=Pick ups
Entregas=Deliveries
Tempo=Time
TrajetoAndamento = Track in Progress
UltimaComunicacao=Last Communication
AtualizarMapaAuto=Updating the map in
Segundos=seconds
SequenciaInvalida=Undefined Route Sequence
RelatorioMovimentacoes=Movement Report
HISTORICO_MOVIMENTACOES=Movement Historic
SA\u00cdDA=Output
ENTRADA=Input
RELAT\u00d3RIO\ DE\ SUPERVISOR=Supervisor Report
RELAT\u00d3RIO=Report
RONDA=Patrol
ROTA=Route
CHEGADA\ DE\ SUPERVISOR=Supervisor Arrival
SAIDA\ DE\ SUPERVISOR=Supervisor Departure
INSPE\u00c7\u00c3O=Inpection
ItensFaturamento=Billing Items
VigenciaInicio=Start Of Term
VigenciaFim=End Of Term
MensagemExtrato=Message on Statement
TipoCalculo=Calculation Type
Vigencia=Validity
A=to
TransporteUrbano=Urban Transport
TransporteInterurbano=Intercity Transport
OutrosServicos=Other Services
TransporteInterestadual=Interstate Transport
CodigoServico=Service Code
Cobranca=Billing
AgrupadorNF=Invoice Cluster
Agrupador2NF=Invoice Cluster2
KMAsfalto=Miles
ViaCxForte=Thru Vault
EntregaSab=Delivery Saturday
EntregaDom=Delivery Sunday
EntregaFer=Delivery Holidays
ItensFaturar=Items to Bill
TipoCalc=Calc Type
Qtde=Quantity
DiasCst=Vault in days
Extraordinaria=Extraordinary
Inativa=Inactive
EmailNaoInformado=Email not informed
SemRetencao=Without Retention
ComRetencao=With Retention
CadastrarOS=Order of Service Registration
FrequenciaServicos=Frequency of Services
DiaSem=Day of the Week
ISS=ISS
RotaC=Route
ItemFatPorMes=A Month
ItemFatPorDia=Per Day
ItemFatPorHora=Per Hour
ItemFatPorQtde=By Quantity
ItemFatPorIndice=Worthless Index
CadastrarCodigoServico=Service Code Registration
Banco=Bank
TAtend=Service Type
TCob=Billing Type
TCar=Vehicle Type
TipoSrv=Service Type
TRota=Route Type
CodInterf=Interface Code
Texportar=Export type
ColetaBau=Pickup
Compartilhado=Shared
PorEquipamento=By Equipment
Eventual1=Eventual 1
Eventual2=Eventual 2
PorPonto=By customer
CarroDedicado=Dedicated Car
DefinidoContrato=Defined in Contract
Embarque=Pickup
Mensal=Monthly
EmbarqueMensalizado=Monthly Boarding
Interbancario=Bank transfer
AeronaveFretada=Charter aircraft
AeronaveRegular=Regular aircraft
CarroForte=Armored Car
CarroLeve=Light Vehicle
AdValorem=Ad Valorem
Extraordinario=Extraordinary
ProcessamentoEnvelopesDestino=Destination Envelopes Processing
Monitoramento=Monitoring
NaoExportar=Do not Export
ExportarDBFCliente=DBF Export
CadastrarAgrupadorNF=Invoice Grouper
PerfilFiscal=Invoice Profile
Municipio=City
HistoricoNotaFiscal=Invoice Historic
HistFixo=Fixed Historic
HistVariavel=Variable Historic
CFOP=CFOP
Retido=Withheld
TipoCobranca=Charge Type
IRRF=IRRF
BaseINSS=Base INSS
INSS=INSS
COFINS=COFINS
CSL=CSL
IRPJ=IRPJ
Repasse=Tax Transfer
IncluirCustodia=Include Custody
RepasseLiquido=Tax Transfer/Net Value (%)
AliqISS=ISS (%)
ValorRot=Routine Price
ValorEve=Eventual Price
ValorEsp=Special Price
ValorAst=Assistance Price
CHDiaria=Time a Day
CHSemana=Time a Week
ValorServico=Service Value
DescrCalc=Kind of Calc
ValorRef=Reference Value
HEDiurna=Overtime Day
HRNoturna=Overtime Night
HEDiurna2=Overtime Day 2
HENoturna2=Overtime Night 2
CHMensal=Time Monthly
ValorDia=Amount per day
ValorHora=Amount per hour
ItensContrato=Contract Items
mostrarValores=Show Prices
Precos=Prices
Franquia=Franchise
TiposEspecificos=Specific Types
CodigoOperacional=Operational Code
ValorReforco=Boost Value
InibirReajuste=Inhibit Readjustment
ValorHEDiurna=Overtime Value
ValorHENoturna=Nighttime Overtime Value
Salario=Salary
TotalPosto=Total Service Site
QtdeFunc=Qty Employees
CHSeman=Time a Week
PorMes=Per Month
PorDia=Per Day
PorHora=Per Hour
PorQtde=By Qty
IndiceSValor=Value percentage
Embarques=Shipments
EmbarquesFranquia=Shipments with franchise 1
EmbarquesFranquia=Shipments with franchise 2
EmbNumerarioConcomitDiaria=Ship money concomitant daily
EmbEnvelopeConcomitDiaria=Ship envelope concomitence daily
EmbSuprimentoDifenciado=Ship diferentiated supply
EmbRecolhimentoDifenciado=Ship diferentiated collect
SuprimentoConcomitante=Concomitant supply
CargaDescarga=Loading and unloading
EmbarquesCobrancaEntrega=Shipments with delivery charge
AdValorem=Ad Valorem
TempoEsperaMinutos=Waiting time (minutes)
TempoEsperaHora=Waiting time (hour)
Envelopes=Envelopes
Bananinha=Pack money relieve (MCD)
Sangria=Pack money relieve
KitTroco=Kit money change
ChequesUnitario=Bank Check (Unit)
UnidadesProdServ=Units (products/services)
Milheiros=Money thousand
MilheirosBom=Good money thousand
MilheirosDilacerado=Damaged money
MilheirosMoedas=Coin money thousand
MilheirosCheques=Bank check thousand
MilheirosCheques=Bank check thousand 2
MilheirosTicket=Ticket thousand
MilheirosCheques=Bank check thousand 3
MilheirosSuprimento=Supply thousand
MilheirosSuprimentoMoedas=Coin supply thousand
Custodia=Custody
CustodiaMoedas=Coin Custody
CustodiaProdutosServi=Products/services Custody
CustodiaPermanenicaDi=Custody daily stay
CustodiaPassagem=Custody per pass
KMRodados=Miles traveled
KMTerra=Miles ground road
FixoMensal=Transport monthly fixed
FixoMensalAdValorem=Ad Valorem monthly fixed
FixoMensalTesouraria=Money process monthly fixed
TempoCoberturaPagamento=Payament coverage time
Malotes=Security bags
EntregaRecolhimentoMalotes=Supply/collect security bags
EnvelopeSangria=Envelope money reliev
CadastrarItensContrato=Contract Itens Registration
SomenteAtivas=Active Only
RotasXEscalas=Routes x Scales
CadastrarMovimentacao=Movement Registration
Anexo=Attachment
Apagar=Delete
CodMovimentacao=Code Movement
NomeSubs=Substitute Name
PesquisarMovimentacao=Search Movement
Reforco=Reinforcement
RotasXModelos=Routes x Models
GeracaoRotasXModelos=Routes x Models generation
PesquisarOS=Search Work Order
TransporteValores=Transportation of Values
GestaoCaixasEletronicos=ATM Management
AtendimentoClientes=Customer Service
OperacoesServicos=Service Operations
EscalaFuncionarios=Employee Scale
PontoEletronico=Electronic Point
Check_In=Check-In
FechamentoRotas=Closing Routes
FechamentoFaturamentoTransportes=Transportation Billing Closing
FechamentoFaturamentoServicos=Service Billing Closing
FaturarVendas=Invoice Sales
NotasFiscais=Invoices
GerarRotasUsandoModelo=Generate Routes using a Model
GerarModeloUsandoRotas=Generate Model using Routes
GerarPedidosUsandoFrequencia=Generate orders using frequency
RoteirizarPedidos=Script orders
FrequenciaRota=Frequency/Route
ModeloUtilizar=Model to use
Tolerancia=Tolerance (min)
RoteirizaAuto=Auto script
ProcurarPor=Search for
FolhaPontoIndividual=Individual Time Sheet
HrTrabalho=Work Schedule
CTPS=CTPS
Extra=Extra
LocalAtuacao=Location
Obs.=Note
Chave=Key
CadastrarContrato=Contract Registration
PesquisarContrato=Search Contract
DeveSerNumerico=Field must be numeric
CampoNaoExiste=Error: field does not exist!
SubContratosAnexos=Annex
PreFatura=Pre-Invoice
HrChegVei=Arrival vehicle
HrSaidaVei=Exit of the vehicle
Montante=Amount
ValorEmbarque=Shipment Price
ValorADV=Advalorem Price
ValorTE=Wait Time Price
TotalRota=Total Route
PreFaturaCliente=Client Pre-Invoice
QtdeParadas=Stops Quantity
Reajustes=Readjustment
itensAnteriores=Previous items
Indice=Index
AnaliseFaturamentoServico=Billing Analysis by Service
AnaliseFaturamentoRamoAtividade=Activity Billing Analysis
AnaliseFaturamentoTipoCliente=Billing Analysis by Customer Type
Milheiro=Thousand
DtBase=Base date
FormulaDesc=Description formula
IndiceDF=Percentage type
IndiceSolic=Percentage requested
IndCombAprov=Approved fuel percentage
TE=Waiting time
IndMOSolic=Percentage of labor requested
IndCombSolic=Percentage of fuel requested
IndDFAprov=Type of approved percentage
Formula=Formula
CodCarta=Letter code
DtCarta=Letter date
OperReaj=Readjustment user
DtReaj=Readjustment date
HrReaj=Readjustment time
IndDFSolic=Type of percentage requested
IInterno=I - Internal
Bloco=Block
AlaCela=Ala/Cela
Prontuario=Record
Dieta=Diet
Blindado=Armored
Particular=Private
Veiculos=Vehicles
EditarVeiculo=Edit Vehicle
CadastrarVeiculo=Register Vehicle
NumeroVeiculoExiste=Vehicle number already exists
Atividades=Activities
Complemento=Complement
CadastrarDieta=Registrer of Diets
Especificacao=Specification
SelecioneDieta=Unable to Edit the General Diet
ManutencaoItens=Maintenance of Item
CardapioDia=Menu of the Day
CodigoCardapio=Menu Code
Cardapio=Menu
Cardapios=Menus
SelecioneCardapio=Select a Menu
PesquisarCardapio=Search Menu
DatasIncoerentes=The start date must not be greater than the end date
PesquisarCardapioDia=Search Menu of the Day
ContratosGrpPagamento=Contracts (Group of Payment)
Tabela=Table
UltimoCadastrado=Last registered
Almoco=Lunch
Janta=Dine
AtualizarTabelaInterna=Update internal table
TabelaPropostaComercial=Business proposal (Services Description)
BlocoAla=Bloco/Ala
CafeManha=Breakfast
Ceia=Snack
CaixaForte=Thru Vault
SaldoReal=Real Balance
QtdeRemessasCustodia=Quantity of Custody Remittances
ValorRemessasCustodia=Custody Remittance Amount
QtdeRemessasPreparadas=Quantity of Shipments Prepared
ValorRemessasPreparadas=Value of Prepared Shipments
SelecioneCaixaForte=Select Thru Vault
Automatizada=Automated
TesourariaEntradas=Cash Receipt
Numerarios=Receipts
TipoMov=Movement Type
QtdeVol=Volume quantity
CadastrarNumerario=Cadastrar numer\u00e1rio
Diurno=Diurno
Noturno=Noturno
Vespertino=Vespertino
SomentePendentes=Pending only 
ContaTes=Treasury Account
ContaDesc=Account description
NRedOri=Shortname origin
NRedDst=Shortname destination
Entrada=Entry
Trajeto=Path
Faturar=Billing
NRedDst=Destination
NRedOS=SO Client
RotaEnt=Entry Route
OperEnt=Entry Operator
HrEnt=Entry Time
RotaSai=Exit Route
OperSai=Exit Operator
DtSai=Exit Date
HrSai=Exit Time
VolDh=VolDh
ValorDh=ValorDh
VolCh=VolCh
ValorCh=ValorCh
VolMd=VolMd
ValorMd=ValorMd
VolMet=VolMet
ValorMet=ValorMet
VolMEstr=VolMEstr
ValorMEstr=ValorMEstr
VolOutr=VolOutr
ValorOutr=ValorOutr
NRedCli=Client
CxForte=Thru Vault
MostrarHistoricoGeral=General History
CliOri=Client
SelecioneModelo=Select model
RotaJaGerada=Routes already generated for this date
Roteirizar=Script Order
Roteirizar=Script Order
ColetarEntre=Collect Between
EntregarEntre=Deliver Between
Roteirizar=Script Order
MostrarRemessas=Show Shipments
RemessaEfetivada=Effective Shipment
TesourariaDestino=Destination treasury
Conta=Account
ClassifSrv=Ranking
Hora1O=TimeO1
Hora2O=TimeO2
Regiao1=Region 1
Regiao2=Region 2
Hora2D=Time2D
QtdeGTV=Receipts
SeqRota=SeqRota
Cancelamento=Cancellation
Material=Materials
AssitTecnica=Technical Assistance
SelecioneRotaMapa=Select a ROUTE to view the MAP
OrigemRota=Route Origin
LocalizacaoRota=Route Location
DestinoRota=Route Destination
LocalizacaoPedido=Order Location
QualReferenciaRotas=Which reference to load ROUTE suggestion?
QtdEnvelopes=Number of envelopes
ReferenciaCarregamento=Route Reference
MensagemImportacaoPedido=%s1 orders were found in the file. Do you want to proceed with the import?
TipoCedula=Banknote Type
PesquisarNumerario=Search receipt
SelecioneNumerario=Select a receipt
SelecioneCodFilGuiaSerie=Select Branch, Receipt e Series
Al\u00edvio=Relief
Refor\u00e7o=Reinforcement
CedulaMoeda=Banknote/Coin
ValorProcDN=Miles de dinero
ValorProcMD=Miles de monedas
ValorCustodia=Custodia
Cedula=Banknote
Moeda=Coin
SelecioneItem=Select Item
Extrato=Billing
ICMS=ICMS
NumeroVeiculo=Vehicle Number
ConfirmarRecebimento=Confirm Receipt
BotaoPanico=Panic Button
RESUMO_ROTAS=Routes Summary
EditarContrato=Edit contract
IdentificacaoBanco=Bank identification
PermissaoNegada=Permission denied
DiasMes=Days of the month
DiasUteis=Working days
HoraEntregaMenorColeta=Delivery time before collection
HorarioCadastrado=Time already registered
SelecioneFrequencia=Select a frequency
PesquisarCliente=Customer Search
CoafComunic=COAF - Communications
Ordenacao=Ordination
Protocolo=Protocol
CadastrarFrequencia=Register frequency
RotaModeloInexistente=There are no routes on that date
ModeloJaGerado=Existing model
HorarioColeta=Collection Time
HorarioEntrega=Delivery Time
NaoExisteOS=There is no work order with the data provided
ModeloNome=Model Name
InformeNomeModelo=Enter the Model Name
OmitirSolicitacoes=Omit unconfirmed solicitations
SolicitacaoSenhaMobile=Mobile password solicitation
FechaduraDesconectada=Lock disconnected
InformeSenhaGerada=Enter the generated password, eg.: 9.888.888
CoordenadasNaoExistem=Coordinates do not exist
SenhaAceitaSucesso=Password sent successfully
SenhaRecusadaSucesso=Password successfully refused
SenhaJaEnviada=Password already sent! Operation not allowed
ErroSalvarBD=Error saving to database
ErroLerBD=Error reading information from database
CancelarFechadura=Do you want to cancel the sending of the disconnected lock password?
TipoOperacao=Operation type
LocalSolic=Solicitation location
HrSolic=Solicitation time
ChaveSrv=Server key
FechIdentif=Lock identification
Comando_Ref=Command Ref
SrvOK=Server OK
PW=Password
MatrChe=Leader registration
PWVig1=Password of Vigilant 1
Comando_Crt=Command Crt
MatrVig1=Vigilant 1 registration
Comando_Aceito=Comando_Aceito
TipoFechVei=TipoFechVei
TipoFechCli=TipoFechCli
StatusFechCli=Customer lock status
StatusFechVei=Customer vehicle status
CodigoFechCli=Customer lock code
CodigoFechVei=Customer vehicle code
SenhaUsr1FechCli=??Usr1 Password lock client
SenhaUsr1FechVei=??Usr1 Password lock vehicle
MACFechCli=Customer lock MAC
MACFechVei=Vehicle lock MAC
InformeNomeModelo=Enter the Model Name
MovimentacoesRecentesHoje=Movimenta\u00e7\u00f5es Recentes de Hoje
UltimasMovimentacoesHoje=\u00daltimas movimentacoes de hoje
MovimentacoesRecentesCofre=Movimenta\u00e7\u00f5es Recentes por Cofre
CoordenadasClienteNaoExistem=Customer coordinates do not exist
AprovarSenha=Approve password for this route
ReprovarSenha=Disapprove password for this route
ExcecaoCodFechaduraCodFilInteiro=Lock code and / or codFil cannot be converted to an integer
SelecioneSolicitacao=Select a solicitation
RecarregarTabela=Reload table
InformarComposicoes=Do you want to proceed without informing the compositions?
InsiraComposicao=Insert Compositions
login.falhageral<message>login.usuariobloqueado=Blocked User 
ComposicoesCedulas=Banknotes composition
ComposicoesMoedas=Coins composition
DuploClickRoteirizar=Double-click to Route
NumeroPedido=Request Number
SelecionePrimeiroHorario=Select The First Time
SelecioneTodasRotas=Select All Routes
NomeChefe=Leader name
HrChe=Leader time
MatrMot=Driver registration
NomeMot=Driver name
HrMot=Driver time
NomeVig1=Vigilant 1 name
HrVig1=Vigilant 1 time
NomeVig2=Vigilant 2 name
MatrVig2=Vigilant 2 registration
HrVig2=Vigilant 2 time
NomeVig3=Vigilant 3 name
HrVig3=Vigilant 3 time
MatrVig3=Vigilant 3 registration
HorarioVazio=Hor\u00e1rio vazio
SomenteDestinoCxForte=Only destination vault
GuiasEmCxForte=Receipts in vault
EntradaCxForte=Vault Entry
CapturaCliente=Customer Capture
GuiasMobile=Mobile receipt
CapturaGTV=Receipt capture
CapturaAuto=Automatic capture
LocalizacaoAtual=Current Location
InformeHora=Inform the Time
DadosSalvosSucesso=Data Successfully Saved
Dinheiro=Money
PrevisaoHoraSaida=Departure time forecast
ConfirmarExclusao=Do you really want to delete this Route
TrajetoExcluido=Route successfully Deleted
TrajetoExcutado=Delete is not allowed. Route was executed.
TrajetoJaExcluido=The route has been previously deleted
NaoExistemRotas=There are no Routes
Ferramentas=Tools
BaixarServico=Low of Service
Atraso=Delay
AtendimentoNaRota=Route Assistance
ErroObterCaixaForte=Unable to obtain vaults
ErroSemRotasNaData=There are still no routes on the current date at this branch.
DiaInvalido=Invalid day
SelecioneEntrada=Select an entry
ImplementandoFeature=This feature is not ready yet ...
GuiaPendenteTesouraria=Receipt pending in treasury. Operation not allowed!
GuiaEntrouEmCxForte=Receipt has already entered the vault
GuiasCustodia=Custody receipts
VolumesDaGTV=Receipt volumes
TrajetoPrevistoGTV=Route predicted on GTV
ParadaNaoLocalizada=Stop not located on this route
ClienteGuiaRedefinido=The source client for this receipt will be reset.
CancelarOperacao=Cancel the operation?
ClienteSelecionado=Selected customer
DesejaUtilizarParada=Do you want to use this stop?
HoraProg=Scheduled time
ChegadaRota=Route Arrival
MoedaReal=Brazilian Real
MoedaDolar=US Dollar
MoedaMexico=Mexican Peso
MoedaEuro=Euro
MoedaBritaniva=British Pound
GuiaEletronica=Electronic Guide
GuiaManual=Manual Guide
GuiaSaiuEmCxForte=Receipt is already out of the vault
ExclusaoNaoPermitida=Exclusion not allowed
GuiaDeletadaSucesso=Receipt successfully deleted
KmTexto=KM
RotaSemParadaDisponivel=Route without available stops
ExcluirVolume=Delete Volume
ExcluirGuia=Delete Receipt
AcaoEfetuadaSucesso=Successful Action!
PrenchaCamposObrigaatorios=Fill in the required fields
InformeGuias=Inform the Guides
DuploClickCliente=Double Click to Select Client
SelecioneCtrItem=Select an item
EditarContratoAnexo=Anexx Edit
CadastrarContratoAnexo=Anexx Insert
Patrimonial=Patrimonial
PesquisarPessoa=Search Person
SelecionePessoa=Select Person
DuploClickPessoa=Double click to Select Person
SelecioneDataFinal=Select the end date
DataSituacao=Date of Situation
DesejaGerarMatrAuto=Want to Generate Registration Automatically?
AnoModelo=Model Year
Carroceria=Bodywork
Combustivel=Fuel
Gasolina=Gasoline
Alcool=Alcohol
Flex=Flex Gasolina/\u00c1lcool
GasNatural=G\u00e1s Natural
Diesel=Diesel
Querosene=Kerosene
Desativado=Disabled
Triflex=Triflex Gasoline/Alcohol/CNG
SelecionePedido=Select an Order
PerguntaPedido=Do you want to delete the Order:
RecarregarSenhas=Reload passwords
MoedaPesoChileno=Chilean Peso
MoedaPesoColombiano=Colombian Peso
TipoMoeda=Currency Type
Chassis=Chasis
dt_Compra=Fecha de Compra
RENAVAN=RENAVAN
dt_Ipva=Madurez IPVA
Seguro=Seguro
Seguradora=Empresa
Apolice=Pol\u00edtica
PRXRadio=PRX radio
ESN=ESN
Blindagens=Escudos
Cabine=Caba\u00f1a
Teto=Techo
Assoalho=Piso
Vidro=Vaso
Habilitacoes=Calificaciones
VistoriaPF=Inspecci\u00f3n PF
Vencimento=Vencimiento
VistoriaFabricante=Inspecci\u00f3n Fabricante
VistoriaQualidade=Inspecci\u00f3n Calidad
VistoriaQualidadeII=Vistoria Calidad II
VistoriaConformidade=Inspecci\u00f3n Conformidad
VistoriaConformidadeII=Inspecci\u00f3n Conformidad II
Tacografo=Tac\u00f3grafo
Alvara=Licencia
VistoriaQualidadeII=Inspecci\u00f3n Calidad II
VeicTerceiros=Veh\u00edculo de Terceros

Chassis=Chassis
dt_Compra=Purchase Date
RENAVAN=RENAVAN
dt_Ipva=IPVA Due Date
Seguro=Insurance
Seguradora=Insurance Company
Apolice=Policy
PRXRadio=PRX radio
ESN=ESN
Blindagens=Shields
Cabine=Cabin
Teto=Ceiling
Assoalho=Floor
Vidro=Glass
Habilitacoes=Qualifications
VistoriaPF=Inspection PF
Vencimento=Due Date
VistoriaFabricante=Inspection Manufacturer
VistoriaQualidade=Inspection Quality
VistoriaQualidadeII=Inspection Quality II
VistoriaConformidade=Inspection Conformity
VistoriaConformidadeII=Inspection Conformity II
Tacografo=Tachograph
Alvara=License
VeicTerceiros=Third Party Vehicle
NaoExisteCaixaForte=Safe not found in Branch
NaoHaDados=No Data!
SaidasAutomaticas=Salidas autom\u00e1ticas
PerguntaClone=Are you sure you want to clone the Service Order: 
OsClonadaSucesso=Service Order Cloned Successfully!
Clonar=Clone Service Order
SPM=SPM
Ordenar=Order
OrdenarPor=Order By
PosicoesRota=Route Positions
PesquisarHorario=Search Schedule
VisualizarMapa=View on Map
SelecioneParaVisualizar=Select a Position from the List
ServicoCancelado=Service Canceled
DistanciaBaixa=Downtown Distance
SemVolumes=No Volumes
TipoGuia=Receipt Type
ServicoBaixado=Downloaded Service
Excluido=Deleted
TotalGeral=Grand Total
TotalRefeicao=Total per Meal
PercursoNaoEncontrado=Stop not found for this series/receipt
RESUMO_ROTAS_PERIODO=Routes Summary by Period
SelecioneClienteSecaoCadastrado=Select a Client with Blocks/Alas registered
TotalCafe=Total Coffee
TotalAlmoco=Total Lunch
TotalJanta=Total Dinner
TotalCeia=Total Supper  
SerieNaoCadastrada=Series not registered
CxForteNaoEncontrado=Vault not found
ResumoEfetividadeRota=Summary Route Effectiveness
Fone=Phone
Efetividade=Effectiveness
Efetivos=Effective
Percentual=Percentage
Adiantados=Advanced
Pendentes=Pending
ExcluirCardapio=Do you want to delete the menu

DadosOperacionais=Operational Data
DataAdmissao=Admission date
RegistroDRI=Record DRI
ApresentadoPor=Presented by
Regional=Regional
PrimeiroPeriodo=First period
SegundoPeriodo=Second period
dtRgEmissao=Emission
Sindicato=Syndicate
dtVencimentoExameMedico=Medical Examination Expiration
dtVencimentoPsicotecnico=Psychotechnical Maturity
Nacionalidade=Nationality
Experiencia=Experience
Raca=Breed
Instrucao=Instruction
EstadoCivil=Marital status
Emissao=Emission
Reservista=Reservist
Categoria=Category
ExameCnhNum=CNH Exam Number
CRM=CRM
TituloEleitor=Voter Title
Zona=Zone
Naturalidade=Naturalness
Conjuge=Spouse
Pai=Dad
Categoria=Category
DtExameCnhNum=CNH Exam Date
CNPJLab=CNPJ Laboratory
GrupoSanguineo=Blood Group
DadosFolha=Data Sheet
InformacoesAdicionais=Additional Information
Documentacao=Documentation
TipoAdm=Adm type
DefFis=Def Fis
CodigoOperacao=Operation Code
ContSind=Union Contribution
Vinculo=Bond
Jornada=Journey
InstituicaoEnsino=Teaching Institution
AgenteIntegracao=Integration Agent
NaturezaEstagio=Nature Internship
NivelEstagio=Stage Level
PrevisaoTermino=Forecast End
TipoReintegracao=Reintegration Type
InfoPagtoJuizo=Payment Info
DataReintegracao=Reintegration Date
DataFormacaoIni=Training Date "Start"
DataFormacaoFim=Training Date "End"
RegPoliciaFederal=Federal Police Record
DataReciclagem=Recycling Date
DataVencimentoCurso=Expiration Date Course
ExportacaoGesp=Gesp Export
EscoltaArmada=Armed Escort
GrandesEventos=Big events
ArmasNaoLetais=Non-lethal weapons
CNV=CNV
CadastroAfis=Register Afis
RegMinTrabalho=Ministry of Labor Registration
DataEmissao=Emission
SegurancaPessoal=Personal Security
EdicaoPedidoNaoPermitida=Editing only allowed for pending orders
TotalDN=Total New Money
TotalMoeda=Total Coin
ChequesQtde=Checks (number)
ChequesValor=Checks (value)
TicketsValor=Tickets (value)
KitTrocoQtde=Kit money quantity
Diferenca=Difference
TotalDD=Total damaged money
DataPedido=Request date
Refeicoes=Meals
GeradorQrCode=QR Code Generator
NaoHaClietesQrCode=There are no active Clients with CodPtoCli Filled
SaidaNumerario=Cash outflow
QtdCacambas=Quantity Buckets
FormaPagamento=Form of Payment
SaidaTesouraria=Cash Outflow
GeracaoSuprimentos=Generation of Supplies
GuiasJaGeradasSequencia=There are already generated receipts for this sequence
OperacaoSucesso=Operation performed successfully
CliquePesquisar=Click "Search" to see data!
CodCli3=Code customer 3
MarcaATM=ATM Brand
TicketsQtde=Number of Ticks
Camera=Camera
TipoSrvRota=Service Type Route
TpCliBrad=TpCliBrad
Conferente=Lecturer
GuiaEmitidaCxForte=Guide issued and transferred to Caixa Forte
GuiaPendente=Pending Guide Issue
GuiaEmitidaNaoCxForte=Guide issued and not transferred to Caixa Forte
MatrConf=Registry Conference
SelecionePeloMenosUmaTesouraria=Select at least one treasury
GuiasGeradas=Generated receipts
ExistemGuiaPendentesSaida1=Exist
ExistemGuiaPendentesSaida2=Pending Guides for Thru Vault
NaoExistemGuiasProcessar=There are no guides to process
ProcessamentoEfetuado=Successful Processing
SomenteGuiasLacre=Sealed Guides Only
PessoaSemCodPessoaWeb=Person does not have Cod. Web Person
ResumoTrabalho=Summary of Fieldwork
Visitas=Visits
Item=Item
RespostasPermitidas=Allowed Responses
Inspetor=Inspector
Titulo2=Title
LocaisVisitados=Visited Places
PreenchimentoObrigatorio=Mandatory Answer
Fiscal=Inspector
Anterior=Previous
Proximo=Next
CandidatoTitulo=Candidate
SATMAXIMA=MAXIMA
PontoEletronicoSelecionado=Selected - Electronic Point
PontoEletronicoAnteriores=Previous - Electronic Point
Localizacao=Location
CliqueVisualizarFoto=Click to View Photo
SemRegistrosAnteriores=No Previous Records
SATFENIXX=FENIXX
PostoSemCoordenadas=Location without Coordinates
PontoEletronicoMensal=Monthly - Electronic Point
SATPISCINAFACIL=PISCINA F\u00c1CIL
SATBIMBO=BIMBO
SATDELTACORP=DELTACORP
SATFORCAALERTA=FORCAALERTA
SATPETGOLDEN=PET GOLDEN
SATFORTEARARUAMA=FORTE ARARUAMA
SATBRINKSDEMO=BRINKS DEMO
SATBRINKS=BRINKS
SATFEDERAL=FEDERAL
SATSOSP=SOS ALARMES
Ferias=Vacation
Atrasos=Delays
HoraRegistrada=Registered Time
HoraPrevista=Estimated Time
Manuais=Manuais
MensagemRecebida=Message Received
CentralAlerta=Alert Center
PathSrv=Path
TipoMensagem=Message type
StatusMensagem=Message status
QueueFech=Queue
FuncionarioEscalado=Escalated Employee
FuncionarioAusente=Missing Employee
NomeAusente=Missing Name
IndicadorHorasExtras=Overtime Indicator
HorasDiurnas=Daytime Hours
HorasNoturnas=Night Hours
NumeroHE=Number of Overtime Hours
Hospedagem=Accommodation
Mesario=Table
DescricaoRegiao=Description Region
Registros=Records
MovimentacaoOperacional=Operational Movement
TotaHorasDecimais=Total Decimal Hours
RefeicoesSimbolo=Meals($)
TransporteSimbolo=Transport($)
HospedagemSimbolo=Accommodation($)
Intrajornada=Day Off
PagarHorasTipo2=Paying Type 2 Hours
HoraExtra=Extra hour
ReforcoNormal=Normal Reinforcement
ReforcoObras=Reinforcement Works
ReforcoEspecial=Special Reinforcement
Cobertura=Roof
ComplementoReforco=Complement Reinforcement
NaoTemEfetivo=Has no staff
Ausencia=Absence
PlantaoReserva=Reserve duty
Cortesia=Courtesy
AusenciaParcial=Partial Absence
FaltaJustificada=Justified Absence
FaltaInjustificada=Unjustified absence
Folga=Day off
Administrativa=Administrative
Reciclagem=Recycling
AlmocoEfetivo=Effective Lunch
Escolta=Escort
CoberturaReserva=Reserve Coverage
Compensar=Compensate
BancoHoras=Bank of Hours
SelecioneQueue=Select queue
SATINVLRO=INVISEG
BaterPonto=Electronic Point
InformeMatriculaSenha=Enter your Registration and Password
UsuarioSenhaIncorreta=Incorrect Username or Password
MensagemLigarGPS=To <b>Register the Point </b> it is necessary to <b>Activate the Location</b> of the device
PontoRegistrado=Successfully Registered Point
EmailIncorretoParaCPF=Incorrect email for this SSN
PessoaNaoEhCandidata=Registration of person who is no longer a candidate
Parametro=Parameter
InformeParametro=Enter the Parameter
VerifiqueParam=Failed to query data. Check the configured Parameter!
ErroAcessarArquivo=Error accessing the file
ErroCriarDir=Error creating directory
ErroDeletarArquivo=Error deleting file
CadastroSucessoCandidato=Your data has been successfully registered.
EdicaoSucessoCandidato=Data updated successfully
ImoveisporSituacaoAgp=Imov\u00e9is por situa\u00e7\u00e3o
DescImoveisporSituacaoAgp=Quantidade de im\u00f3veis por situa\u00e7\u00e3o
ImoveisDiaTrab=Im\u00f3veis por dia de trabalho
DescImoveisDiaTrab=M\u00e9dia de im\u00f3veis por dia trabalhado
ImoveisporSituacaoDia=Evolu\u00e7ao im\u00f3veis por situa\u00e7\u00e3o
DescImoveisporSituacaoDia=Evolu\u00e7\u00e3o di\u00e1ria im\u00f3veis por situa\u00e7\u00e3o
InspecaoeTratamentoAgp=Inspe\u00e7\u00e3o e tratamento
DescInspecaoeTratamentoAgp=Inspe\u00e7\u00e3o e tratamento por tipo de im\u00f3vel
InspecaoTratamentoTipo=Evolu\u00e7\u00e3o inspe\u00e7\u00e3o e tratamento
DescInspecaoTratamentoTipo=Evolu\u00e7\u00e3o di\u00e1ria de inspe\u00e7\u00f5es e tratamentos
DepositosTipoAgp=Dep\u00f3sitos por tipo
DescDepositosTipoAgp=Dep\u00f3sitos identificados por tipo
DepositoAcaoAgp=A\u00e7\u00f5es tomadas
DescDepositoAcaoAgp=A\u00e7\u00f5es tomadas em dep\u00f3sitos identificados
DepositosEvolucao=Evolu\u00e7\u00e3o dep\u00f3sitos
DescDepositosEvolucao=Evolu\u00e7\u00e3o di\u00e1ria depositos identificados
EvolucaoDepositosTipo=Evolu\u00e7\u00e3o dep\u00f3sitos por tipo
DescEvolucaoDepositosTipo=Evolu\u00e7\u00e3o di\u00e1ria depositos identificados por tipo
AmostrasRoedoresAmostras=Im\u00f3veis inspecionados x Amostras, roedores e dengue
DescAmostrasRoedoresAmostras=Comparativo entre Inspe\u00e7\u00f5es e amostas, tocas e dengue
InspecionadosxDengue=Im\u00f3veis inspecionados x Dengue
DescInspecionadosxDengue=Im\u00f3veis inspecionados x Dengue nos ultimos tr\u00eas meses
ToqueAquiResponder=Tap here to Reply
ToqueAquiAssinar=Tap here to Subscribe
ToqueTirarFoto=Tap to take a Photo
TipoBD=Database Type
CodEmpresa=Company Code
HostNameLocal=Local Host Name
HostNameWeb=WEB Host Name
ArquivoLogotipo=Logo File
ArquivoLogotipoNfe=Logo File NFe
EmailAdm=Administrative Email
Fotografias=Photos
FusoHorario=Timezone
FusoSefaz=Sefaz Timezone
ToleranciaEscala=Scale Tolerance (min)
ToleranciaPonto=Point Tolerance (min)
ToleranciaAcesso=Access Tolerance (min)
NomeFilial=Branch Name
TrocarSenhaMobile=Change Mobile Password Daily
FonteDados=Data Source
ParamCadastrado=Parameter already registered for this Company
SATFIDELYS1=FIDELYS
SATFIDELYS=FIDELYS
ContasReceber=Bills to Receive
DataNF=Invoice Date
DtVencimento=Due Date
DtPrevisaoPagto=Payment Forecast
DiasAtraso=Delayed Days
MensagemSucesso=Mensagem enviada com sucesso!
DtPagto=Pay Day
ContasPagar=Bills to Pay
Fornecedor=Provider
UltimaLocalizacaoSupervisor=Supervisor Location
Fechaduras=Locks
FechaduraLagardCrypto=01 - Lagard CryptoGard
FechaduraLagardSmart=02 - Lagard SmartGard
FechaduraLagardCombo=03 - Lagard ComboGard
FechaduraLagardCrypto04=04 - Lagard CryptoGard-BR
FechaduraLagardDynamic=05 - Lagard Dynamic
FechaduraMasHamington=11 - Mas Hamington
FechaduraTamborMecanico=21 - Tambor Mec\u00e2nico
FechaduraTamper=22 - Tamper
FechaduraHslPerto=30 - HSL Perto
FechaduraOutras=99 - Outras
TipoInstalacao=Type of Installation
TipoInstalacaoAtm=01 - ATM
TipoInstalacaoAtm2=11 - ATM Fechadura 2
TipoInstalacaoAtm21=21 - ATM Fechadura 2
TipoInstalacaoVeiculo=2 - Ve\u00edculo
TipoInstalacaoVeiculoFechadura=12 - Ve\u00edculo Fechadura 2
TipoInstalacaoVeiculoFechadura22=22 - Ve\u00edculo Fechadura 3
SenhaManager=Senha Manager
PrazoRestante=Remaining Term
ID=Identification
SemLatLon=No Lat/Lon
Dt_Demis=Resignation Date
AcessoNegado=Unauthorized access!<br>Contact your System Administrator
AutorizacaoAcesso=Access Authorization
EnviarAutorizacao=Submit Authorization
Caracteres=characters
PessoaSemTelefone=Person Without Telephone
DataAutMenor=Date must be greater than or equal to Today
HoraAutMenor=End Time Must be Greater Than Start Time
HoraAutMenor2=Start Time Must Be Greater Than Current Time
SPMEW=SPM EW
GuiaAutorizada=Authorized Guide
LatLonCaixaForte=Safe Without Latitude/Longitude Registered
AtualizarMeusDados=Atualizar Meus Dados
ComprovanteResidencia=Proof of Residence
CertidaoNascimento=Birth Certificate
CertidaoCasamento=Wedding Certificate
Passaporte=Passport
CarteiraVigilante=Vigilante Wallet
FotoNaoEncontrada=Photo Not Found
FotoCarregadaSucesso=Photo Saved successfully
ArquivosPasta=Files in Folder
ArquivoPasta=File in Folder
NenhumArquivoEncontrado=No Find Files in Folder
AnoCheg=Arrival Year
Ingresso=Ticket
CTPS_UF=ERB ST
NovaMensagem=New Message
UltimaEntrada=Last Entry
UltimaSaida=Last Exit
ExclusaoRotaDia=Allowed to delete only route of the day
SATASO=ASO
SATCEFOR=CEFOR
SATTECBAN=TECBAN
SATG5ESTRELAS=G5 ESTRELAS
SATGRIFFO=GRIFFO
SATROMMA=ROMMA
SATSEFIX=SEFIX
SATTAGUA=TAGU\u00c1 ENTULHOS
NaoEncontradoCentral=Person not found in central database
ChaveNaoEncontrada=Key not Found
StatusGuia=Tab Status
Emitida=Issued
ChaveExcluida=The reported key is Disabled
ChaveExpirada=The key entered Expired
GeradoPor=Generated by
RPVdescr=PROVISIONAL CASH TRANSPORTATION RECEIPT
EdicaoNaoPermitida=Editing not allowed.<br>Service performed.
ParadaEntrega=Cannot edit Guides: <b>Delivery Stop</b>
SelecioneGuiaEmitida=Select an Issued Tab
EmissaoDisponivel=Available for Emission
EdicaoDisponivel=Editing Allowed
ResumoCaixa=Cash Summary
Todas=All
AnalisePorQtde=Count Analysis - By Quantity
AnalisePorValor=Count Analysis - By Receipt Value
AnalisePorQtdeHora=Average Qty Per Hour
AnalisePorValorHora=Average Receipt Value Per Hour
AnalisePorFilial=Count Analysis - By Branch
AnaliseQtdeDia=Daily Quantity Analysis
AnaliseValorDia=Analysis of Amount Received per Day
AnaliseContagemDet=Detailed Count Analysis
QtdeDH=Qty. Banknotes
QtdeMD=Qty. Coins
QuantidadeTotal=Total amount
ProdDH_Hr=Banknote Production - Hour
ProdMD_Hr=Currency Production - Hour
ValorRecebido=Amount received
InformeDataParaAcesso=Enter Access Date
DataInvalidaParaAcesso=Invalid Access Date
InformeBaseDadosAcesso=Report Access Database
InformeCodPessoaAcesso=Inform Cod. Access Person
InformeSeqRotaAcesso=Report Access Route Sequence
SemPermissaoAcesso=No Access Permission
DesbloquearUsuario=Unlock User
ConfirmaDesbloquearUsuario=Are you sure you want to unlock User?
UsuarioDesbloqueado=User Unlocked
SATGLOBAL=GLOBAL
Produtividade=Productivity
MilheirosHr=Thousands of Hours
PeriodoFechado=Closed Period
FichaFuncional=Functional Sheet
SelecioneTransacao=Select a Transaction
SelecioneParada=Select a Stop
TransacoesSemVinculo=Transactions without Link
SemRegistrosTransacao=There are no routes in the selected period with card transactions
TotalOnePay=Total 1Pay
TaxaOnePay=Rate 1Pay
LiquidoOnePay=Net 1Pay
Vincular=Link
Crescente=Ascending
Decrescente=Descending
LogContraCheque=Paycheck Log
Contracheques=Paychecks
PosicoesSupervisor=Supervisor Positions
CliqueParaPosicaoDetalhada=Click for Detailed Positions
EditarResposta=Edit Reply
PesquisarGuia=Search Guide
SATNSF=SATNSF
UploadArquivo=File Upload
SelecionarArquivo=Select File
TokenNaoInformado=Token Not Informed
TokenNaoEncontrado=Token Not Found
InformeNomeArquivo=Enter the File Name
ArquivoCarregadoComSucesso=File Uploaded Successfully
GuiaAssistencia=TECHNICAL ASSISTANCE GUIDE
PedidoCli=Customer Order
NaoInformado=Uninformed
ImgPadrao=Standard Image
NovaFoto=New Picture
FotoPadrao=Standard Photo
TornarPadrao=Make Default
GuiasEgtv=Receipt
PortalCliente=Customer Portal
UnidadesCliente=Customer Units
MapaTesouraria=Treasury Map
IdUsuario=User ID
Seguranca=Security
AutorizacaoAcesso=Access Authorization
SegSex=MonFri
Sab=Sat
Dom=Sun
Fer=Hol
Finalidade=Goal
OperAut=Authorized
Dt_Aut=Aut Date
Hr_Aut=Aut Time
Autorizar=Authorize
DeptoDestino=Destination Department
AreaSeguranca=Security Area
Area=Area
ConfirmaAutorizacao=Confirm Authorization?
NaoExibirPaginacao=Don't display pagination