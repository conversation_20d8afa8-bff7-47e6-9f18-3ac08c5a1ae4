/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Dados.Persistencia;
import SasBeans.ExtratoFaturamento;
import SasDaos.FatTVGuiasDao;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class ExtratoFaturamentoClienteLazyList extends LazyDataModel<ExtratoFaturamento> {

    private static final long serialVersionUID = 1L;
    private List<ExtratoFaturamento> extratos;
    private final FatTVGuiasDao extratosDao;
    private final Persistencia persistencia;
    private final Persistencia central;
    private final BigDecimal codPessoa;
    private final String codFil;
    private final String dataInicio;
    private final String dataFim;

    public ExtratoFaturamentoClienteLazyList(Persistencia persistencia, Persistencia central, BigDecimal codPessoa, String codFil, String dataInicio, String dataFim) {
        this.persistencia = persistencia;
        this.central = central;
        this.codPessoa = codPessoa;
        this.codFil = codFil;
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.extratosDao = new FatTVGuiasDao();
    }

    @Override
    public List<ExtratoFaturamento> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        System.out.println("=== ExtratoFaturamentoClienteLazyList.load() CHAMADO ===");
        System.out.println("Página: " + pageSize + " First: " + first + " Field: " + sortField);
        System.out.println("Parâmetros: codPessoa=" + this.codPessoa + ", codFil=" + this.codFil + ", dataInicio=" + this.dataInicio + ", dataFim=" + this.dataFim);

        try {
            // Usar o método existente listarExtratoCliente
            System.out.println("Chamando extratosDao.listarExtratoCliente...");
            this.extratos = this.extratosDao.listarExtratoCliente(
                this.codPessoa.toString(),
                this.codFil,
                this.dataInicio,
                this.dataFim,
                null, // numeroNF
                null, // pracaNF
                this.persistencia,
                this.central
            );

            System.out.println("Resultado da consulta: " + (this.extratos != null ? this.extratos.size() : "null") + " registros");

            if (this.extratos == null) {
                this.extratos = new ArrayList<>();
                System.out.println("Lista era null, criada lista vazia");
            }

            // Define o total de registros
            setRowCount(this.extratos.size());
            System.out.println("RowCount definido: " + this.extratos.size());

            // Define o tamanho da página
            setPageSize(pageSize);

            System.out.println("Retornando lista completa: " + this.extratos.size() + " registros");
            return this.extratos;

        } catch (Exception e) {
            System.out.println("ERRO no ExtratoFaturamentoClienteLazyList.load(): " + e.getMessage());
            e.printStackTrace();
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Erro ao carregar extrato: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            return new ArrayList<>();
        }
    }

    @Override
    public Object getRowKey(ExtratoFaturamento extrato) {
        try {
            return extrato.getGuia() + "_" + extrato.getData();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public ExtratoFaturamento getRowData(String rowKey) {
        try {
            if (this.extratos != null) {
                for (ExtratoFaturamento extrato : this.extratos) {
                    String key = extrato.getGuia() + "_" + extrato.getData();
                    if (key.equals(rowKey)) {
                        return extrato;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            System.out.println("Erro ao buscar extrato: " + rowKey + " - " + e.getMessage());
            return null;
        }
    }
}
