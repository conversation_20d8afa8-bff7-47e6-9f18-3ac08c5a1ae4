/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.relatorio;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Controller.SatMobEW.SatMobEWSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.FuncionFaltas;
import SasBeans.FuncionFerias;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaTrajeto;
import SasBeans.PstInspecao;
import SasBeans.PstServ;
import SasBeans.RHPonto;
import SasBeans.RHPontoDet;
import SasBeans.RastrearEW;
import SasBeans.RelatorioDoctos;
import SasBeans.Rondas;
import SasBeansCompostas.FuncionPstServ;
import SasBeansCompostas.LogsSatMobEW;
import SasDaos.FiliaisDao;
import SasDaos.FuncionDao;
import SasDaos.RHPontoDao;
import br.com.sasw.lazydatamodels.LogsSatMobEWLazyList;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.Funcionarios;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.LogsSatMobEWDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import static br.com.sasw.pacotesuteis.utilidades.Numeros.S2I;
import br.com.sasw.utils.Coordenadas;
import static br.com.sasw.utils.Mapas.MARCADOR;
import br.com.sasw.utils.Mascaras;
import static br.com.sasw.utils.Mascaras.Data;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datagrid.DataGrid;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.primefaces.model.chart.ChartSeries;
import org.primefaces.model.chart.HorizontalBarChartModel;
import org.primefaces.model.map.DefaultMapModel;
import org.primefaces.model.map.LatLng;
import org.primefaces.model.map.MapModel;
import org.primefaces.model.map.Marker;
import org.primefaces.model.map.Polyline;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 *
 * <AUTHOR>
 */
@Named(value = "mobEW")
@ViewScoped
public class SatMobEWMB implements Serializable {

    private BigDecimal codPessoa;
    private Persistencia persistencia, satellite;
    private String codFil, codFilEfetivos, banco, operador, nomeFilial, caminho, log, secao, secaoEfetivos, fotoRelatorio, dataTela, imagemTipo, html, centroMapa, zoomMapa,
            titulo, senhaAtual, novaSenha, matricula, nivel, fotoGride, tipoItem,
            resumoQdeEntradas, resumoQdeSaidas, resumoQdeInspecoes, resumoQdeRondas, resumoQdeRelatorios, resumoQdeOutros,
            fotoCarregada, fotoNumero, dadosRelatorioLine, dadosRelatorioTimeLine, listaTrabalhos, markers, centro, batidaPontoHtmlTopo,
            batidaPontoFoto, batidaPontoTipo, batidaPontoHtmlHistorico, batidaPontoHtmlHistoricoMensal, htmlAtrasados, htmlFaltas, htmlFerias, feriasQtde, atrasadosQde, faltasQtde,
            qtdeAtrasados, qtdeFaltas, qtdeFerias, operDest, mensagem,
            webPontoLatitude, webPontoLongitude, webPontoLarguraImagem, webPontoAlturaImagem, webPontoBase64,
            data1, data2, data1Efetivos, data2Efetivos,
            htmlEfetivos, htmlEfetivosPontos, filtroNomeFuncionario, ordenacao,
            mapaLocalPonto;

    private static Date ultimoDia;
    private ArquivoLog logerro;
    private SatMobEWSatWeb satMobEWSatWeb;
    private UploadedFile uploadedFile;
    private InputStream inputStream;
    private Map filters;
    private int posFotoRelatorio, total;
    private List<PstServ> postos, postosCadastro, postosEfetivos;
    private List<PessoaCliAut> contatosCadastro;
    private RelatorioDoctos cadastroRelatorio;
    private List<String> fotosRelatorio;
    private List<Rondas> rondas;
    private List<Funcionarios> funcionarios;
    private Funcion funcionarioSelecionado;
    private List<Funcion> funcionariosDash;
    private List<Funcionarios> functionariosSelecionadosFiltro;
    private List<PstServ> secaoSelecionadosFiltro;
    private LazyDataModel<LogsSatMobEW> logsSatMobEWs = null;
    private List<LogsSatMobEW> logsSatMobEWsResumo = null;
    private LazyDataModel<LogsSatMobEW> lazyGride = null;
    private List<PessoaTrajeto> contatos;
    private List<Pessoa> pessoas;
    private Pessoa pessoaSelecionada;
    private List<LogsSatMobEW> paradasPrestador;
    private List<InspecoesMB.PstInspecaoDetalhes> pstInspecaoSelecionadaDetalhes;
    private List<PstInspecao> pstInspecoesBoletimTrabalho;
    private LogsSatMobEW logsSatMobEWSelecionado;
    private LogsSatMobEW logsSatMobEWFiltro;
    private MapModel mapa;
    private HorizontalBarChartModel graficoRonda;
    private StreamedContent arquivoRelatorio;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private boolean supervisor, filtroWeb, video, audio, mostrarFiliais, limparFiltros;    

    private List<Date> datasSelecionadas, datasSelecionadasEfetivos;
    private Date dataInicio, dataFim, dataInicioEfetivos, dataFimEfetivos;
    private String chavePesquisa = "NOME";
    private String valorPesquisa = "";
    
    private RHPontoDao umRHPontoDao = new RHPontoDao();
    private static final String SERVIDOR_MOBILE_FOTO = 
            "https://mobile.sasw.com.br:9091/satellite/fotos/";
    private Set<String> resumoCategoria = new TreeSet<>();
    
    private static final int CATEGORIA_ENTRADA = 1;
    private static final int CATEGORIA_SAIDA = 2;
    private static final int CATEGORIA_RELATORIO = 3;
    private static final int CATEGORIA_INSPECAO = 4;
    private static final int CATEGORIA_RONDA = 5;
    private static final int CATEGORIA_OUTRA = 6;
    private static final int QUANTIDADE_LINHA_MAXIMA = 10000;
    private static final int QUANTIDADE_LINHA_MINIMA = 100;
    
    private boolean paginacaoDesabilitada;
    private int quantidadeLinha;
    
    public SatMobEWMB() throws Exception {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        secao = (String) fc.getExternalContext().getSessionMap().get("posto");
        nivel = (String) fc.getExternalContext().getSessionMap().get("nivel");
        matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        log = new String();
        try {
            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        } catch (Exception e) {

        }
        logerro = new ArquivoLog();
        satMobEWSatWeb = new SatMobEWSatWeb();
        rotassatweb = new RotasSatWeb();
        secao = "";
        dataTela = DataAtual.getDataAtual("SQL");
        ultimoDiadoMes();

        pessoaSelecionada = new Pessoa();
        funcionarioSelecionado = new Funcion();

        mapa = new DefaultMapModel();
        try {
            supervisor = !nivel.equals("8");
        } catch (Exception e) {

        }
        cadastroRelatorio = new RelatorioDoctos();

        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        dataFim = c.getTime();
        dataInicio = c.getTime();
        data1 = dataFim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        data2 = dataFim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        datasSelecionadas = new ArrayList<>();
        datasSelecionadas.add(dataInicio); // data inicial
        datasSelecionadas.add(dataFim); // data final
        filtroNomeFuncionario = "";
        ordenacao = "D";
        limparFiltroCategoria();
        quantidadeLinha = QUANTIDADE_LINHA_MINIMA;
    }

    public void PersistenciaWebPonto(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("index.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void Persistencia(Persistencia pp, Persistencia satellite) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.satellite = satellite;
            if (null == this.satellite) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            this.filters = new HashMap();
            this.filters.put("data", this.dataTela);
            this.filters.put("nivel", this.nivel);
            this.filters.put("codfil", this.codFil.equals("0") ? "" : this.codFil);
            this.filters.put("posto", this.secao == null ? "" : this.secao);
            this.filters.put("secao", this.secao == null ? "" : this.secao);

            if (!this.nivel.equals("") && (this.nivel.equals("1") || this.nivel.equals("2"))) {
                this.filters.put("matricula", this.matricula);
            } else {
                this.filters.put("matricula", "");
            }
            this.filters.put("tipo", "");
            this.filters.put("nome", "");
            this.filters.put("codpessoa", "");
            this.filters.put("filtroWeb", (this.matricula == null || this.matricula.equals("0") || this.matricula.equals(""))
                    && !this.supervisor ? "1" : "0");
            this.total = this.satMobEWSatWeb.contagem(this.filters, !this.supervisor ? this.codPessoa : null, this.persistencia);
            this.filiais = this.rotassatweb.buscaInfoFilial(this.codFil, this.persistencia);

            this.logsSatMobEWSelecionado = new LogsSatMobEW();

            if (null == this.logsSatMobEWFiltro) {
                this.logsSatMobEWFiltro = new LogsSatMobEW();
                this.logsSatMobEWFiltro.setCodfil(codFil);
                this.postos = this.satMobEWSatWeb.getPstSerList(BigDecimal.ZERO, codFil, this.persistencia);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<LogsSatMobEW> getAllLogs() throws Exception {
        String MatriculasSel = "";

        /* if (null != this.functionariosSelecionadosFiltro) {
            for (Funcionarios functionariosSelecionadosFiltro1 : this.functionariosSelecionadosFiltro) {
                if (null != functionariosSelecionadosFiltro1 && null != functionariosSelecionadosFiltro1.getMatr()) {
                    if (!MatriculasSel.equals("")) {
                        MatriculasSel += ",";
                    }

                    MatriculasSel += functionariosSelecionadosFiltro1.getMatr();
                }
            }
        }

        if (!MatriculasSel.equals("")) {
            this.filters.replace("matricula", "");
        } else if (!this.nivel.equals("") && (this.nivel.equals("1") || this.nivel.equals("2"))) {
            this.filters.replace("matricula", this.matricula);
        } else {
            this.filters.replace("matricula", "");
        }*/
        //this.filters.replace("matricula", "");
        //this.filters.replace("secao", "");
        String secoesSelecionadas = "";

        if (null != secaoSelecionadosFiltro && secaoSelecionadosFiltro.size() > 0) {
            for (int I = 0; I < secaoSelecionadosFiltro.size(); I++) {
                if (secaoSelecionadosFiltro.get(I) != null) {
                    if (!secoesSelecionadas.equals("")) {
                        secoesSelecionadas += ",";
                    }

                    secoesSelecionadas += "'" + secaoSelecionadosFiltro.get(I).getSecao() + "'";
                }
            }
        }

        this.filters.replace("data", "");
        /*
        //Código para consultar determinada Matr em um Período específico
        this.filters = new HashMap();
        this.filters.put("codfil", this.codFil.equals("0") ? "" : this.codFil);
        this.filters.put("tipo", "1");
        MatriculasSel = "2314";
        this.data1 = "20200416";
        this.data2 = "20210106";
         */

        this.filters.replace("codfil", this.codFil.equals("0") ? "" : this.codFil);

        if (null != this.logsSatMobEWFiltro.getTipo()) {
            this.filters.replace("tipo", this.logsSatMobEWFiltro.getTipo());
        } else {
            this.filters.replace("tipo", "");
        }

        /*if (null != this.logsSatMobEWFiltro.getSecao() && !this.logsSatMobEWFiltro.getSecao().equals("")) {
            this.filters.replace("secao", logsSatMobEWFiltro.getSecao());
        } else {
            this.filters.replace("secao", this.secao == null ? "" : this.secao);
        }*/
        //dt.setFilters(this.filters);
        /*if (secoesSelecionadas.equals("")) {
            this.logsSatMobEWs = new LogsSatMobEWLazyList(this.persistencia, !this.supervisor ? this.codPessoa : null, this.filters, this.data1, this.data2);
        } else {*/
        this.logsSatMobEWs = new LogsSatMobEWLazyList(this.persistencia, 
                !this.supervisor ? this.codPessoa : null, 
                this.filters, secoesSelecionadas, filtroNomeFuncionario, 
                this.data1, this.data2, this.ordenacao, resumoCategoria,
                paginacaoDesabilitada);
        //}

        try {
            /*if (secoesSelecionadas.equals("")) {
                this.total = this.satMobEWSatWeb.contagem(this.filters, !this.supervisor ? this.codPessoa : null, this.data1, this.data2, this.persistencia);
            } else {*/
            this.total = this.satMobEWSatWeb.contagem(this.filters, 
                    !this.supervisor ? this.codPessoa : null, this.data1, 
                    this.data2, secoesSelecionadas, filtroNomeFuncionario, 
                    resumoCategoria, this.persistencia);
            //}

            PrimeFaces.current().executeScript("ConsultarEndereco();");
            PrimeFaces.current().ajax().update("main:tabela");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.logsSatMobEWs;
    }

    public void manual1() {
        try {
            String Arquivo = FacesContext.getCurrentInstance().getExternalContext().getRealPath("/WEB-INF/Arquivos/manualoperacaoguardavidas.pdf");
            File file = new File(Arquivo);
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int tamanho = fis.available();
            byte[] buffer = new byte[tamanho];
            int bytesRead;
            while ((bytesRead = fis.read(buffer, 0, tamanho)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            fis.close();
            byte[] arquivo = baos.toByteArray();
            FacesContext.getCurrentInstance().getExternalContext().setResponseContentType("application/pdf");
            HttpServletResponse response = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
            response.setContentLength(arquivo.length);
            response.setHeader("Content-disposition", "inline; filename=\"manualoperacaoguardavidas.pdf\"");
            OutputStream ouputStream = response.getOutputStream();
            ouputStream.write(arquivo, 0, arquivo.length);
            ouputStream.flush();
            ouputStream.close();
            FacesContext.getCurrentInstance().responseComplete();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void manual2() {
        try {
            String Arquivo = FacesContext.getCurrentInstance().getExternalContext().getRealPath("/WEB-INF/Arquivos/manualoperacaosupervisor.pdf");
            File file = new File(Arquivo);
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int tamanho = fis.available();
            byte[] buffer = new byte[tamanho];
            int bytesRead;
            while ((bytesRead = fis.read(buffer, 0, tamanho)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            fis.close();
            byte[] arquivo = baos.toByteArray();
            FacesContext.getCurrentInstance().getExternalContext().setResponseContentType("application/pdf");
            HttpServletResponse response = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
            response.setContentLength(arquivo.length);
            response.setHeader("Content-disposition", "inline; filename=\"manualoperacaosupervisor.pdf\"");
            OutputStream ouputStream = response.getOutputStream();
            ouputStream.write(arquivo, 0, arquivo.length);
            ouputStream.flush();
            ouputStream.close();
            FacesContext.getCurrentInstance().responseComplete();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void manual3() {
        try {
            String Arquivo = FacesContext.getCurrentInstance().getExternalContext().getRealPath("/WEB-INF/Arquivos/politicadisciplinar.pdf");
            File file = new File(Arquivo);
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int tamanho = fis.available();
            byte[] buffer = new byte[tamanho];
            int bytesRead;
            while ((bytesRead = fis.read(buffer, 0, tamanho)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            fis.close();
            byte[] arquivo = baos.toByteArray();
            FacesContext.getCurrentInstance().getExternalContext().setResponseContentType("application/pdf");
            HttpServletResponse response = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
            response.setContentLength(arquivo.length);
            response.setHeader("Content-disposition", "inline; filename=\"politicadisciplinar.pdf\"");
            OutputStream ouputStream = response.getOutputStream();
            ouputStream.write(arquivo, 0, arquivo.length);
            ouputStream.flush();
            ouputStream.close();
            FacesContext.getCurrentInstance().responseComplete();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void carregarDadosRelatorios() throws Exception {
        carregarDadosRelatoriosLine();
    }

    public void carregarDadosRelatoriosLine() throws Exception {
        dadosRelatorioLine = "";
        dadosRelatorioTimeLine = "";

        this.filters.replace("tipo", "2");
        StringBuilder htmlRel = new StringBuilder(), htmlTimeLine = new StringBuilder();
        String UltimoDia = "";

        // Consulta e tratamento de dados
        this.logsSatMobEWsResumo = this.satMobEWSatWeb.listagemPaginada(0, 9999999, this.filters, !this.supervisor ? this.codPessoa : null, this.persistencia);
        if (funcionarios == null) {
            funcionarios = new ArrayList<>();
        }

        for (LogsSatMobEW dados : this.logsSatMobEWsResumo) {
            /* DADOS RELATÓRIO EM LINHA */
            htmlRel.append(" <div class=\"ItemRelatorio\">");
            htmlRel.append("    <table class=\"DadosRelatorio\">");
            htmlRel.append("       <tr>");
            htmlRel.append("          <td>");
            if (!dados.getFotos().equals("")) {
                htmlRel.append("              <div class=\"FotoPrincipal\" style=\"background-image:url('").append(dados.getFotos().split(";")[0]).append("');\"></div>");
            } else {
                htmlRel.append("              <div class=\"FotoPrincipal\" style=\"background-image:url('https://mobile.sasw.com.br:9091/satmobile/img/icone_semfoto.jpg');\"></div>");
            }
            htmlRel.append("          </td>");
            htmlRel.append("          <td style=\"color:#303030 !important; padding:10px;\">");
            htmlRel.append("              <label ref=\"usuario\" style=\"height:12px !important; font-size:10pt; font-weight:bold; margin:0px !important;\">").append(dados.getFuncionario()).append("</label>");
            if (!dados.getTitulo().equals("")) {
                htmlRel.append("              <label ref=\"ocorrencia\" style=\"height:18px !important; font-size:8pt; font-weight:bold; margin:0px !important;\">").append(dados.getTitulo()).append("</label>");
            } else {
                htmlRel.append("              <label ref=\"ocorrencia\" style=\"height:18px !important; font-size:8pt; font-weight:bold; margin:0px !important;\">").append(Messages.getMessageS("RelatorioSupervisor")).append("</label>");
            }
            if (!dados.getPosto().equals("")
                    && null != dados.getPosto()) {
                htmlRel.append("              <label ref=\"ocorrencia\" style=\"background-color:#505050 !important; height:15px !important; font-size:7pt; font-weight:bold; margin:0px !important;\">").append(dados.getPosto()).append("</label>");
            }
            htmlRel.append("              <label style=\"height:12px !important;display:block; font-size:8pt; font-weight:500 !important; margin:0px !important; margin-top:2px !important;\"><i class=\"fa fa-calendar\"></i>&nbsp;<label style=\"font-weight:bold\">").append(Messages.getMessageS("Data")).append(":</label>&nbsp;").append(Mascaras.Data(dados.getData())).append("&nbsp;&nbsp;&nbsp;&nbsp;<i class=\"fa fa-clock-o\"></i>&nbsp;<label style=\"font-weight:bold\">Hora:</label>&nbsp;").append(dados.getHora());
            if (!dados.getDistancia_km().equals("")
                    && !dados.getDistancia_km().equals("0")
                    && null != dados.getDistancia_km()) {
                htmlRel.append("&nbsp;&nbsp;&nbsp;&nbsp;<i class=\"fa fa-map-pin\"></i>&nbsp;<label style=\"font-weight:bold\">").append(Messages.getMessageS("Distancia")).append("</label>&nbsp;").append(dados.getDistancia_km());
            }
            htmlRel.append("</label>");
            htmlRel.append("          </td>");
            htmlRel.append("          <td>");
            htmlRel.append("              <label class=\"badge badge-success\"><i class=\"fa fa-camera\" style=\"width:100%; text-align:center;\"></i>").append(!dados.getFotos().equals("") ? dados.getFotos().split(";").length : "0").append(" ").append(Messages.getMessageS("Fotos")).append("</label>");
            htmlRel.append("          </td>");
            htmlRel.append("       </tr>");
            htmlRel.append("    </table>");
            htmlRel.append(" </div>");

            /* RELATÓRIO TIME LINE */
            if (!UltimoDia.equals(dados.getData().split("-")[2].split(" ")[0])) {
                UltimoDia = dados.getData().split("-")[2].split(" ")[0];

                htmlTimeLine.append(" <span class=\"timeline__year\">");
                htmlTimeLine.append("    <span style=\"width:100%; text-align: center;display:block\">").append(UltimoDia).append("</span>");
                htmlTimeLine.append("    <span style=\"width:100%; text-align: center;display:block\">NOV</span>");
                htmlTimeLine.append(" </span>");
            }

            htmlTimeLine.append(" <div class=\"timeline__box\">");
            htmlTimeLine.append("    <div class=\"timeline__date\">");
            htmlTimeLine.append(dados.getHora());
            htmlTimeLine.append("    </div>");
            htmlTimeLine.append("    <div class=\"timeline__post\">");
            htmlTimeLine.append("       <div class=\"timeline__content\">");
            htmlTimeLine.append("          <label style=\"color:#3C8DBC; height:10px !important; font-size:10pt !important; width:100%;\"><i class=\"fa fa-user\"></i>&nbsp;").append(dados.getFuncionario()).append("</label>");
            htmlTimeLine.append("          <label style=\"font-weight:500; background-color:#505050; margin:0px !important; font-size:8pt !important; height:16px !important; padding-bottom:2px !important; color:#FFF; font-weight:bold; padding-left:8px; padding-right:8px; font-weight:bold; text-transform:uppercase; border-radius:4px;display:inline-block\">").append(dados.getTitulo()).append("</label>");
            htmlTimeLine.append("          <p><b>").append(Messages.getMessageS("Historico")).append("</b>:&nbsp;").append(dados.getDetalhes()).append("</p>");
            htmlTimeLine.append("       </div>");
            if (!dados.getFotos().equals("")
                    && null != dados.getFotos()) {
                htmlTimeLine.append("<label style=\"width:100%\">");
                for (int I = 0; I < dados.getFotos().split(";").length; I++) {
                    htmlTimeLine.append("<div class=\"col-md-6 FotoTimeLine\"><div class=\"col-md-12\" style=\"background-image:url('").append(dados.getFotos().split(";")[I]).append("')\"></div></div>");
                }
                htmlTimeLine.append("</label>");
            }
            htmlTimeLine.append("    </div>");
            htmlTimeLine.append(" </div>");
        }

        this.dadosRelatorioLine = htmlRel.toString();
        this.dadosRelatorioTimeLine = htmlTimeLine.toString();
    }

    public void carregarFuncionariosTela() {
        boolean TesteExiste;
        //this.functionariosSelecionadosFiltro = new ArrayList<>();
        this.funcionarios = new ArrayList<>();

        for (LogsSatMobEW dados : this.logsSatMobEWsResumo) {
            if (!dados.getFuncionario().equals("")) {
                Funcionarios funcItem = new Funcionarios();
                funcItem.setMatr(dados.getChave().split(";")[1]);
                funcItem.setSecao(dados.getSecao());
                funcItem.setNome(dados.getFuncionario());
                TesteExiste = false;

                for (Funcionarios funcionario : funcionarios) {
                    if (funcionario.getMatr().equals(funcItem.getMatr())) {
                        TesteExiste = true;
                        break;
                    }
                }

                if (null == secaoSelecionadosFiltro
                        || secaoSelecionadosFiltro.size() == 0
                        || (!TesteExiste && secaoSelecionadosFiltro.contains(funcItem.getSecao()))) {
                    funcionarios.add(funcItem);
                }
            }
        }

        // Ordenar Nomes
        Collections.sort(funcionarios, (Object o1, Object o2) -> {
            Funcionarios func1 = (Funcionarios) o1;
            Funcionarios func2 = (Funcionarios) o2;
            return func1.getNome().compareToIgnoreCase(func2.getNome());
        });
    }

    public void carregarEfetivos() throws Exception {
        // Lista de Efetivos
        LogsSatMobEWDao logDao = new LogsSatMobEWDao();
        List<Funcion> Retorno = logDao.consultaHistoricoEfetivo(this.secaoEfetivos, this.codFilEfetivos, this.persistencia);
        String retorno = "";

        if (Retorno.size() > 0) {
            for (Funcion Retorno1 : Retorno) {
                retorno += "<tr>";
                retorno += "  <td>" + Retorno1.getMatr().toPlainString().replace(".0", "") + "</td>";
                retorno += "  <td>" + Retorno1.getNome() + "</td>";
                retorno += "  <td>" + Retorno1.getCargo() + "</td>";
                retorno += "  <td>" + Retorno1.getFuncao() + "</td>";
                retorno += "  <td>" + Retorno1.getEscala() + "</td>";
                retorno += "  <td>" + Data(Retorno1.getDt_Admis()) + "</td>";
                retorno += "  <td>" + Data(Retorno1.getDt_Demis()) + "</td>";
                retorno += "</tr>";
            }
        } else {
            retorno = "<tr><td colspan=\"7\"><br><br></label style=\"width: 100%; text-align: center; font-size: 14pt; color: #999\">" + getMessageS("NaoHaDados") + "</label><br><br><br></td></tr>";
        }

        this.htmlEfetivos = retorno;

        // Lista de Pontos - Efetivos
        List<RHPontoDet> RetornoPto = logDao.consultaPontosEfetivo(this.data1Efetivos, this.data2Efetivos, this.secaoEfetivos, this.codFilEfetivos, this.persistencia);
        retorno = "";
        String DiaSemana = "";

        if (RetornoPto.size() > 0) {
            for (RHPontoDet RetornoPto1 : RetornoPto) {
                try {
                    Calendar c = Calendar.getInstance();
                    c.setTime(new SimpleDateFormat("yyyyMMdd").parse(RetornoPto1.getDtCompet()));
                    String tipo = "";
                    switch (c.get(Calendar.DAY_OF_WEEK)) {
                        case 1:
                            DiaSemana = getMessageS("Domingo");
                            break;
                        case 2:
                            DiaSemana = getMessageS("Segunda");
                            break;
                        case 3:
                            DiaSemana = getMessageS("Terça");
                            break;
                        case 4:
                            DiaSemana = getMessageS("Quarta");
                            break;
                        case 5:
                            DiaSemana = getMessageS("Quinta");
                            break;
                        case 6:
                            DiaSemana = getMessageS("Sexta");
                            break;
                        case 7:
                            DiaSemana = getMessageS("Sabado");
                            break;
                    }
                } catch (Exception e) {
                    DiaSemana = "";
                }

                retorno += "<tr>";
                retorno += "  <td>" + RetornoPto1.getMatr().replace(".0", "") + "</td>";
                retorno += "  <td>" + RetornoPto1.getNome() + "</td>";
                retorno += "  <td>" + RetornoPto1.getLocal() + "</td>";
                retorno += "  <td>" + Data(RetornoPto1.getDtCompet()) + "</td>";
                retorno += "  <td>" + DiaSemana + "</td>";
                retorno += "  <td>" + Data(RetornoPto1.getHora1()) + "</td>";
                retorno += "  <td>" + Data(RetornoPto1.getHora2()) + "</td>";
                retorno += "  <td>" + Data(RetornoPto1.getHora3()) + "</td>";
                retorno += "  <td>" + Data(RetornoPto1.getHora4()) + "</td>";
                retorno += "</tr>";
            }
        } else {
            retorno = "<tr><td colspan=\"9\"><br><br></label style=\"width: 100%; text-align: center; font-size: 14pt; color: #999\">" + getMessageS("NaoHaDados") + "</label><br><br><br></td></tr>";
        }

        this.htmlEfetivosPontos = retorno;
    }

    public void carregarResumoQdes(Boolean CarregarFuncion) throws Exception {
        // Limpeza de propriedades
        this.resumoQdeEntradas = "0";
        this.resumoQdeInspecoes = "0";
        this.resumoQdeOutros = "0";
        this.resumoQdeRelatorios = "0";
        this.resumoQdeRondas = "0";
        this.resumoQdeSaidas = "0";

        // Variaveis de cálculo
        int countEntradas = 0, countInspecoes = 0, countSaidas = 0, countRelatorios = 0, countOutros = 0, countRondas = 0;


        // Consulta e tratamento de dados
//        this.logsSatMobEWsResumo = new ArrayList<>();
        this.logsSatMobEWsResumo = this.satMobEWSatWeb.listagemResumo(0, 9999999, 
                this.filters, !this.supervisor ? this.codPessoa : null, 
                this.data1, this.data2, resumoCategoria, this.persistencia);
        if (funcionarios == null) {
            funcionarios = new ArrayList<>();
        }

        for (LogsSatMobEW dados : this.logsSatMobEWsResumo) {
            switch (dados.getTipo()) {
                case "1": // ENTRADA E SAÍDA
                    if (dados.getChave().split(";")[2].equals("2")
                            || dados.getChave().split(";")[2].equals("4")) {
                        countSaidas = countSaidas + 1;
                    } else {
                        countEntradas = countEntradas + 1;
                    }
                    break;
                case "3":// RELATÓRIO DE SUPERVISOR
                    countRelatorios = countRelatorios + 1;
                    break;
                case "2":
                case "10"://RELATÓRIO
                    countRelatorios = countRelatorios + 1;
                    break;
                case "4":
                case "6":// RONDA
                    countRondas = countRondas + 1;
                    break;
                case "9":// ROTA
                    countOutros = countOutros + 1;
                    break;
                case "5":// CHEGADA DE SUPERVISOR
                    countOutros = countOutros + 1;
                    break;
                case "7":// INSPEÇÃO
                    countInspecoes = countInspecoes + 1;
                    break;
                case "11": //ENTRADA
                    countEntradas = countEntradas + 1;
                    break;
                case "12": //SAÍDA
                    countSaidas = countSaidas + 1;
                    break;
            }
        }

        /*if (CarregarFuncion) {
            carregarFuncionariosTela();
        }*/
        // Guardar valores para display em "Tela"
        this.resumoQdeEntradas = Integer.toString(countEntradas);
        this.resumoQdeInspecoes = Integer.toString(countInspecoes);
        this.resumoQdeOutros = Integer.toString(countOutros);
        this.resumoQdeRelatorios = Integer.toString(countRelatorios);
        this.resumoQdeRondas = Integer.toString(countRondas);
        this.resumoQdeSaidas = Integer.toString(countSaidas);
    }

    public void selecionarDatas(SelectEvent event) {
        try {
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1 = this.datasSelecionadas.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.datasSelecionadas.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataInicio = this.datasSelecionadas.get(0);
                this.dataFim = this.datasSelecionadas.get(1);

                //SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
                //this.dataTela = tesedata.format(dataInicio);
                //this.filters.replace("data", "");
                //            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
                //            dt.setFilters(this.filters);
                setPaginacaoDesabilitada(false);
                limparPesquisa();
                limparFiltroCategoria();
                this.logsSatMobEWs = null;
                this.lazyGride = getAllLogs();
                carregarResumoQdes(true);
                PrimeFaces.current().ajax().update("main:tabela");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarDatasEfetivos(SelectEvent event) {
        try {
            this.datasSelecionadasEfetivos = (ArrayList) event.getObject();
            if (this.datasSelecionadasEfetivos.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1Efetivos = this.datasSelecionadasEfetivos.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2Efetivos = this.datasSelecionadasEfetivos.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataInicioEfetivos = this.datasSelecionadasEfetivos.get(0);
                this.dataFimEfetivos = this.datasSelecionadasEfetivos.get(1);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            this.filters.replace("data", this.dataTela);
//            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
//            dt.setFilters(this.filters);
            this.lazyGride = getAllLogs();
            carregarResumoQdes(true);
//            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            this.filters.replace("data", this.dataTela);
//            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
//            dt.setFilters(this.filters);
            this.lazyGride = getAllLogs();
            carregarResumoQdes(true);
//            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarData(SelectEvent event) {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, 0);
            date = calendar.getTime();

            this.dataTela = Date2String(date);
            this.filters.replace("data", this.dataTela);
            //        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            //        dt.setFilters(this.filters);
            this.lazyGride = getAllLogs();
            carregarResumoQdes(true);
            //        dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Tranforma Date em String
     *
     * @param date Data a ser formatada
     * @return String no formato yyyyMMdd
     */
    public String Date2String(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    public void selecionarPessoa(SelectEvent event) {
        abrirBoletimTrabalho(true);
    }

    private void carregarAtrasos() throws Exception {
        LogsSatMobEWDao obj = new LogsSatMobEWDao();

        if (null == this.funcionarioSelecionado) {
            this.funcionarioSelecionado = new Funcion();
        }

        List<RHPontoDet> listaAtrasos = obj.listaAtrasos(this.codFil, this.dataTela, null, this.funcionarioSelecionado.getMatr(), this.persistencia);
        StringBuilder str = new StringBuilder();

        this.atrasadosQde = Integer.toString(listaAtrasos.size());

        if (listaAtrasos.size() > 0) {
            this.qtdeAtrasados = Integer.toString(listaAtrasos.size());

            str.append("<table class=\"GridDash\" ref=\"Atrasos\">");
            str.append("  <thead>");
            str.append("    <tr>");
            str.append("       <th>").append(getMessageS("Matr")).append("</th>");
            str.append("       <th>").append(getMessageS("Nome")).append("</th>");
            str.append("       <th>").append(getMessageS("Diferenca")).append("</th>");
            str.append("       <th>").append(getMessageS("HoraRegistrada")).append("</th>");
            str.append("       <th>").append(getMessageS("HoraPrevista")).append("</th>");
            str.append("    </tr>");
            str.append("  </thead>");
            str.append("  <tbody>");

            for (RHPontoDet listaAtraso : listaAtrasos) {
                str.append("    <tr>");
                str.append("      <td>").append(listaAtraso.getMatr().replace(".0", "")).append("</td>");
                str.append("      <td>").append(listaAtraso.getNome().toUpperCase()).append("</td>");
                str.append("      <td>").append(listaAtraso.getAtraso()).append("</td>");
                str.append("      <td>").append(listaAtraso.getHoraRegistro()).append("</td>");
                str.append("      <td>").append(listaAtraso.getHoraPrevista()).append("</td>");
                str.append("    </tr>");
            }

            str.append("  </tbody>");
            str.append("</table>");
        } else {
            // NÃO EXISTEM ATRASOS
            this.qtdeAtrasados = "0";
            str.append("<label style=\"position: absolute; font-size: 14pt !important; height: 25px; width: 100%; top: 0; right: 0; bottom: 0; left: 0; margin: auto !important; text-align: center; color: #BBB !important;\">").append(getMessageS("NaoHaDados")).append("</label>");
        }

        this.htmlAtrasados = str.toString();
    }

    private void carregarFerias() throws Exception {
        LogsSatMobEWDao obj = new LogsSatMobEWDao();
        List<FuncionFerias> listaFerias = obj.listaFerias(this.codFil, this.dataTela, this.funcionarioSelecionado.getMatr(), this.persistencia);
        StringBuilder str = new StringBuilder();

        this.feriasQtde = Integer.toString(listaFerias.size());

        if (listaFerias.size() > 0) {
            this.qtdeFerias = Integer.toString(listaFerias.size());

            str.append("<table class=\"GridDash\" ref=\"Ferias\">");
            str.append("  <thead>");
            str.append("    <tr>");
            str.append("       <th rowspan=\"2\">").append(getMessageS("Matr")).append("</th>");
            str.append("       <th rowspan=\"2\">").append(getMessageS("Nome")).append("</th>");
            str.append("       <th rowspan=\"2\">").append(getMessageS("Inicio")).append("</th>");
            str.append("       <th rowspan=\"2\">").append(getMessageS("Fim")).append("</th>");
            str.append("       <th rowspan=\"2\">").append(getMessageS("Data")).append("</th>");
            str.append("       <th colspan=\"2\" style=\"line-height: 10px !important\">").append(getMessageS("Substituto")).append("</th>");
            str.append("    </tr>");
            str.append("    <tr>");
            str.append("       <th>").append(getMessageS("Nome")).append("</th>");
            str.append("       <th>").append(getMessageS("Matr")).append("</th>");
            str.append("    </tr>");
            str.append("  </thead>");
            str.append("  <tbody>");

            for (FuncionFerias listaFerias1 : listaFerias) {
                str.append("    <tr>");
                str.append("      <td>").append(listaFerias1.getMatr().replace(".0", "")).append("</td>");
                str.append("      <td>").append(listaFerias1.getNome().toUpperCase().toUpperCase()).append("</td>");
                str.append("      <td>").append(Data(listaFerias1.getDtInicioFer())).append("</td>");
                str.append("      <td>").append(Data(listaFerias1.getDtFinalFer())).append("</td>");
                str.append("      <td>").append(Data(listaFerias1.getData())).append("</td>");
                str.append("      <td>").append(listaFerias1.getNome_Subs().toUpperCase()).append("</td>");
                str.append("      <td>").append(listaFerias1.getMatr_Subs().replace(".0", "")).append("</td>");
                str.append("    </tr>");
            }

            str.append("  </tbody>");
            str.append("</table>");
        } else {
            // NÃO EXISTEM ATRASOS
            this.qtdeFerias = "0";
            str.append("<label style=\"position: absolute; font-size: 14pt !important; height: 25px; width: 100%; top: 0; right: 0; bottom: 0; left: 0; margin: auto !important; text-align: center; color: #BBB !important;\">").append(getMessageS("NaoHaDados")).append("</label>");
        }

        this.htmlFerias = str.toString();
    }

    private void carregarFaltas() throws Exception {
        LogsSatMobEWDao obj = new LogsSatMobEWDao();
        List<FuncionFaltas> listaFaltas = obj.listaFaltas(this.codFil, this.dataTela, this.funcionarioSelecionado.getMatr(), this.persistencia);
        StringBuilder str = new StringBuilder();

        this.faltasQtde = Integer.toString(listaFaltas.size());

        if (listaFaltas.size() > 0) {
            this.qtdeFaltas = Integer.toString(listaFaltas.size());

            str.append("<table class=\"GridDash\" ref=\"Faltas\">");
            str.append("  <thead>");
            str.append("    <tr>");
            str.append("       <th rowspan=\"2\">").append(getMessageS("Matr")).append("</th>");
            str.append("       <th rowspan=\"2\">").append(getMessageS("Nome")).append("</th>");
            str.append("       <th rowspan=\"2\">").append(getMessageS("Data")).append("</th>");
            str.append("       <th rowspan=\"2\">").append(getMessageS("Posto")).append("</th>");
            str.append("       <th rowspan=\"2\">").append(getMessageS("Local")).append("</th>");
            str.append("       <th colspan=\"2\" style=\"line-height: 10px !important\">").append(getMessageS("Substituto")).append("</th>");
            str.append("    </tr>");
            str.append("    <tr>");
            str.append("       <th>").append(getMessageS("Nome")).append("</th>");
            str.append("       <th>").append(getMessageS("Matr")).append("</th>");
            str.append("    </tr>");
            str.append("  </thead>");
            str.append("  <tbody>");

            for (FuncionFaltas listaFaltas1 : listaFaltas) {
                str.append("    <tr>");
                str.append("      <td>").append(listaFaltas1.getMatr().replace(".0", "")).append("</td>");
                str.append("      <td>").append(listaFaltas1.getNome().toUpperCase().toUpperCase()).append("</td>");
                str.append("      <td>").append(Data(listaFaltas1.getData())).append("</td>");
                str.append("      <td>").append(listaFaltas1.getPosto().toUpperCase()).append("</td>");
                str.append("      <td>").append(listaFaltas1.getLocal().toUpperCase()).append("</td>");
                str.append("      <td>").append(listaFaltas1.getNomeSubs().toUpperCase()).append("</td>");
                str.append("      <td>").append(listaFaltas1.getMatrCober().replace(".0", "")).append("</td>");
                str.append("    </tr>");
            }

            str.append("  </tbody>");
            str.append("</table>");
        } else {
            // NÃO EXISTEM ATRASOS
            this.qtdeFaltas = "0";
            str.append("<label style=\"position: absolute; font-size: 14pt !important; height: 25px; width: 100%; top: 0; right: 0; bottom: 0; left: 0; margin: auto !important; text-align: center; color: #BBB !important;\">").append(getMessageS("NaoHaDados")).append("</label>");
        }

        this.htmlFaltas = str.toString();
    }

    public void carregarTodosTiposDash() throws Exception {
        try {
            carregarAtrasos();
            carregarFerias();
            carregarFaltas();
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void selecionarPessoaUltimaComunicacao(SelectEvent event) {
        abrirUltimaComunicacao(true);
    }

    public void abrirUltimaComunicacao(boolean Reload) {
        try {
            if (!Reload || null == this.pessoaSelecionada) {
                this.pessoaSelecionada = new Pessoa();
            }

            carregarAtrasos();
            carregarFerias();
            carregarFaltas();

            LogsSatMobEWDao obj = new LogsSatMobEWDao();

            // Carregar Pessoas (PINs)
            List<RastrearEW> listaRastrearEW = obj.listaUltimaComunicacao(this.dataTela, this.pessoaSelecionada.getCodigo(), this.persistencia);
            this.markers = "";
            this.centro = "";
            int Contador = 0;

            if (listaRastrearEW.size() > 0) {
                for (RastrearEW listaRastrearEW1 : listaRastrearEW) {
                    if (this.centro.equals("")) {
                        this.centro = "{ lat: " + listaRastrearEW1.getLatitude() + ", lng: " + listaRastrearEW1.getLongitude() + " }";
                    }

                    String HoraFormatada = "";
                    DateTimeFormatter df;
                    Mascaras mascaras = new Mascaras();
                    try {
                        df = DateTimeFormatter.ofPattern("HH:mm");
                        HoraFormatada = LocalTime.parse(listaRastrearEW1.getHora(), df).format(DateTimeFormatter.ofPattern(mascaras.getPadraoHora()));
                    } catch (Exception e) {
                        HoraFormatada = listaRastrearEW1.getHora();
                    }

                    this.markers += MARCADOR.replace("@indice", "pin_" + Integer.toString(Contador))
                            .replace("@lat", listaRastrearEW1.getLatitude()).replace("@lng", listaRastrearEW1.getLongitude())
                            .replace("@title", getMessageS("Nome") + ": " + listaRastrearEW1.getNome() + "\\n" + getMessageS("Horario") + ": " + HoraFormatada)
                            .replace("@icon", "https://mobile.sasw.com.br:9091/satmobile/pins/icone_mobile_supervisor.png");

                    Contador++;
                }
            }

            // Carregar Pessoas
            this.pessoas = new ArrayList<>();
            this.pessoas = obj.listaUltimaComunicacaoPessoas(this.dataTela, this.persistencia);

            // Carregar Funcionarios
            this.funcionariosDash = new ArrayList<>();
            this.funcionariosDash = obj.listaFuncionarios(this.codFil, this.persistencia);

            // Abrir Modal
            if (!Reload) {
                PrimeFaces.current().ajax().update("formUltimaComunicacao");
                PrimeFaces.current().executeScript("PF('dlgUltimaComunicacao').show()");
            }

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void abrirBoletimTrabalho(boolean Reload) {
        try {
            if (!Reload || null == this.pessoaSelecionada) {
                this.pessoaSelecionada = new Pessoa();
            }
            LogsSatMobEWDao obj = new LogsSatMobEWDao();

            // Carregar Lista de Trabalho
            this.pstInspecoesBoletimTrabalho = obj.listaBoletimTrabalho(this.dataTela, this.codFil, this.pessoaSelecionada.getCodigo(), this.persistencia);
            StringBuilder str = new StringBuilder();

            for (PstInspecao pstInspecoesBoletimTrabalho1 : this.pstInspecoesBoletimTrabalho) {
                str.append("ArrayBoletimTrabalho.push({");
                str.append("  Nred: '").append(pstInspecoesBoletimTrabalho1.getLocal()).append("',");
                str.append("  Latitude: '").append(pstInspecoesBoletimTrabalho1.getLatitude()).append("',");
                str.append("  Longitude: '").append(pstInspecoesBoletimTrabalho1.getLongitude()).append("',");
                str.append("  Pergunta: '").append(pstInspecoesBoletimTrabalho1.getPergunta()).append("',");
                str.append("  Resposta: '").append(pstInspecoesBoletimTrabalho1.getResposta()).append("'");
                str.append("});");
            }

            this.listaTrabalhos = str.toString();

            // Carregar Clientes (PINs)
            List<Clientes> lstClientes = obj.listaBoletimTrabalhoClientes(this.dataTela, this.codFil, this.pessoaSelecionada.getCodigo(), this.persistencia);
            this.markers = "";
            this.centro = "";
            int Contador = 0;

            for (Clientes lstClientes1 : lstClientes) {
                if (this.centro.equals("")) {
                    this.centro = "{ lat: " + lstClientes1.getLatitude() + ", lng: " + lstClientes1.getLongitude() + " }";
                }

                String HoraFormatada = "";
                DateTimeFormatter df;
                Mascaras mascaras = new Mascaras();
                try {
                    df = DateTimeFormatter.ofPattern("HH:mm");
                    HoraFormatada = LocalTime.parse(lstClientes1.getHr_Alter(), df).format(DateTimeFormatter.ofPattern(mascaras.getPadraoHora()));
                } catch (Exception e) {
                    HoraFormatada = lstClientes1.getHr_Alter();
                }

                this.markers += MARCADOR.replace("@indice", "pin_" + Integer.toString(Contador))
                        .replace("@lat", lstClientes1.getLatitude()).replace("@lng", lstClientes1.getLongitude())
                        .replace("@title", getMessageS("Local") + ": " + lstClientes1.getNRed() + "\\n" + getMessageS("Endereco") + ": " + lstClientes1.getEnde() + "\\n" + getMessageS("Bairro") + ": " + lstClientes1.getBairro() + "\\n" + getMessageS("Cidade") + ": " + lstClientes1.getCidade() + "\\n" + getMessageS("Horario") + ": " + HoraFormatada)
                        .replace("@icon", "https://mobile.sasw.com.br:9091/satmobile/pins/icone_mobile_supervisor.png");

                Contador++;
            }

            // Carregar Pessoas
            this.pessoas = new ArrayList<>();
            this.pessoas = obj.listaBoletimTrabalhoPessoas(this.dataTela, this.codFil, this.persistencia);

            // Abrir Modal
            if (!Reload) {
                PrimeFaces.current().ajax().update("formBoletimTrabalho");
                PrimeFaces.current().executeScript("PF('dlgBoletimTrabalho').show()");
            }
            PrimeFaces.current().executeScript("CarregarListaTrabalho('" + Integer.toString(lstClientes.size()) + "')");

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    /**
     * Procura o último dia do mês atual
     */
    public static void ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONDAY) + 1);
        int ano = calendar.get(Calendar.YEAR);

        try {
            ultimoDia = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void rowDblselect(SelectEvent event) {
        this.logsSatMobEWSelecionado = (LogsSatMobEW) event.getObject();
        buttonAction(null);
    }

    public void rowSelect(SelectEvent event) {
        this.logsSatMobEWSelecionado = (LogsSatMobEW) event.getObject();
    }

    public void buttonActionEfetivos() {
        try {
            this.data1Efetivos = this.data1;
            this.data2Efetivos = this.data2;
            this.dataInicioEfetivos = this.dataInicio;
            this.dataFimEfetivos = this.dataFim;

            this.codFilEfetivos = this.logsSatMobEWFiltro.getCodfil();

            this.datasSelecionadasEfetivos = this.datasSelecionadas;

            atualizarListaPostosEfetivos();

            this.htmlEfetivos = "<tr><td colspan=\"7\"><br><br></label style=\"width: 100%; text-align: center; font-size: 14pt; color: #999\">" + getMessageS("CliquePesquisar") + "</label><br><br><br></td></tr>";
            this.htmlEfetivosPontos = "<tr><td colspan=\"9\"><br><br></label style=\"width: 100%; text-align: center; font-size: 14pt; color: #999\">" + getMessageS("CliquePesquisar") + "</label><br><br><br></td></tr>";

            PrimeFaces.current().ajax().update("formEfetivos");
            PrimeFaces.current().executeScript("PF('dlgEfetivos').show();");
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void buttonAction(LogsSatMobEW logsSatMobEW) {
        this.logsSatMobEWSelecionado = logsSatMobEW;
        if (null == this.logsSatMobEWSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneRelatorio"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                if (!this.logsSatMobEWSelecionado.getTitulo().equals("RESUMO DO TRABALHO DE CAMPO")) {
                    this.html = "";
                    this.posFotoRelatorio = 0;
                    this.fotosRelatorio = new ArrayList<>();
                    this.fotoRelatorio = "";
                    this.rondas = new ArrayList<>();
                    this.contatos = new ArrayList<>();
                    this.pstInspecaoSelecionadaDetalhes = new ArrayList<>();
                    gerarRelatorio();
                    this.video = this.fotoRelatorio != null && this.fotoRelatorio.contains("mp4");

                    if (this.logsSatMobEWSelecionado.getTipo().equals("1")) {
                        carregarPontoMensal();

                        String[] vetorChave = logsSatMobEWSelecionado.getChave().
                                split(";");
                        batidaPontoFoto = preencherURL(
                                logsSatMobEWSelecionado.getURL(),
                                persistencia.getEmpresa(), vetorChave[0], 
                                vetorChave[1], vetorChave[2]);
                        
                        String idioma = FacesContext.getCurrentInstance().getViewRoot().getLocale().getLanguage();
                        FuncionDao funcionDao = new FuncionDao();
                        FiliaisDao filiaisDao = new FiliaisDao();
                        FuncionPstServ funcion = funcionDao.listaFuncionFilialMatr(this.logsSatMobEWSelecionado.getChave().split(";")[3], this.logsSatMobEWSelecionado.getChave().split(";")[0], this.logsSatMobEWSelecionado.getChave().split(";")[1], persistencia);
                        Filiais filial = filiaisDao.getFilial(this.logsSatMobEWSelecionado.getChave().split(";")[3], persistencia);

                        String Classe = "";
                        String empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");
                        String Logo = getLogo(empresa, "0");
                        String TipoRelatorio = "";
                        if (S2I(this.logsSatMobEWSelecionado.getFotos()) % 2 == 0) {
                            this.batidaPontoTipo = "0";
                            TipoRelatorio = getMessageS("CheckOutReport");
                            this.titulo = getMessageS("Saida");
                            Classe = "batida-saida";
                        } else {
                            this.batidaPontoTipo = "1";
                            TipoRelatorio = getMessageS("CheckInReport");
                            this.titulo = getMessageS("Entrada");
                            Classe = "batida-entrada";
                        }

                        String HTML = "", Telefone = idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####") : FuncoesString.formatarString(filial.getFone(), "(##) ########?");

                        HTML += "<div class=\"col-md-12 col-sm-12 col-xs-12 divTopoBatida\">";
                        HTML += "   <table class=\"DadosCabecalhoPonto\">";
                        HTML += "     <tr>";
                        HTML += "        <td rowspan=\"5\" style=\"vertical-align: middle; text-align: center; padding-right: 8px !important\"><img src=\"" + Logo + "\" /></td>";
                        HTML += "        <td class=\"" + Classe + "\">" + TipoRelatorio + "</td>";
                        HTML += "     </tr>";
                        HTML += "     <tr>";
                        HTML += "       <td style=\"border-bottom: thin dashed #DDD;\">" + funcion.getPstserv().getLocal() + "</td>";
                        HTML += "     </tr>";
                        HTML += "     <tr>";
                        HTML += "       <td style=\"border-bottom: thin dashed #DDD;\">" + funcion.getPstserv().getDescContrato() + "</td>";
                        HTML += "     </tr>";
                        HTML += "     <tr>";
                        HTML += "       <td style=\"border-bottom: thin dashed #DDD;\">" + filial.getRazaoSocial() + "</td>";
                        HTML += "     </tr>";
                        HTML += "     <tr>";
                        HTML += "       <td>" + Telefone + "</td>";
                        HTML += "     </tr>";
                        HTML += "   </table>";
                        HTML += "</div>";

                        this.batidaPontoHtmlTopo = HTML;

                        // Historico de Pontos
                        LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
                        List<LogsSatMobEW> objHistorico = logsSatMobEWDao.obterLogsHistorico(0, 1000, this.logsSatMobEWSelecionado.getData().split(" ")[0].replace("-", ""), this.logsSatMobEWSelecionado.getCodfil(), funcion.getFuncion().getMatr().toPlainString().replace(".0", ""), "", "", "", "", null, this.logsSatMobEWSelecionado.getTipo(), this.logsSatMobEWSelecionado.getHora(), persistencia);

                        HTML = "";

                        if (objHistorico.size() > 0) {
                            String ClasseHistorico = "", TituloHistorico = "", FotoHistorico = "";

                            for (LogsSatMobEW objHistorico1 : objHistorico) {

                                if (S2I(objHistorico1.getFotos()) % 2 == 0) {
                                    TituloHistorico = getMessageS("Saida");
                                    ClasseHistorico = "batida-saida";
                                } else {
                                    TituloHistorico = getMessageS("Entrada");
                                    ClasseHistorico = "batida-entrada";
                                }

                                vetorChave = objHistorico1.getChave().
                                        split(";");
                                FotoHistorico = preencherURL(objHistorico1.getURL(),
                                        persistencia.getEmpresa(), vetorChave[0], 
                                        vetorChave[1], vetorChave[2]);

                                HTML += "    <div class=\"col-md-12 col-sm-12 col-xs-12\" style=\"height: 136px; padding-top: 10px; border: 2px solid #DDD; border-radius: 6px; box-shadow: 2px 2px 4px #EEE, -2px -2px 4px #EEE; margin-top: 6px;\">";
                                HTML += "             <div title=\"" + getMessageS("CliqueVisualizarFoto") + "\" style=\"float: left; background-color: #EEE; margin-top: 8px !important; border: 2px solid #DDD; width: 100px; height: 100px; border-radius: 50%; background-image: url(" + FotoHistorico + "); background-size: cover; background-position: center center; cursor: pointer;\" onclick=\"AbrirFoto('" + FotoHistorico + "');\"></div>";
                                HTML += "             <div style=\"height: 120px; float: left; width: calc(100% - 118px); margin-left: 18px\">";
                                HTML += "                 <table class=\"DetalhesPonto\" style=\"width: 100%\">";
                                HTML += "                     <tr>";
                                HTML += "                         <td>" + objHistorico1.getFuncionario() + "</td>";
                                HTML += "                     </tr>";
                                HTML += "                     <tr>";
                                HTML += "                         <td><label class=\"" + ClasseHistorico + "\" style=\"width: 130px !important;\">" + TituloHistorico + "</label></td>";
                                HTML += "                     </tr>";
                                HTML += "                     <tr>";
                                HTML += "                         <td><i class=\"fa fa-clock-o\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + getMessageS("Hora") + ": </b>" + objHistorico1.getHora() + "</td>";
                                HTML += "                     </tr>";
                                HTML += "                     <tr>";
                                HTML += "                         <td><i class=\"fa fa-id-card-o\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + getMessageS("Cargo") + ": </b>" + objHistorico1.getCargo() + "</td>";
                                HTML += "                     </tr>";
                                HTML += "                     <tr>";
                                HTML += "                         <td><i class=\"fa fa-map-marker\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + getMessageS("Local") + ": </b>" + objHistorico1.getPosto() + "</td>";
                                HTML += "                     </tr>";
                                HTML += "                     <tr>";
                                HTML += "                         <td><i class=\"fa fa-map-pin\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + getMessageS("Distancia") + ": </b>" + objHistorico1.getDistancia_km() + "</td>";
                                HTML += "                     </tr>";
                                HTML += "                 </table>";
                                HTML += "             </div>";
                                HTML += "         </div>";
                            }

                            this.batidaPontoHtmlHistorico = HTML;
                        } else {
                            this.batidaPontoHtmlHistorico = "<label style=\"font-size: 16pt; color: #BBB; width: 100%; text-align: center; margin-top: 30px;\">" + getMessageS("SemRegistrosAnteriores") + "</label>";
                        }

                        PrimeFaces.current().executeScript("PF('dlgBatidaPonto').show();");
                    } else {
                        // Demais Relatorios
                        PrimeFaces.current().resetInputs("relatorio:cadastrar");
                        PrimeFaces.current().executeScript("PF('dlgRelatorio').show();");
                    }
                } else {
                    abrirBoletimTrabalho(false);
                }
            } catch (Exception e) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    private RHPonto consultarRHPonto(String chave) throws Exception {
        String[] vetorChave = chave.split(";");
        RHPonto umRHPonto = umRHPontoDao.buscaPonto(
                vetorChave[0], vetorChave[1], vetorChave[2],
                persistencia);
        return umRHPonto;
    }

    public void webBatidaPonto() {
        try {
            if (null != this.webPontoLatitude
                    && !this.webPontoLatitude.equals("")
                    && null != this.webPontoLongitude
                    && !this.webPontoLongitude.equals("")) {
                PrimeFaces.current().executeScript("$('[id*=\"uploadFoto_input\"]').click();");
            } else {
                PrimeFaces.current().executeScript("$.MsgBoxAzulOk('" + getMessageS("Aviso") + "', '" + getMessageS("MensagemLigarGPS") + "');");
            }
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void webBatidaPontoHistoricoMensal() {
        try {
            this.batidaPontoHtmlHistoricoMensal = "";
            FuncionDao funcionDao = new FuncionDao();
            FuncionPstServ funcion = funcionDao.listaFuncionFilialMatr(FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("CodFil").toString(),
                    this.dataTela,
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("Matr").toString(),
                    this.persistencia);

            // Historico de Pontos
            LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
            List<LogsSatMobEW> objHistorico = logsSatMobEWDao.obterLogsHistoricoMensal(0,
                    1000,
                    this.dataTela,
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("CodFil").toString(),
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("Matr").toString(),
                    "", "", "", "", null, "1", "", this.persistencia);

            String HTML = "", UltimaData = "";

            if (objHistorico.size() > 0) {
                String ClasseHistorico = "", TituloHistorico = "", FotoHistorico = "";

                for (LogsSatMobEW objHistorico1 : objHistorico) {

                    if (S2I(objHistorico1.getFotos()) % 2 == 0) {
                        TituloHistorico = getMessageS("Saida");
                        ClasseHistorico = "batida-saida";
                    } else {
                        TituloHistorico = getMessageS("Entrada");
                        ClasseHistorico = "batida-entrada";
                    }

                    FotoHistorico = SERVIDOR_MOBILE_FOTO + this.persistencia.getEmpresa() + "/ponto/";
                    FotoHistorico += FuncoesString.RecortaString(objHistorico1.getChave().split(";")[0], 0, 10).replaceAll("-", "") + "/";
                    FotoHistorico += FuncoesString.PreencheEsquerda(objHistorico1.getChave().split(";")[1].replace(".0", ""), 8, "0")
                            + "_" + objHistorico1.getFotos().replace(".0", "") + ".jpg";

                    if (!UltimaData.equals(objHistorico1.getData())) {
                        HTML += "<label ref=\"mensal-data\" style=\"background-color: steelblue; color: #FFF; font-weight: bold; padding: 4px 10px 4px 20px; margin: 8px 0px 0px 0px !important; width: 100%; border-radius:20px; width: 100%\"><i class=\"fa fa-calendar\" aria-hidden=\"true\"></i>&nbsp;&nbsp;" + Data(objHistorico1.getData()) + "</label>";
                    }

                    HTML += "    <div class=\"col-md-12 col-sm-12 col-xs-12\" style=\"height: 98px; padding: 8px 0px 8px 10px !important; border: thin solid #DDD; border-radius: 5px; margin-top: 6px; background-color: #FAFAFA !important\">";
                    HTML += "             <div title=\"" + getMessageS("CliqueVisualizarFoto") + "\" style=\"float: left; background-color: #EEE; border: 2px solid #DDD; width: 80px; height: 80px; border-radius: 50%; background-image: url(" + FotoHistorico + "); background-size: cover; background-position: center center; cursor: pointer;\" onclick=\"AbrirFoto('" + FotoHistorico + "');\"></div>";
                    HTML += "             <div style=\"height: 100px; float: left; width: calc(100% - 114px); margin-left: 14px; padding-top: 0px !important\">";
                    HTML += "                 <table class=\"DetalhesPonto\" style=\"width: 100%\">";
                    HTML += "                     <tr>";
                    HTML += "                         <td>" + objHistorico1.getFuncionario() + "</td>";
                    HTML += "                     </tr>";
                    HTML += "                     <tr>";
                    HTML += "                         <td><label class=\"" + ClasseHistorico + "\" style=\"width: 100px !important;\">" + TituloHistorico + "</label></td>";
                    HTML += "                     </tr>";
                    HTML += "                     <tr>";
                    HTML += "                         <td><i class=\"fa fa-clock-o\" aria-hidden=\"true\" style=\"width: 25px; text-align: center\"></i><b>" + getMessageS("Hora") + ": </b>" + objHistorico1.getHora() + "</td>";
                    HTML += "                     </tr>";
                    HTML += "                     <tr>";
                    HTML += "                         <td><i class=\"fa fa-map-marker\" aria-hidden=\"true\" style=\"width: 25px; text-align: center\"></i><b>" + getMessageS("Local") + ": </b>" + objHistorico1.getPosto() + "</td>";
                    HTML += "                     </tr>";
                    HTML += "                     <tr>";
                    HTML += "                         <td><i class=\"fa fa-map-pin\" aria-hidden=\"true\" style=\"width: 25px; text-align: center\"></i><b>" + getMessageS("Distancia") + ": </b>" + objHistorico1.getDistancia_km() + "</td>";
                    HTML += "                     </tr>";
                    HTML += "                 </table>";
                    HTML += "             </div>";
                    HTML += "         </div>";

                    UltimaData = objHistorico1.getData();
                }

                this.batidaPontoHtmlHistoricoMensal = HTML;
            } else {
                this.batidaPontoHtmlHistoricoMensal = "<label style=\"font-size: 16pt; color: #BBB; width: 100%; text-align: center; margin-top: 30px;\">" + getMessageS("SemRegistrosAnteriores") + "</label>";
            }
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void webBatidaHistorico() {
        try {
            LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
            List<LogsSatMobEW> objHistorico = logsSatMobEWDao.obterLogsHistorico(0,
                    1000,
                    this.dataTela,
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("CodFil").toString(),
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("Matr").toString(),
                    "",
                    "",
                    "",
                    "",
                    null,
                    "1",
                    "",
                    this.persistencia);

            String HTML = "";
            mapaLocalPonto = "";

            if (objHistorico.size() > 0) {
                String ClasseHistorico = "", TituloHistorico = "", FotoHistorico = "";

                for (LogsSatMobEW objHistorico1 : objHistorico) {

                    if (S2I(objHistorico1.getFotos()) % 2 == 0) {
                        TituloHistorico = getMessageS("Saida");
                        ClasseHistorico = "batida-saida";
                    } else {
                        TituloHistorico = getMessageS("Entrada");
                        ClasseHistorico = "batida-entrada";
                    }

                    FotoHistorico = SERVIDOR_MOBILE_FOTO + this.persistencia.getEmpresa() + "/ponto/";
                    FotoHistorico += FuncoesString.RecortaString(objHistorico1.getChave().split(";")[0], 0, 10).replaceAll("-", "") + "/";
                    FotoHistorico += FuncoesString.PreencheEsquerda(objHistorico1.getChave().split(";")[1].replace(".0", ""), 8, "0")
                            + "_" + objHistorico1.getFotos().replace(".0", "") + ".jpg";

                    HTML += "    <div class=\"col-md-12 col-sm-12 col-xs-12\" style=\"height: 98px; padding: 8px 0px 8px 10px !important; border: thin solid #DDD; border-radius: 5px; margin-top: 6px; background-color: #FAFAFA !important\">";
                    HTML += "             <div title=\"" + getMessageS("CliqueVisualizarFoto") + "\" style=\"float: left; background-color: #EEE; border: 2px solid #DDD; width: 80px; height: 80px; border-radius: 50%; background-image: url(" + FotoHistorico + "); background-size: cover; background-position: center center; cursor: pointer;\" onclick=\"AbrirFoto('" + FotoHistorico + "');\"></div>";
                    HTML += "             <div style=\"height: 100px; float: left; width: calc(100% - 114px); margin-left: 14px; padding-top: 0px !important\">";
                    HTML += "                 <table class=\"DetalhesPonto\" style=\"width: 100%\">";
                    HTML += "                     <tr>";
                    HTML += "                         <td>" + objHistorico1.getFuncionario() + "</td>";
                    HTML += "                     </tr>";
                    HTML += "                     <tr>";
                    HTML += "                         <td><label class=\"" + ClasseHistorico + "\" style=\"width: 100px !important;\">" + TituloHistorico + "</label></td>";
                    HTML += "                     </tr>";
                    HTML += "                     <tr>";
                    HTML += "                         <td><i class=\"fa fa-clock-o\" aria-hidden=\"true\" style=\"width: 25px; text-align: center\"></i><b>" + getMessageS("Hora") + ": </b>" + objHistorico1.getHora() + "</td>";
                    HTML += "                     </tr>";
                    HTML += "                     <tr>";
                    HTML += "                         <td><i class=\"fa fa-map-marker\" aria-hidden=\"true\" style=\"width: 25px; text-align: center\"></i><b>" + getMessageS("Local") + ": </b>" + objHistorico1.getPosto() + "</td>";
                    HTML += "                     </tr>";
                    HTML += "                     <tr>";
                    HTML += "                         <td><i class=\"fa fa-map-pin\" aria-hidden=\"true\" style=\"width: 25px; text-align: center\"></i><b>" + getMessageS("Distancia") + ": </b>" + objHistorico1.getDistancia_km() + "</td>";
                    HTML += "                     </tr>";
                    HTML += "                     <tr>";
                    HTML += "                         <td><iframe id=\"ifrMapa\" src=\"https://mobile.sasw.com.br/SatMobWeb/tmpMapaFree.html?lat='" + objHistorico1.getLatitude() + "'&lon='" + objHistorico1.getLongitude() + "'&marcador=pin_funcionario.png\" style=\"width: 100%; height: 100%; border: none; margin: 0px; paddin: 0px;\"></iframe>'";
                    HTML += "                     </tr>";
                    HTML += "                 </table>";
                    HTML += "             </div>";
                    HTML += "         </div>";
                    
                    // Fazer mapa para o primeiro registro de ponto
                    if ("".equals(mapaLocalPonto)) {
                        mapaLocalPonto = "<iframe id=\"ifrMapa\" src=\"https://mobile.sasw.com.br/SatMobWeb/tmpMapaFree.html?lat=" + 
                                objHistorico1.getLatitude() + "&lon=" + 
                                objHistorico1.getLongitude() +  
                                "&marcador=pin_funcionario.png\" style=\"width: 600px; height: 400px; border: none; margin: 0px; paddin: 0px;\"></iframe>'";
                    }
                }

                
                this.batidaPontoHtmlHistorico = HTML;
            } else {
                this.batidaPontoHtmlHistorico = "<label style=\"font-size: 16pt; color: #BBB; width: 100%; text-align: center; margin-top: 30px;\">" + getMessageS("SemRegistrosAnteriores") + "</label>";
            }
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void baterPonto() {
        try {
            String PontoData = DataAtual.getDataAtual("SQL"),
                    PontoHora = DataAtual.getDataAtual("HORA");

            String UrlBatidaPonto = "https://mobile.sasw.com.br/SatMobEW/ponto/RealizarBatida";
            String Params = "";
            Params += FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("cod_pessoa").toString();
            Params += "|**|" + FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("PwWeb").toString();
            Params += "|**|" + this.persistencia.getEmpresa();
            Params += "|**|" + FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("Matr").toString();
            Params += "|**|" + FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("CodFil").toString();
            Params += "|**|" + PontoData;
            Params += "|**|" + PontoHora;
            Params += "|**|" + FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("Nome_Guer").toString();
            Params += "|**|" + this.webPontoLatitude;
            Params += "|**|" + this.webPontoLongitude;
            Params += "|**|" + FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("Secao").toString();
            Params += "|**|" + PontoData;
            Params += "|**|" + PontoHora;
            Params += "|**|" + FacesContext.getCurrentInstance().getViewRoot().getLocale().getLanguage();
            Params += "|**|";

            PrimeFaces.current().executeScript("ExecBatidaPonto('" + UrlBatidaPonto + "', '" + Params + "');");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void carregarPontoMensal() throws Exception {
        this.batidaPontoHtmlHistoricoMensal = "";
        FuncionDao funcionDao = new FuncionDao();
        FuncionPstServ funcion = funcionDao.listaFuncionFilialMatr(this.logsSatMobEWSelecionado.getChave().split(";")[3], this.logsSatMobEWSelecionado.getChave().split(";")[0], this.logsSatMobEWSelecionado.getChave().split(";")[1], persistencia);

        // Historico de Pontos
        LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
        List<LogsSatMobEW> objHistorico = logsSatMobEWDao.obterLogsHistoricoMensal(0, 1000, this.logsSatMobEWSelecionado.getData().split(" ")[0].replace("-", ""), this.logsSatMobEWSelecionado.getCodfil(), funcion.getFuncion().getMatr().toPlainString().replace(".0", ""), "", "", "", "", null, this.logsSatMobEWSelecionado.getTipo(), "", persistencia);

        String HTML = "", UltimaData = "";

        if (objHistorico.size() > 0) {
            String ClasseHistorico = "", TituloHistorico = "", FotoHistorico = "";

            for (LogsSatMobEW objHistorico1 : objHistorico) {

                if (S2I(objHistorico1.getFotos()) % 2 == 0) {
                    TituloHistorico = getMessageS("Saida");
                    ClasseHistorico = "batida-saida";
                } else {
                    TituloHistorico = getMessageS("Entrada");
                    ClasseHistorico = "batida-entrada";
                }

                
                String[] vetorChaves = objHistorico1.getChave().split(";");
                FotoHistorico = preencherURL(objHistorico1.getURL(), 
                        persistencia.getEmpresa(), vetorChaves[0], 
                        vetorChaves[1], vetorChaves[2]);
//                FotoHistorico = SERVIDOR_MOBILE_FOTO + this.persistencia.getEmpresa() + "/ponto/";
//                FotoHistorico += FuncoesString.RecortaString(objHistorico1.getChave().split(";")[0], 0, 10).replaceAll("-", "") + "/";
//                FotoHistorico += FuncoesString.PreencheEsquerda(objHistorico1.getChave().split(";")[1].replace(".0", ""), 8, "0")
//                        + "_" + objHistorico1.getFotos().replace(".0", "") + ".jpg";

                if (!UltimaData.equals(objHistorico1.getData())) {
                    HTML += "<label style=\"background-color: steelblue; color: #FFF; font-weight: bold; padding: 4px 10px 4px 20px; margin: 8px 0px 0px 0px !important; width: 100%; border-radius:20px; width: 100%\"><i class=\"fa fa-calendar\" aria-hidden=\"true\"></i>&nbsp;&nbsp;" + Data(objHistorico1.getData()) + "</label>";
                }

                HTML += "    <div class=\"col-md-12 col-sm-12 col-xs-12\" style=\"height: 136px; padding-top: 10px; border: 2px solid #DDD; border-radius: 6px; box-shadow: 2px 2px 4px #EEE, -2px -2px 4px #EEE; margin-top: 6px;\">";
                HTML += "             <div title=\"" + getMessageS("CliqueVisualizarFoto") + "\" style=\"float: left; background-color: #EEE; margin-top: 8px !important; border: 2px solid #DDD; width: 100px; height: 100px; border-radius: 50%; background-image: url(" + FotoHistorico + "); background-size: cover; background-position: center center; cursor: pointer;\" onclick=\"AbrirFoto('" + FotoHistorico + "');\"></div>";
                HTML += "             <div style=\"height: 110px; float: left; width: calc(100% - 118px); margin-left: 18px\">";
                HTML += "                 <table class=\"DetalhesPonto\" style=\"width: 100%\">";
                HTML += "                     <tr>";
                HTML += "                         <td>" + objHistorico1.getFuncionario() + "</td>";
                HTML += "                     </tr>";
                HTML += "                     <tr>";
                HTML += "                         <td><label class=\"" + ClasseHistorico + "\" style=\"width: 130px !important;\">" + TituloHistorico + "</label></td>";
                HTML += "                     </tr>";
                HTML += "                     <tr>";
                HTML += "                         <td><i class=\"fa fa-clock-o\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + getMessageS("Hora") + ": </b>" + objHistorico1.getHora() + "</td>";
                HTML += "                     </tr>";
                HTML += "                     <tr>";
                HTML += "                         <td><i class=\"fa fa-id-card-o\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + getMessageS("Cargo") + ": </b>" + objHistorico1.getCargo() + "</td>";
                HTML += "                     </tr>";
                HTML += "                     <tr>";
                HTML += "                         <td><i class=\"fa fa-map-marker\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + getMessageS("Local") + ": </b>" + objHistorico1.getPosto() + "</td>";
                HTML += "                     </tr>";
                HTML += "                     <tr>";
                HTML += "                         <td><i class=\"fa fa-map-pin\" aria-hidden=\"true\" style=\"width: 30px; text-align: center\"></i><b>" + getMessageS("Distancia") + ": </b>" + objHistorico1.getDistancia_km() + "</td>";
                HTML += "                     </tr>";
                HTML += "                 </table>";
                HTML += "             </div>";
                HTML += "         </div>";

                UltimaData = objHistorico1.getData();
            }

            this.batidaPontoHtmlHistoricoMensal = HTML;
        } else {
            this.batidaPontoHtmlHistoricoMensal = "<label style=\"font-size: 16pt; color: #BBB; width: 100%; text-align: center; margin-top: 30px;\">" + getMessageS("SemRegistrosAnteriores") + "</label>";
        }

    }

    public void prepararFiltro() {
        try {
            this.logsSatMobEWSelecionado = new LogsSatMobEW();
            if (supervisor) {
                this.postos = this.satMobEWSatWeb.getPstSerList(this.codPessoa, this.codFil, this.persistencia);
            } else {
                this.postos = this.satMobEWSatWeb.getPstServClientList(codPessoa, log, persistencia);
            }
            PrimeFaces.current().ajax().update("formPesquisar");
            PrimeFaces.current().executeScript("PF('dlgPesquisar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void atualizarListaPostos() {
        try {
            this.postos = this.satMobEWSatWeb.getPstSerList(this.codPessoa, this.logsSatMobEWFiltro.getCodfil(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void atualizarLazyList() throws Exception {
        this.lazyGride = getAllLogs();
    }

    public void atualizarListaPostosEfetivos() {
        try {
            this.postosEfetivos = this.satMobEWSatWeb.getPstSerList(this.codPessoa, this.codFilEfetivos, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void pesquisarValor() throws Exception {
        limparPesquisa();
        preencherFiltro(valorPesquisa);
        this.lazyGride = getAllLogs();
        this.logsSatMobEWSelecionado = null;
    }

    public void mostrarTodasFiliais() throws Exception {
        funcionarios = new ArrayList<>();

        if (this.mostrarFiliais) {
            this.filters.replace("codfil", "");
        } else {
            this.filters.replace("codfil", this.codFil.equals("0") ? "" : this.codFil);
        }
//        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
//        dt.setFilters(this.filters);
        this.lazyGride = getAllLogs();
        carregarResumoQdes(true);
//        dt.setFirst(0);
        this.logsSatMobEWSelecionado = null;
    }

    public void limparTodosFiltros() throws Exception {
        this.logsSatMobEWs = null;
        funcionarios = new ArrayList<>();
        this.secaoSelecionadosFiltro = new ArrayList<>();
        this.filtroNomeFuncionario = "";
        this.mostrarFiliais = false;
        this.limparFiltros = false;
        this.dataTela = DataAtual.getDataAtual("SQL");

        this.logsSatMobEWFiltro.setTipo("");
        this.logsSatMobEWFiltro.setFuncionario("");
        this.logsSatMobEWFiltro.setPosto("");
        this.logsSatMobEWFiltro.setCodfil(this.codFil.equals("0") ? "" : this.codFil);

        this.filters.replace("data", this.dataTela);
        this.filters.replace("codfil", this.codFil.equals("0") ? "" : this.codFil);
        this.filters.replace("posto", "");
        this.filters.replace("matricula", "");
        this.filters.replace("secao", "");
        this.filters.replace("tipo", "");
        this.filters.replace("codpessoa", "");
        this.filters.replace("filtroWeb", (this.matricula == null || this.matricula.equals("0") || this.matricula.equals(""))
                && !this.supervisor ? "1" : "0");

//        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
//        dt.setFilters(this.filters);
        this.lazyGride = getAllLogs();
        carregarResumoQdes(true);
//        dt.setFirst(0);
        this.logsSatMobEWSelecionado = null;
    }

    public void pesquisar() throws Exception {
        /*this.logsSatMobEWs = null;
        this.filters.replace("codfil", this.logsSatMobEWFiltro.getCodfil() == null || this.logsSatMobEWFiltro.getCodfil().equals("") || this.logsSatMobEWFiltro.getCodfil().equals("0") ? "" : this.codFil);
        this.filters.replace("secao", this.logsSatMobEWFiltro.getSecao() == null || this.logsSatMobEWFiltro.getSecao().equals("") ? "" : this.logsSatMobEWFiltro.getSecao());
        this.filters.replace("matricula", this.logsSatMobEWFiltro.getFuncionario() == null || this.logsSatMobEWFiltro.getFuncionario().equals("") ? "" : this.logsSatMobEWFiltro.getFuncionario());
        this.filters.replace("tipo", this.logsSatMobEWFiltro.getTipo() == null || this.logsSatMobEWFiltro.getTipo().equals("") ? "" : this.logsSatMobEWFiltro.getTipo());*/

        this.logsSatMobEWs = null;
        carregarResumoQdes(true);
        this.lazyGride = getAllLogs();
        PrimeFaces.current().ajax().update("main:tabela");
        PrimeFaces.current().ajax().update("main:pnlListaPontos");
    }

    public void carregarContatos() throws Exception {
        this.cadastroRelatorio.setCodigo("");
        this.contatosCadastro = this.satMobEWSatWeb.getContatosListaCompleta(this.codFil, this.cadastroRelatorio.getSecao(), this.cadastroRelatorio.getLatitude(), this.cadastroRelatorio.getLongitude(), this.persistencia);
    }

    public void novoLancamento() {
        try {
            if (this.supervisor) {
                if (this.cadastroRelatorio.getLatitude().equals("")) {
                    this.cadastroRelatorio.setLatitude("0");
                }

                if (this.cadastroRelatorio.getLongitude().equals("")) {
                    this.cadastroRelatorio.setLongitude("0");
                }

                this.cadastroRelatorio.setSecao("");
                this.cadastroRelatorio.setCodigo("");
                this.cadastroRelatorio.setHistorico("");
                this.cadastroRelatorio.setDetalhes("");
                this.cadastroRelatorio.setFotos("");

                if (null == postosCadastro) {
                    this.postosCadastro = this.satMobEWSatWeb.getPstListaCompleta(this.codFil, this.cadastroRelatorio.getLatitude(), this.cadastroRelatorio.getLongitude(), this.persistencia);
                }

                this.contatosCadastro = this.satMobEWSatWeb.getContatosListaCompleta(this.codFil, this.cadastroRelatorio.getCodigo(), this.cadastroRelatorio.getLatitude(), this.cadastroRelatorio.getLongitude(), this.persistencia);

                StringBuilder script = new StringBuilder();
                script.append("$('div[id*=\"cadastrarNovoRel\"]').css('display','block');");
                script.append("$('div[id*=\"escolhaTipo\"]').css('display','none');");
                script.append("$('.FotoCarregada').attr('class','FotoPendente').find('i').attr('class','fa fa-camera');");

                script.append("PF('dlgNovoLancamento').show();");

                PrimeFaces.current().ajax().update("novoLancamento");
                PrimeFaces.current().executeScript(script.toString());
            }
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void HandleFileUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                this.uploadedFile = fileUploadEvent.getFile();
                String NomeArquivoUpload = DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                NomeArquivoUpload = NomeArquivoUpload.replace("-", "").replace(":", "").replace(" ", "").replace("/", "");

                new File("C:\\xampp\\htdocs\\satellite\\fotos\\" + this.persistencia.getEmpresa() + "\\relatorios\\" + this.codFil).mkdirs();
                String arquivo = "C:\\xampp\\htdocs\\satellite\\fotos\\" + this.persistencia.getEmpresa() + "\\relatorios\\" + this.codFil + "\\" + NomeArquivoUpload + this.uploadedFile.getFileName();

                byte[] conteudo = fileUploadEvent.getFile().getContents();
                FileOutputStream fos = new FileOutputStream(arquivo);
                fos.write(conteudo);
                fos.close();

                /*  double angleOfRotation = 90.0;
                String Extensao = FilenameUtils.getExtension(this.uploadedFile.getFileName());

                BufferedImage imagemOriginal = ImageIO.read(this.uploadedFile.getInputstream());

                if (imagemOriginal.getWidth() >= 2448 || imagemOriginal.getHeight() >= 3264) {
                    BufferedImage imagemProcessada = rotateMyImage(imagemOriginal, angleOfRotation);
                    ImageIO.write(imagemProcessada, Extensao, new File(arquivo));
                } else {
                    ImageIO.write(imagemOriginal, Extensao, new File(arquivo));
                }*/
                this.fotoCarregada = SERVIDOR_MOBILE_FOTO + this.persistencia.getEmpresa() + "/relatorios/" + this.codFil + "/" + NomeArquivoUpload + this.uploadedFile.getFileName();

                PrimeFaces.current().executeScript("$('.FotoPendente').find('.fa-spin').parents('.FotoPendente').css('background-image', 'url(\"" + this.fotoCarregada + "\")').find('table').css('display', 'none').find('.fa-spin').attr('class','fa fa-camera');");

                if (this.cadastroRelatorio.getFotos().equals("")) {
                    this.cadastroRelatorio.setFotos(this.fotoCarregada);
                } else {
                    this.cadastroRelatorio.setFotos(this.cadastroRelatorio.getFotos() + ";" + this.fotoCarregada);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public static BufferedImage rotateMyImage(BufferedImage img, double angle) {
        int w = img.getWidth();
        int h = img.getHeight();
        BufferedImage dimg = new BufferedImage(w, h, img.getType());
        Graphics2D g = dimg.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, // Anti-alias!
                RenderingHints.VALUE_ANTIALIAS_ON);

        g.rotate(Math.toRadians(angle), w / 2, h / 2);

        g.drawImage(img, null, 0, 0);
        return dimg;
    }

    public void novoRelatorioTipos(String Tipo) throws Exception {
        StringBuilder Script = new StringBuilder();

        Script.append("$('div[id*=\"escolhaTipo\"]').css('display','none');");
        Script.append("$('div[id*=\"cadastrarNovoRel\"]').css('display','block');");

        /*if (Tipo.equals("PST")) {
            Script.append("$('#divPostos, #divContatos').css('display','block');");
        } else {
            Script.append("$('#divContatos').css('display','block');");
        }*/
        PrimeFaces.current().executeScript(Script.toString());
    }

    public void salvarRelatorio() {
        try {
            if (!this.cadastroRelatorio.getHistorico().equals("")
                    && !this.cadastroRelatorio.getDetalhes().equals("")
                    && !this.cadastroRelatorio.getFotos().equals("")) {

                this.cadastroRelatorio.setData(DataAtual.getDataAtual("SQL"));
                this.cadastroRelatorio.setHora(DataAtual.getDataAtual("HORA"));
                this.cadastroRelatorio.setCodPessoa(this.codPessoa);
                this.cadastroRelatorio.setCodFil(this.codFil);
                this.cadastroRelatorio.setOperador(this.operador);
                this.cadastroRelatorio.setDt_Alter(DataAtual.getDataAtual("SQL"));
                this.cadastroRelatorio.setHr_Alter(DataAtual.getDataAtual("HORA"));

                this.satMobEWSatWeb.inserirRelatorio(this.cadastroRelatorio, this.persistencia);
                limparTodosFiltros();
                PrimeFaces.current().executeScript("PF('dlgNovoLancamento').hide();");
            } else {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Informe um histórico, detalhes e importe uma ou mais fotos."), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void editarRespostaInsp() {
        try {
            PstInspecao editInsp = new PstInspecao();

            editInsp.setCodigo(FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("inCodigo"));
            editInsp.setSequencia(FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("inSequencia"));
            editInsp.setResposta(FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("inResposta"));
            editInsp.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            editInsp.setDt_Alter(DataAtual.getDataAtual("SQL"));
            editInsp.setHr_Alter(DataAtual.getDataAtual("HORA"));

            LogsSatMobEWDao obj = new LogsSatMobEWDao();
            obj.alterarResposta(editInsp, this.persistencia);
            PrimeFaces.current().executeScript("$.MsgBoxAzulOk('" + getMessageS("Aviso") + "', '" + getMessageS("DadosSalvosSucesso") + "'); posSalvar('" + editInsp.getCodigo() + ".0','" + editInsp.getSequencia() + ".0','" + editInsp.getResposta() + "')");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void gerarRelatorio() throws Exception {
        String foto, idioma = FacesContext.getCurrentInstance().getViewRoot().getLocale().getLanguage();
        String[] fotos;
        Polyline polyline;
        List<LatLng> latLngs;
        LatLng latlng;
        switch (this.logsSatMobEWSelecionado.getTipo()) {
            /**
             * Tipos de relatório: 1 - Batida de Ponto 2 - Relatório 3 -
             * Relatório Supervisor 4 - Resumo Ronda 5 - Check Ins Supervisores
             * 6 - Rondas 7 - Rondas completas 8 - Check Ins Prestadores 9 -
             * Resumo Rota Prestador 10 - Relatórios Prestador
             */
            case "1":
                if (S2I(this.logsSatMobEWSelecionado.getFotos()) > 0) {
                    foto = SERVIDOR_MOBILE_FOTO + this.persistencia.getEmpresa() + "/ponto/";
                    foto += FuncoesString.RecortaString(this.logsSatMobEWSelecionado.getChave().split(";")[0], 0, 10).replaceAll("-", "") + "/";
                    foto += FuncoesString.PreencheEsquerda(this.logsSatMobEWSelecionado.getChave().split(";")[1].replace(".0", ""), 8, "0")
                            + "_" + this.logsSatMobEWSelecionado.getFotos().replace(".0", "") + ".jpg";
                    this.fotosRelatorio.add(foto);
                    this.fotoRelatorio = this.fotosRelatorio.get(0);
                }
                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioBatida(this.logsSatMobEWSelecionado, idioma, false, this.persistencia));
                if (!this.logsSatMobEWSelecionado.getLatitude().equals("") || !this.logsSatMobEWSelecionado.getLongitude().equals("")) {
                    this.mapa = new DefaultMapModel();
                    this.mapa.addOverlay(new Marker(new LatLng(new BigDecimal(this.logsSatMobEWSelecionado.getLatitude()).doubleValue(),
                            new BigDecimal(this.logsSatMobEWSelecionado.getLongitude()).doubleValue()),
                            this.logsSatMobEWSelecionado.getFuncionario(), this.logsSatMobEWSelecionado,
                            "https://mobile.sasw.com.br:9091/satmobile/img/pin_funcionario.png"));
                    this.centroMapa = this.logsSatMobEWSelecionado.getLatitude() + "," + this.logsSatMobEWSelecionado.getLongitude();
                    this.zoomMapa = "17";
                } else {
                    this.mapa = null;
                }
                this.titulo = Messages.getMessageS(S2I(this.logsSatMobEWSelecionado.getFotos()) % 2 == 0 ? Messages.getMessageS("SAÍDA") : Messages.getMessageS("ENTRADA"));
                break;
            case "2":
                fotos = this.logsSatMobEWSelecionado.getFotos().split(";");
                for (String f : fotos) {
                    if (!f.equals("")) {
                        this.fotosRelatorio.add(f);
                    }
                }
                this.fotoRelatorio = this.fotosRelatorio.isEmpty() ? "" : this.fotosRelatorio.get(0);
                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioReport(this.logsSatMobEWSelecionado, idioma, false, this.persistencia));
                if (!this.logsSatMobEWSelecionado.getLatitude().equals("") || !this.logsSatMobEWSelecionado.getLongitude().equals("")) {
                    this.mapa = new DefaultMapModel();
                    this.mapa.addOverlay(new Marker(new LatLng(new BigDecimal(this.logsSatMobEWSelecionado.getLatitude()).doubleValue(),
                            new BigDecimal(this.logsSatMobEWSelecionado.getLongitude()).doubleValue()),
                            this.logsSatMobEWSelecionado.getFuncionario(), this.logsSatMobEWSelecionado,
                            "https://mobile.sasw.com.br:9091/satmobile/img/pin_geral.png"));
                    this.centroMapa = this.logsSatMobEWSelecionado.getLatitude() + "," + this.logsSatMobEWSelecionado.getLongitude();
                    this.zoomMapa = "17";
                } else {
                    this.mapa = null;
                }
                this.titulo = Messages.getMessageS("Relatorio");

                this.filtroWeb = this.satMobEWSatWeb.isFiltroWeb(this.logsSatMobEWSelecionado, this.persistencia);
                break;
            case "3":
                fotos = this.logsSatMobEWSelecionado.getFotos().split(";");
                for (String f : fotos) {
                    if (!f.equals("")) {
                        this.fotosRelatorio.add(f);
                    }
                }
                this.fotoRelatorio = this.fotosRelatorio.isEmpty() ? "" : this.fotosRelatorio.get(0);
                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioReport(this.logsSatMobEWSelecionado, idioma, false, this.persistencia));
                this.mapa = new DefaultMapModel();
                this.mapa.addOverlay(new Marker(new LatLng(new BigDecimal(this.logsSatMobEWSelecionado.getLatitude()).doubleValue(),
                        new BigDecimal(this.logsSatMobEWSelecionado.getLongitude()).doubleValue()),
                        this.logsSatMobEWSelecionado.getFuncionario(), this.logsSatMobEWSelecionado,
                        "https://mobile.sasw.com.br:9091/satmobile/img/pin_geral.png"));
                this.centroMapa = this.logsSatMobEWSelecionado.getLatitude() + "," + this.logsSatMobEWSelecionado.getLongitude();
                this.zoomMapa = "17";
                this.titulo = Messages.getMessageS("RelatorioSupervisor");

                this.filtroWeb = this.satMobEWSatWeb.isFiltroWeb(this.logsSatMobEWSelecionado, this.persistencia);
                break;
            case "4":
                this.rondas = this.satMobEWSatWeb.obterRondas(this.logsSatMobEWSelecionado, this.persistencia);
                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioRondas(this.logsSatMobEWSelecionado, this.rondas,
                        idioma, false, this.persistencia));
                this.mapa = new DefaultMapModel();
                polyline = new Polyline();
                latLngs = new ArrayList<>();
                for (Rondas ronda : this.rondas) {
                    latlng = new LatLng(new BigDecimal(ronda.getLatitude()).doubleValue(),
                            new BigDecimal(ronda.getLongitude()).doubleValue());
                    latLngs.add(latlng);
                    polyline.getPaths().add(latlng);
                    this.mapa.addOverlay(new Marker(latlng, ronda.getDescricao() + ": " + Messages.getHoraS(ronda.getHr_alter(), "HH:mm"),
                            this.logsSatMobEWSelecionado, "https://mobile.sasw.com.br:9091/satmobile/img/pin_geral.png"));
                }
                latlng = Coordenadas.getCentroMapa(latLngs);
                this.centroMapa = latlng.getLat() + "," + latlng.getLng();
                polyline.setStrokeWeight(10);
                polyline.setStrokeColor("#FF9900");
                polyline.setStrokeOpacity(0.7);
                this.mapa.addOverlay(polyline);
                this.zoomMapa = "19";
                this.titulo = Messages.getMessageS("Ronda");

                this.graficoRonda = new HorizontalBarChartModel();

                String chaveRonda[] = this.logsSatMobEWSelecionado.getDetalhes().split(";");
                BigDecimal rondasHora = new BigDecimal(chaveRonda[2]);
                BigDecimal rondasTotal = new BigDecimal(chaveRonda[3]);

                ChartSeries completas = new ChartSeries();
                completas.setLabel(Messages.getMessageS("Completas"));
                completas.set("", rondasHora);

                ChartSeries total = new ChartSeries();
                total.setLabel(Messages.getMessageS("Total"));
                total.set("", rondasTotal);

                this.graficoRonda.setTitle(Messages.getMessageS("Conformidade"));
                this.graficoRonda.addSeries(completas);
                this.graficoRonda.addSeries(total);
                this.graficoRonda.setStacked(true);
                this.graficoRonda.setLegendPosition("e");
                this.graficoRonda.setShowDatatip(false);
                this.graficoRonda.setShadow(false);
                break;
            case "5":
                if (S2I(this.logsSatMobEWSelecionado.getFotos()) > 0) {
                    foto = SERVIDOR_MOBILE_FOTO + this.persistencia.getEmpresa() + "/ponto/";
                    foto += FuncoesString.RecortaString(this.logsSatMobEWSelecionado.getChave().split(";")[0], 0, 10).replaceAll("-", "") + "/";
                    foto += FuncoesString.PreencheEsquerda(this.logsSatMobEWSelecionado.getChave().split(";")[1].replace(".0", ""), 8, "0")
                            + "_" + this.logsSatMobEWSelecionado.getFotos().replace(".0", "") + ".jpg";
                    this.fotosRelatorio.add(foto);
                    this.fotoRelatorio = this.fotosRelatorio.get(0);
                }
                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioBatida(this.logsSatMobEWSelecionado, idioma, false, this.persistencia));
                this.mapa = new DefaultMapModel();
                this.mapa.addOverlay(new Marker(new LatLng(new BigDecimal(this.logsSatMobEWSelecionado.getLatitude()).doubleValue(),
                        new BigDecimal(this.logsSatMobEWSelecionado.getLongitude()).doubleValue()),
                        this.logsSatMobEWSelecionado.getFuncionario(), this.logsSatMobEWSelecionado,
                        "https://mobile.sasw.com.br:9091/satmobile/img/pin_funcionario.png"));
                this.centroMapa = this.logsSatMobEWSelecionado.getLatitude() + "," + this.logsSatMobEWSelecionado.getLongitude();
                this.zoomMapa = "17";
                this.titulo = Messages.getMessageS(S2I(this.logsSatMobEWSelecionado.getFotos()) % 2 == 0 ? "CheckOut" : "CheckIn");
                break;
            case "9":
                this.paradasPrestador = this.satMobEWSatWeb.obterRotaPrestador(this.logsSatMobEWSelecionado, this.persistencia);
                this.logsSatMobEWSelecionado.setCodfil(this.codFil);
                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioRotaPrestador(this.logsSatMobEWSelecionado, this.paradasPrestador,
                        idioma, false, this.persistencia));
                this.mapa = new DefaultMapModel();
                polyline = new Polyline();
                latLngs = new ArrayList<>();
                this.contatos = new ArrayList<>();
                PessoaTrajeto contato;
                int contador = 0;
                latlng = new LatLng(0, 0);
                for (LogsSatMobEW parada : this.paradasPrestador) {
                    try {
                        latlng = new LatLng(new BigDecimal(parada.getLatitude()).doubleValue(),
                                new BigDecimal(parada.getLongitude()).doubleValue());
                        latLngs.add(latlng);
                    } catch (Exception erro) {
                        System.out.println("contador: " + contador);
                    } finally {
                        contador++;
                    }
                    if (!parada.getTipo().equals("0")) {
                        contato = new PessoaTrajeto();
                        contato.setHrCheg(parada.getDetalhes().split(";")[0]);
                        if (parada.getDetalhes().split(";").length > 1) {
                            contato.setHrSaida(parada.getDetalhes().split(";")[1]);
                        }
                        switch (parada.getTipo()) {
                            case "1":
                                if (parada.getPosto().equals("")) {
                                    contato.setDescricao(Messages.getMessageS("RotaRetomada"));
                                } else {
                                    contato.setDescricao(Messages.getMessageS("InicioRota") + ": " + parada.getPosto());
                                }
                                break;
                            case "2":
                                contato.setDescricao(parada.getPosto());
                                break;
                            case "3":
                                contato.setDescricao(Messages.getMessageS("RotaPausada"));
                                break;
                        }
                        this.contatos.add(contato);
                        this.mapa.addOverlay(new Marker(latlng, contato.getDescricao() + ": " + Messages.getHoraS(contato.getHrCheg(), "HH:mm"),
                                this.logsSatMobEWSelecionado, "https://mobile.sasw.com.br:9091/satmobile/img/pin_geral.png"));
                    } else {
                        polyline.getPaths().add(latlng);
                    }
                }

//                latlng = Coordenadas.getCentroMapa(latLngs);
                this.centroMapa = latlng.getLat() + "," + latlng.getLng();
                polyline.setStrokeWeight(10);
                polyline.setStrokeColor("#FF9900");
                polyline.setStrokeOpacity(0.7);
                this.mapa.addOverlay(polyline);
                this.zoomMapa = "19";
                this.titulo = Messages.getMessageS("Rota");

                break;
            case "10":
                fotos = this.logsSatMobEWSelecionado.getFotos().split(";");
                for (String f : fotos) {
                    if (!f.equals("")) {
                        this.fotosRelatorio.add(f);
                    }
                }
                this.fotoRelatorio = this.fotosRelatorio.isEmpty() ? "" : this.fotosRelatorio.get(0);
                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioPrestador(this.logsSatMobEWSelecionado, idioma, false, this.persistencia));
                if (!this.logsSatMobEWSelecionado.getLatitude().equals("") || !this.logsSatMobEWSelecionado.getLongitude().equals("")) {
                    this.mapa = new DefaultMapModel();
                    this.mapa.addOverlay(new Marker(new LatLng(new BigDecimal(this.logsSatMobEWSelecionado.getLatitude()).doubleValue(),
                            new BigDecimal(this.logsSatMobEWSelecionado.getLongitude()).doubleValue()),
                            this.logsSatMobEWSelecionado.getFuncionario(), this.logsSatMobEWSelecionado,
                            "https://mobile.sasw.com.br:9091/satmobile/img/pin_geral.png"));
                    this.centroMapa = this.logsSatMobEWSelecionado.getLatitude() + "," + this.logsSatMobEWSelecionado.getLongitude();
                    this.zoomMapa = "17";
                } else {
                    this.mapa = null;
                }
                this.titulo = Messages.getMessageS("Relatorio");

                this.filtroWeb = this.satMobEWSatWeb.isFiltroWeb(this.logsSatMobEWSelecionado, this.persistencia);
                break;
            case "7":

                this.mapa = null;
                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioInspecao(this.logsSatMobEWSelecionado, idioma, this.persistencia));
                this.titulo = this.logsSatMobEWSelecionado.getTitulo();

                if (!this.logsSatMobEWSelecionado.getLatitude().equals("") || !this.logsSatMobEWSelecionado.getLongitude().equals("")) {
                    this.mapa = new DefaultMapModel();
                    this.mapa.addOverlay(new Marker(new LatLng(new BigDecimal(this.logsSatMobEWSelecionado.getLatitude()).doubleValue(),
                            new BigDecimal(this.logsSatMobEWSelecionado.getLongitude()).doubleValue()),
                            this.logsSatMobEWSelecionado.getFuncionario(), this.logsSatMobEWSelecionado,
                            "https://mobile.sasw.com.br:9091/satmobile/img/pin_geral.png"));
                    this.centroMapa = this.logsSatMobEWSelecionado.getLatitude() + "," + this.logsSatMobEWSelecionado.getLongitude();
                    this.zoomMapa = "17";
                } else {
                    this.mapa = null;
                }

                List<PstInspecao> pp = this.satMobEWSatWeb.getDetalhesPstInspecoes(this.logsSatMobEWSelecionado, this.persistencia);

                InspecoesMB.PstInspecaoDetalhes aux;
                for (PstInspecao p : pp) {
                    aux = new InspecoesMB.PstInspecaoDetalhes();
                    aux.setPstInspecao(p);
                    aux.getPstInspecao().setResposta(p.getResposta().replace("\\N", "\n"));
                    aux.setFotos(Arrays.asList(p.getCaminhoImagem().split(";")));
                    if (!aux.getFotos().isEmpty()) {
                        if (aux.getFotos().size() == 1 && aux.getFotos().get(0).equals("")) {
                            aux.setFotos(new ArrayList<>());
                        } else {
                            aux.setFoto(aux.getFotos().get(0));
                        }
                    }

                    aux.setVideos(Arrays.asList(p.getCaminhoVideo().split(";")));
                    if (!aux.getVideos().isEmpty()) {
                        if (aux.getVideos().size() == 1 && aux.getVideos().get(0).equals("")) {
                            aux.setVideos(new ArrayList<>());
                        } else {
                            aux.setVideo(aux.getVideos().get(0));
                        }
                    }

                    aux.setAudios(Arrays.asList(p.getCaminhoAudio().split(";")));
                    if (!aux.getAudios().isEmpty()) {
                        if (aux.getAudios().size() == 1 && aux.getAudios().get(0).equals("")) {
                            aux.setAudios(new ArrayList<>());
                        } else {
                            aux.setAudio(aux.getAudios().get(0));
                        }
                    }

                    aux.setAudios(Arrays.asList(p.getCaminhoAudio().split(";")));
                    if (!aux.getAudios().isEmpty()) {
                        if (aux.getAudios().size() == 1 && aux.getAudios().get(0).equals("")) {
                            aux.setAudios(new ArrayList<>());
                        } else {
                            aux.setAudio(aux.getAudios().get(0));
                        }
                    }

                    aux.setPosicaoFoto(0);
                    aux.setPosicaoVideo(0);

                    this.pstInspecaoSelecionadaDetalhes.add(aux);
                }
                break;
        }

    }

    public void gerarRelatorioDownload() throws Exception {
        String idioma = FacesContext.getCurrentInstance().getViewRoot().getLocale().getLanguage();
        InputStream stream = null;
        String nomeArquivo = "file.pdf";
        switch (this.logsSatMobEWSelecionado.getTipo()) {
            case "1":
                nomeArquivo = this.titulo + "_" + this.logsSatMobEWSelecionado.getFotos()
                        + "_" + this.logsSatMobEWSelecionado.getChave().split(";")[1].replace(".0", "")
                        + "_" + this.logsSatMobEWSelecionado.getChave().split(";")[0].replace("-", "") + ".pdf";
                stream = new ByteArrayInputStream(Messages.replaceTags(
                        this.satMobEWSatWeb.getRelatorioBatida(this.logsSatMobEWSelecionado, idioma, true, this.persistencia)).getBytes());
                break;
            case "2":
                nomeArquivo = "report_" + this.logsSatMobEWSelecionado.getChave().split(";")[0].replace(".0", "") + ".pdf";
                stream = new ByteArrayInputStream(Messages.replaceTags(
                        this.satMobEWSatWeb.getRelatorioReport(this.logsSatMobEWSelecionado, idioma, true, this.persistencia)).getBytes());
                break;
            case "3":
                nomeArquivo = this.titulo + "_" + this.logsSatMobEWSelecionado.getChave().split(";")[0]
                        + this.logsSatMobEWSelecionado.getChave().split(";")[1] + "_" + "pdf";
                stream = new ByteArrayInputStream(Messages.replaceTags(
                        this.satMobEWSatWeb.getRelatorioRondas(this.logsSatMobEWSelecionado, this.rondas, idioma, true, this.persistencia)).getBytes());
                break;
        }

        ByteArrayOutputStream osPdf = new ByteArrayOutputStream();
        ITextRenderer renderer = new ITextRenderer();
        Tidy tidy = new Tidy();
        tidy.setShowWarnings(false);
        Document doc = tidy.parseDOM(stream, null);
        renderer.setDocument(doc, null);
        renderer.layout();
        renderer.createPDF(osPdf);

        InputStream inputPDF = new ByteArrayInputStream(osPdf.toByteArray());

        arquivoRelatorio = new DefaultStreamedContent(inputPDF, "pdf", nomeArquivo);
        osPdf.close();
        stream.close();
        inputPDF.close();

    }

    public void avancarFotoRelatorio() {
        if (this.posFotoRelatorio + 1 == this.fotosRelatorio.size()) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosFim"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.fotoRelatorio = this.fotosRelatorio.get(this.posFotoRelatorio + 1);
            this.posFotoRelatorio = this.posFotoRelatorio + 1;
            this.video = this.fotoRelatorio != null && this.fotoRelatorio.contains("mp4");
        }
    }

    public void voltarFotoRelatorio() {
        if (this.posFotoRelatorio == 0) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosInicio"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.fotoRelatorio = this.fotosRelatorio.get(this.posFotoRelatorio - 1);
            this.posFotoRelatorio = this.posFotoRelatorio - 1;
            this.video = this.fotoRelatorio != null && this.fotoRelatorio.contains("mp4");
        }
    }

    public void trocarSenha() {
        try {
            Boolean senhaValida = this.satMobEWSatWeb.verificaSenha(this.codPessoa, this.senhaAtual, this.persistencia);
            if (senhaValida) {
                this.satMobEWSatWeb.trocarSenhaCliente(this.codPessoa, this.novaSenha,
                        FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia, this.satellite);
            } else {
                throw new Exception("SenhaIncorreta");
            }
            PrimeFaces.current().executeScript("PF('dlgTrocarSenha').hide()");
            PrimeFaces.current().executeScript("PF('dlgOk').hide()");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SenhaAlteradaSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void atualizarFiltroWeb() {
        try {
            this.satMobEWSatWeb.atualizaFiltroWeb(this.logsSatMobEWSelecionado, this.filtroWeb, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(this.filtroWeb ? "RelatorioTornadoPublico" : "RelatorioTornadoPrivado"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public String getFotoRelatorio() {
        return fotoRelatorio;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public LogsSatMobEW getLogsSatMobEWSelecionado() {
        return logsSatMobEWSelecionado;
    }

    public void setLogsSatMobEWSelecionado(LogsSatMobEW logsSatMobEWSelecionado) {
        this.logsSatMobEWSelecionado = logsSatMobEWSelecionado;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public String getFotoGride(String inTipo, String inChave, String inFotos) {
        String foto;
        String[] fotos;
        this.fotosRelatorio = new ArrayList<>();

        switch (inTipo) {
            /**
             * Tipos de relatório: 1 - Batida de Ponto 2 - Relatório 3 -
             * Relatório Supervisor 4 - Resumo Ronda 5 - Check Ins Supervisores
             * 6 - Rondas 7 - Rondas completas 8 - Check Ins Prestadores 9 -
             * Resumo Rota Prestador 10 - Relatórios Prestador
             */
            case "1":
                if (S2I(inFotos) > 0) {
                    try {
                        LogsSatMobEW logsSatMobEW = new LogsSatMobEW();
                        logsSatMobEW.setChave(inChave);
                        logsSatMobEW.setTipo(inTipo);
                        int i = logsSatMobEWs.getWrappedData().indexOf(
                                logsSatMobEW);
                        if (i > -1) {
                            logsSatMobEW = logsSatMobEWs.getWrappedData().get(i);
                            String[] vetorChave = inChave.split(";");
                            foto = preencherURL(logsSatMobEW.getURL(), 
                                    this.persistencia.getEmpresa(), 
                                    vetorChave[0], vetorChave[1], inFotos);
                            fotosRelatorio.add(foto);
                            fotoGride = fotosRelatorio.get(0);                            
                        }
                    } catch (Exception ex) {
                        Logger.getLogger(SatMobEWMB.class.getName()).log(Level.SEVERE, null, ex);
                    }                    
                    foto = SERVIDOR_MOBILE_FOTO + 
                            this.persistencia.getEmpresa() + "/ponto/";
                    foto += FuncoesString.RecortaString(inChave.split(";")[0], 0, 10).replaceAll("-", "") + "/";
                    foto += FuncoesString.PreencheEsquerda(inChave.split(";")[1].replace(".0", ""), 8, "0")
                            + "_" + inFotos.replace(".0", "") + ".jpg";
                }
                break;
            case "2":
                fotos = inFotos.split(";");
                for (String f : fotos) {
                    if (!f.equals("")) {
                        this.fotosRelatorio.add(f);
                    }
                }
                fotoGride = this.fotosRelatorio.isEmpty() ? "" : this.fotosRelatorio.get(0);
                break;
            case "3":
            case "7":
                fotos = inFotos.split(";");
                for (String f : fotos) {
                    if (!f.equals("")) {
                        this.fotosRelatorio.add(f);
                    }
                }
                fotoGride = this.fotosRelatorio.isEmpty() ? "" : this.fotosRelatorio.get(0);
                break;
            case "5":
                if (S2I(inFotos) > 0) {
                    foto = SERVIDOR_MOBILE_FOTO + this.persistencia.getEmpresa() + "/ponto/";
                    foto += FuncoesString.RecortaString(inChave.split(";")[0], 0, 10).replaceAll("-", "") + "/";
                    foto += FuncoesString.PreencheEsquerda(inChave.split(";")[1].replace(".0", ""), 8, "0")
                            + "_" + inFotos.replace(".0", "") + ".jpg";
                    this.fotosRelatorio.add(foto);
                    fotoGride = this.fotosRelatorio.get(0);
                }
                break;

            case "4":
            case "9":
                fotoGride = "https://mobile.sasw.com.br:9091/satmobile/img/icone_semfoto.jpg";

                break;
            case "10":
                fotos = inFotos.split(";");
                for (String f : fotos) {
                    if (!f.equals("")) {
                        this.fotosRelatorio.add(f);
                    }
                }
                fotoGride = this.fotosRelatorio.isEmpty() ? "" : this.fotosRelatorio.get(0);
                break;
        }

        if (null == fotoGride
                || fotoGride.equals("")) {
            fotoGride = "https://mobile.sasw.com.br:9091/satmobile/img/icone_semfoto.jpg";
        }

        return fotoGride;
    }

    public void setFotoGride(String fotoGride) {
        this.fotoGride = fotoGride;
    }

    public String getTipoItem(String tipo, String chave, String tipoRelatorio) {
        /*
         * 1 - Batida de Ponto
         * 2 - Relatório
         * 3 - Relatório Supervisor
         * 4 - Resumo Ronda
         * 5 - Check Ins Supervisores
         * 6 - Rondas
         *11 - Batida de ponto Entrada 
         *12 - Batida de ponto Saida
         */
        switch (tipo) {
            case "1":
                tipoItem = (chave.split(";")[2].equals("2")
                        || chave.split(";")[2].equals("4"))
                        ? Messages.getMessageS("SAÍDA")
                        : Messages.getMessageS("ENTRADA");
                break;
            case "3":
                tipoItem = Messages.getMessageS("RELATÓRIO DE SUPERVISOR");
                break;
            case "2":
            case "10":
                if (null != tipoRelatorio
                        && !tipoRelatorio.equals("")) {
                    tipoItem = tipoRelatorio;
                } else {
                    tipoItem = Messages.getMessageS("RELATÓRIO");
                }

                break;
            case "4":
            case "6":
                tipoItem = Messages.getMessageS("RONDA");
                break;
            case "9":
                tipoItem = Messages.getMessageS("ROTA");
                break;
            case "5":
                tipoItem = S2I(chave.split(";")[2]) % 2 == 1 ? Messages.getMessageS("CHEGADA DE SUPERVISOR") : Messages.getMessageS("SAIDA DE SUPERVISOR");
                break;
            case "7":
                tipoItem = Messages.getMessageS("INSPEÇÃO");
                break;
            case "11":
                tipoItem = Messages.getMessageS("ENTRADA");
                break;
            case "12":
                tipoItem = Messages.getMessageS("SAÍDA");
                break;
        }
        return tipoItem;
    }

    public void setTipoItem(String tipoItem) {
        this.tipoItem = tipoItem;
    }

    public String getImagemTipo(String tipo, String chave) {
        /*
         * 1 - Batida de Ponto
         * 2 - Relatório
         * 3 - Relatório Supervisor
         * 4 - Resumo Ronda
         * 5 - Check Ins Supervisores
         * 6 - Rondas
         *11 - Batida de ponto Entrada 
         *12 - Batida de ponto Saida
         */
        switch (tipo) {
            case "1":
                imagemTipo = (chave.split(";")[2].equals("2")
                        || chave.split(";")[2].equals("4"))
                        ? "../assets/img/icone_batidadeponto_saida.png"
                        : "../assets/img/icone_batidadeponto_entrada.png";
                break;
            case "2":
            case "3":
            case "10":
                imagemTipo = "../assets/img/icone_relatorio.png";
                break;
            case "4":
            case "9":
                imagemTipo = "../assets/img/icone_ronda.png";
                break;
            case "5":
                imagemTipo = "../assets/img/icone_batidadeponto.png";
                break;
            case "7":
                imagemTipo = "../assets/img/icones_satmob_insp_postos_G.png";
                break;
            case "11":
                imagemTipo = "../assets/img/icone_batidadeponto_entrada.png";
                break;
            case "12":
                imagemTipo = "../assets/img/icone_batidadeponto_saida.png";
                break;

        }
        return imagemTipo;
    }

    public void abrirMensagens(boolean Reload) {
        try {
            if (!Reload || null == this.pessoaSelecionada) {
                this.pessoaSelecionada = new Pessoa();
            }

            LogsSatMobEWDao obj = new LogsSatMobEWDao();

            // Carregar Pessoas com comunicação na data
            this.pessoas = new ArrayList<>();
            this.pessoas = obj.listaUltimaComunicacaoPessoas(this.dataTela, this.persistencia);

            // Abrir Modal
            if (!Reload) {
                PrimeFaces.current().ajax().update("formMensagens");
                PrimeFaces.current().executeScript("PF('dlgMensagens').show()");
            }

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void envioMensagem() {
        try {
            this.satMobEWSatWeb.envioMensagem(this.pessoaSelecionada.getCodigo(), this.codPessoa, this.mensagem,
                    this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgMensagens').hide()");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Sucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void setImagemTipo(String imagemTipo) {
        this.imagemTipo = imagemTipo;
    }

    public Date getUltimoDia() {
        return ultimoDia;
    }

    public List<String> getFotosRelatorio() {
        return fotosRelatorio;
    }

    public void setFotosRelatorio(List<String> fotosRelatorio) {
        this.fotosRelatorio = fotosRelatorio;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public MapModel getMapa() {
        return mapa;
    }

    public void setMapa(MapModel mapa) {
        this.mapa = mapa;
    }

    public String getCentroMapa() {
        return centroMapa;
    }

    public void setCentroMapa(String centroMapa) {
        this.centroMapa = centroMapa;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getZoomMapa() {
        return zoomMapa;
    }

    public void setZoomMapa(String zoomMapa) {
        this.zoomMapa = zoomMapa;
    }

    public String getSenhaAtual() {
        return senhaAtual;
    }

    public void setSenhaAtual(String senhaAtual) {
        this.senhaAtual = senhaAtual;
    }

    public String getNovaSenha() {
        return novaSenha;
    }

    public void setNovaSenha(String novaSenha) {
        this.novaSenha = novaSenha;
    }

    public List<Rondas> getRondas() {
        return rondas;
    }

    public void setRondas(List<Rondas> rondas) {
        this.rondas = rondas;
    }

    public HorizontalBarChartModel getGraficoRonda() {
        return graficoRonda;
    }

    public void setGraficoRonda(HorizontalBarChartModel graficoRonda) {
        this.graficoRonda = graficoRonda;
    }

    public StreamedContent getArquivoRelatorio() {
        return arquivoRelatorio;
    }

    public void setArquivoRelatorio(StreamedContent arquivoRelatorio) {
        this.arquivoRelatorio = arquivoRelatorio;
    }

    public boolean isSupervisor() {
        return !this.nivel.equals("8");
    }

    public void setSupervisor(boolean supervisor) {
        this.supervisor = supervisor;
    }

    public boolean isFiltroWeb() {
        return filtroWeb;
    }

    public void setFiltroWeb(boolean filtroWeb) {
        this.filtroWeb = filtroWeb;
    }

    public boolean isVideo() {
        return video;
    }

    public void setVideo(boolean video) {
        this.video = video;
    }

    public List<PstServ> getPostos() {
        return postos;
    }

    public void setPostos(List<PstServ> postos) {
        this.postos = postos;
    }

    public List<Funcionarios> getFuncionarios() {
        return funcionarios;
    }

    public void setFuncionarios(List<Funcionarios> funcionarios) {
        this.funcionarios = funcionarios;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public boolean isLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public List<PessoaTrajeto> getContatos() {
        return contatos;
    }

    public void setContatos(List<PessoaTrajeto> contatos) {
        this.contatos = contatos;
    }

    public List<InspecoesMB.PstInspecaoDetalhes> getPstInspecaoSelecionadaDetalhes() {
        return pstInspecaoSelecionadaDetalhes;
    }

    public void setPstInspecaoSelecionadaDetalhes(List<InspecoesMB.PstInspecaoDetalhes> pstInspecaoSelecionadaDetalhes) {
        this.pstInspecaoSelecionadaDetalhes = pstInspecaoSelecionadaDetalhes;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getResumoQdeEntradas() {
        return resumoQdeEntradas;
    }

    public void setResumoQdeEntradas(String resumoQdeEntradas) {
        this.resumoQdeEntradas = resumoQdeEntradas;
    }

    public String getResumoQdeSaidas() {
        return resumoQdeSaidas;
    }

    public void setResumoQdeSaidas(String resumoQdeSaidas) {
        this.resumoQdeSaidas = resumoQdeSaidas;
    }

    public String getResumoQdeInspecoes() {
        return resumoQdeInspecoes;
    }

    public void setResumoQdeInspecoes(String resumoQdeInspecoes) {
        this.resumoQdeInspecoes = resumoQdeInspecoes;
    }

    public String getResumoQdeRondas() {
        return resumoQdeRondas;
    }

    public void setResumoQdeRondas(String resumoQdeRondas) {
        this.resumoQdeRondas = resumoQdeRondas;
    }

    public String getResumoQdeRelatorios() {
        return resumoQdeRelatorios;
    }

    public void setResumoQdeRelatorios(String resumoQdeRelatorios) {
        this.resumoQdeRelatorios = resumoQdeRelatorios;
    }

    public String getResumoQdeOutros() {
        return resumoQdeOutros;
    }

    public void setResumoQdeOutros(String resumoQdeOutros) {
        this.resumoQdeOutros = resumoQdeOutros;
    }

    public LogsSatMobEW getLogsSatMobEWFiltro() {
        return logsSatMobEWFiltro;
    }

    public void setLogsSatMobEWFiltro(LogsSatMobEW logsSatMobEWFiltro) {
        this.logsSatMobEWFiltro = logsSatMobEWFiltro;
    }

    public List<PstServ> getPostosCadastro() {
        return postosCadastro;
    }

    public void setPostosCadastro(List<PstServ> postosCadastro) {
        this.postosCadastro = postosCadastro;
    }

    public List<PessoaCliAut> getContatosCadastro() {
        return contatosCadastro;
    }

    public void setContatosCadastro(List<PessoaCliAut> contatosCadastro) {
        this.contatosCadastro = contatosCadastro;
    }

    public RelatorioDoctos getCadastroRelatorio() {
        return cadastroRelatorio;
    }

    public void setCadastroRelatorio(RelatorioDoctos cadastroRelatorio) {
        this.cadastroRelatorio = cadastroRelatorio;
    }

    public String getFotoCarregada() {
        return fotoCarregada;
    }

    public void setFotoCarregada(String fotoCarregada) {
        this.fotoCarregada = fotoCarregada;
    }

    public String getFotoNumero() {
        return fotoNumero;
    }

    public void setFotoNumero(String fotoNumero) {
        this.fotoNumero = fotoNumero;
    }

    public String getDadosRelatorioLine() {
        return dadosRelatorioLine;
    }

    public void setDadosRelatorioLine(String dadosRelatorioLine) {
        this.dadosRelatorioLine = dadosRelatorioLine;
    }

    public String getDadosRelatorioTimeLine() {
        return dadosRelatorioTimeLine;
    }

    public void setDadosRelatorioTimeLine(String dadosRelatorioTimeLine) {
        this.dadosRelatorioTimeLine = dadosRelatorioTimeLine;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getListaTrabalhos() {
        return listaTrabalhos;
    }

    public void setListaTrabalhos(String listaTrabalhos) {
        this.listaTrabalhos = listaTrabalhos;
    }

    public String getMarkers() {
        return markers;
    }

    public void setMarkers(String markers) {
        this.markers = markers;
    }

    public String getCentro() {
        return centro;
    }

    public void setCentro(String centro) {
        this.centro = centro;
    }

    public List<Pessoa> getPessoas() {
        return pessoas;
    }

    public void setPessoas(List<Pessoa> pessoas) {
        this.pessoas = pessoas;
    }

    public Pessoa getPessoaSelecionada() {
        return pessoaSelecionada;
    }

    public void setPessoaSelecionada(Pessoa pessoaSelecionada) {
        this.pessoaSelecionada = pessoaSelecionada;
    }

    public String getBatidaPontoHtmlTopo() {
        return batidaPontoHtmlTopo;
    }

    public void setBatidaPontoHtmlTopo(String batidaPontoHtmlTopo) {
        this.batidaPontoHtmlTopo = batidaPontoHtmlTopo;
    }

    public String getBatidaPontoFoto() {
        return batidaPontoFoto;
    }

    public void setBatidaPontoFoto(String batidaPontoFoto) {
        this.batidaPontoFoto = batidaPontoFoto;
    }

    public String getBatidaPontoTipo() {
        return batidaPontoTipo;
    }

    public void setBatidaPontoTipo(String batidaPontoTipo) {
        this.batidaPontoTipo = batidaPontoTipo;
    }

    public String getBatidaPontoHtmlHistorico() {
        return batidaPontoHtmlHistorico;
    }

    public void setBatidaPontoHtmlHistorico(String batidaPontoHtmlHistorico) {
        this.batidaPontoHtmlHistorico = batidaPontoHtmlHistorico;
    }

    public String getBatidaPontoHtmlHistoricoMensal() {
        return batidaPontoHtmlHistoricoMensal;
    }

    public void setBatidaPontoHtmlHistoricoMensal(String batidaPontoHtmlHistoricoMensal) {
        this.batidaPontoHtmlHistoricoMensal = batidaPontoHtmlHistoricoMensal;
    }

    public String getHtmlAtrasados() {
        return htmlAtrasados;
    }

    public void setHtmlAtrasados(String htmlAtrasados) {
        this.htmlAtrasados = htmlAtrasados;
    }

    public String getHtmlFaltas() {
        return htmlFaltas;
    }

    public void setHtmlFaltas(String htmlFaltas) {
        this.htmlFaltas = htmlFaltas;
    }

    public String getHtmlFerias() {
        return htmlFerias;
    }

    public void setHtmlFerias(String htmlFerias) {
        this.htmlFerias = htmlFerias;
    }

    public String getQtdeAtrasados() {
        return qtdeAtrasados;
    }

    public void setQtdeAtrasados(String qtdeAtrasados) {
        this.qtdeAtrasados = qtdeAtrasados;
    }

    public String getQtdeFaltas() {
        return qtdeFaltas;
    }

    public void setQtdeFaltas(String qtdeFaltas) {
        this.qtdeFaltas = qtdeFaltas;
    }

    public String getQtdeFerias() {
        return qtdeFerias;
    }

    public void setQtdeFerias(String qtdeFerias) {
        this.qtdeFerias = qtdeFerias;
    }

    public String getSecao() {
        return secao;
    }

    public void setSecao(String secao) {
        this.secao = secao;
    }

    public String getOperDest() {
        return operDest;
    }

    public void setOperDest(String operDest) {
        this.operDest = operDest;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getWebPontoLatitude() {
        return webPontoLatitude;
    }

    public void setWebPontoLatitude(String webPontoLatitude) {
        this.webPontoLatitude = webPontoLatitude;
    }

    public String getWebPontoLongitude() {
        return webPontoLongitude;
    }

    public void setWebPontoLongitude(String webPontoLongitude) {
        this.webPontoLongitude = webPontoLongitude;
    }

    public String getWebPontoLarguraImagem() {
        return webPontoLarguraImagem;
    }

    public void setWebPontoLarguraImagem(String webPontoLarguraImagem) {
        this.webPontoLarguraImagem = webPontoLarguraImagem;
    }

    public String getWebPontoAlturaImagem() {
        return webPontoAlturaImagem;
    }

    public void setWebPontoAlturaImagem(String webPontoAlturaImagem) {
        this.webPontoAlturaImagem = webPontoAlturaImagem;
    }

    public String getWebPontoBase64() {
        return webPontoBase64;
    }

    public void setWebPontoBase64(String webPontoBase64) {
        this.webPontoBase64 = webPontoBase64;
    }

    public Funcion getFuncionarioSelecionado() {
        return funcionarioSelecionado;
    }

    public void setFuncionarioSelecionado(Funcion funcionarioSelecionado) {
        this.funcionarioSelecionado = funcionarioSelecionado;
    }

    public List<Funcion> getFuncionariosDash() {
        return funcionariosDash;
    }

    public void setFuncionariosDash(List<Funcion> funcionariosDash) {
        this.funcionariosDash = funcionariosDash;
    }

    public String getFeriasQtde() {
        return feriasQtde;
    }

    public void setFeriasQtde(String feriasQtde) {
        this.feriasQtde = feriasQtde;
    }

    public String getAtrasadosQde() {
        return atrasadosQde;
    }

    public void setAtrasadosQde(String atrasadosQde) {
        this.atrasadosQde = atrasadosQde;
    }

    public String getFaltasQtde() {
        return faltasQtde;
    }

    public void setFaltasQtde(String faltasQtde) {
        this.faltasQtde = faltasQtde;
    }

    public List<Funcionarios> getFunctionariosSelecionadosFiltro() {
        return functionariosSelecionadosFiltro;
    }

    public void setFunctionariosSelecionadosFiltro(List<Funcionarios> functionariosSelecionadosFiltro) {
        this.functionariosSelecionadosFiltro = functionariosSelecionadosFiltro;
    }

    public List<Date> getDatasSelecionadas() {
        return datasSelecionadas;
    }

    public void setDatasSelecionadas(List<Date> datasSelecionadas) {
        this.datasSelecionadas = datasSelecionadas;
    }

    public String getCodFilEfetivos() {
        return codFilEfetivos;
    }

    public void setCodFilEfetivos(String codFilEfetivos) {
        this.codFilEfetivos = codFilEfetivos;
    }

    public String getSecaoEfetivos() {
        return secaoEfetivos;
    }

    public void setSecaoEfetivos(String secaoEfetivos) {
        this.secaoEfetivos = secaoEfetivos;
    }

    public List<Date> getDatasSelecionadasEfetivos() {
        return datasSelecionadasEfetivos;
    }

    public void setDatasSelecionadasEfetivos(List<Date> datasSelecionadasEfetivos) {
        this.datasSelecionadasEfetivos = datasSelecionadasEfetivos;
    }

    public List<PstServ> getPostosEfetivos() {
        return postosEfetivos;
    }

    public void setPostosEfetivos(List<PstServ> postosEfetivos) {
        this.postosEfetivos = postosEfetivos;
    }

    public String getHtmlEfetivos() {
        return htmlEfetivos;
    }

    public void setHtmlEfetivos(String htmlEfetivos) {
        this.htmlEfetivos = htmlEfetivos;
    }

    public String getHtmlEfetivosPontos() {
        return htmlEfetivosPontos;
    }

    public void setHtmlEfetivosPontos(String htmlEfetivosPontos) {
        this.htmlEfetivosPontos = htmlEfetivosPontos;
    }

    public List<PstServ> getSecaoSelecionadosFiltro() {
        return secaoSelecionadosFiltro;
    }

    public void setSecaoSelecionadosFiltro(List<PstServ> secaoSelecionadosFiltro) {
        this.secaoSelecionadosFiltro = secaoSelecionadosFiltro;
    }

    public String getFiltroNomeFuncionario() {
        return filtroNomeFuncionario;
    }

    public void setFiltroNomeFuncionario(String filtroNomeFuncionario) {
        this.filtroNomeFuncionario = filtroNomeFuncionario;
    }

    public LazyDataModel<LogsSatMobEW> getLazyGride() {
        return lazyGride;
    }

    public void setLazyGride(LazyDataModel<LogsSatMobEW> lazyGride) {
        this.lazyGride = lazyGride;
    }

    public String getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    public String getMapaLocalPonto() {
        return mapaLocalPonto;
    }

    public void setMapaLocalPonto(String mapaLocalPonto) {
        this.mapaLocalPonto = mapaLocalPonto;
    }

    public String gerarURLServidor1(String empresa, String dtCompet, String matricula, 
            String batida){
        String caminhoFoto = SERVIDOR_MOBILE_FOTO + empresa + "/ponto/";
        caminhoFoto += FuncoesString.RecortaString(dtCompet, 0, 10).replaceAll("-", "") + "/";
        caminhoFoto += FuncoesString.PreencheEsquerda(matricula.replace(".0", ""), 8, "0")
                + "_" + batida.replace(".0", "") + ".jpg";
        return caminhoFoto;                
    }
    
    public String preencherURL(String url, String empresa, String dtCompet, 
            String matricula, String batida){
        if ((url == null) || ("".equals(url))){
            return gerarURLServidor1(empresa, dtCompet, matricula, batida);
        }
        return url;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }    

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    } 

    public void limparPesquisa() {
        filters.replace("matricula", "");
        filters.replace("posto", "");
        filtroNomeFuncionario = "";        
    }
    private void preencherFiltro(String valor) {
        try {
            switch (chavePesquisa) {
                case "MATRICULA":
                    filters.replace("matricula", valor);
                    return;
                case "NOME":
                    filtroNomeFuncionario = "%" + valor + "%";
                    return;
                case "POSTO":
                    filters.replace("posto", valor);
                    return;                  
                default:
                    throw new Exception("CampoNaoExiste");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void limparFiltroCategoria() {
        resumoCategoria.clear();
        resumoCategoria.add("1"); // ENTRADA E SAÍDA
        resumoCategoria.add("11"); // ENTRADA
        resumoCategoria.add("12"); // SAÍDA
        resumoCategoria.add("2"); // RELATÓRIO
        resumoCategoria.add("3"); // RELATÓRIO
        resumoCategoria.add("10"); // RELATÓRIO
        resumoCategoria.add("4"); // RONDA
        resumoCategoria.add("6"); // RONDA
        resumoCategoria.add("5"); // OUTROS
        resumoCategoria.add("9"); // OUTROS
        resumoCategoria.add("7"); // INSPEÇÃO
    }

    public void filtrarCategoria(int tipo) {
        setPaginacaoDesabilitada(true);        
        resumoCategoria.clear();
        switch (tipo) {
            case CATEGORIA_ENTRADA: 
                resumoCategoria.add("1"); // ENTRADA E SAÍDA
                resumoCategoria.add("11"); // ENTRADA
                break;
            case CATEGORIA_SAIDA: 
                resumoCategoria.add("1"); // ENTRADA E SAÍDA
                resumoCategoria.add("12"); // SAÍDA
                break;                
            case CATEGORIA_RELATORIO: 
                resumoCategoria.add("2"); // RELATÓRIO
                resumoCategoria.add("3"); // RELATÓRIO
                resumoCategoria.add("10"); // RELATÓRIO
                break;                
            case CATEGORIA_INSPECAO: 
                resumoCategoria.add("7"); // INSPEÇÃO
                break;                
            case CATEGORIA_RONDA: 
                resumoCategoria.add("4"); // RONDA
                resumoCategoria.add("6"); // RONDA
                break;                
            case CATEGORIA_OUTRA: 
                resumoCategoria.add("5"); // OUTROS
                resumoCategoria.add("9"); // OUTROS
                break;                
        }    
        try {
            // Limpar o condeúdo do valor pesquisa para trazer todos os registros
            valorPesquisa = "";
            pesquisarValor();            
        } catch (Exception ex) {
            Logger.getLogger(SatMobEWMB.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void filtrarCategoriaEntrada(){
        filtrarCategoria(CATEGORIA_ENTRADA);
    }

    public void filtrarCategoriaSaida(){
        filtrarCategoria(CATEGORIA_SAIDA);
    }

    public void filtrarCategoriaRelatorio(){
        filtrarCategoria(CATEGORIA_RELATORIO);
    }

    public void filtrarCategoriaInspecao(){
        filtrarCategoria(CATEGORIA_INSPECAO);
    }

    public void filtrarCategoriaRonda(){
        filtrarCategoria(CATEGORIA_RONDA);
    }
    
    public void filtrarCategoriaOutra(){
        filtrarCategoria(CATEGORIA_OUTRA);
    }    
    
    public void limparFiltroPesquisa() throws Exception {
        limparPesquisa();
        limparFiltroCategoria();
        this.lazyGride = getAllLogs();
        this.logsSatMobEWSelecionado = null;
    }

    public boolean isPaginacaoDesabilitada() {
        return paginacaoDesabilitada;
    }

    public void setPaginacaoDesabilitada(boolean paginacaoDesabilitada) {
        this.paginacaoDesabilitada = paginacaoDesabilitada;
        if (paginacaoDesabilitada){
            quantidadeLinha = QUANTIDADE_LINHA_MAXIMA;
        } else {
            quantidadeLinha = QUANTIDADE_LINHA_MINIMA;
        }
    }       

    public int getQuantidadeLinha() {
        return quantidadeLinha;
    }

    public void setQuantidadeLinha(int quantidadeLinha) {
        this.quantidadeLinha = quantidadeLinha;
    }
    
    
}
