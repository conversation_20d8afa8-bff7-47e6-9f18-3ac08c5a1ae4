/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.comercial;

import Arquivo.ArquivoLog;
import Controller.Clientes.ClientesSatMobWeb;
import Controller.Guias.GuiasSatWeb;
import Controller.Login.LoginSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.ClientesEmail;
import SasBeans.ClientesImg;
import SasBeans.CxFGuiasVol;
import SasBeans.EmailsEnviar;
import SasBeans.Fechaduras;
import SasBeans.Filiais;
import SasBeans.GTVeAcesso;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.MovimentacaoContainer;
import SasBeans.GuiasCliente;
import SasBeans.Municipios;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.RamosAtiv;
import SasBeans.Regiao;
import SasBeans.SasPWFill;
import SasBeans.Saspw;
import SasBeans.PessoaLogin;
import SasDaos.ClientesDao;
import SasDaos.EmailsEnviarDao;
import SasDaos.FechadurasDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import SasDaos.SaspwDao;
import br.com.sasw.lazydatamodels.ClientesLazyList;
import br.com.sasw.pacotesuteis.utilidades.BuscarEndereco;
import static br.com.sasw.pacotesuteis.utilidades.BuscarEndereco.BuscarLatLon;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.pacotesuteis.utilidades.ValidadorCPF_CNPJ;
import br.com.sasw.utils.LocaleController;
import br.com.sasw.utils.Logger;
import br.com.sasw.utils.Mascaras;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.json.JSONArray;
import org.primefaces.json.JSONObject;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.UploadedFile;
import org.primefaces.model.map.DefaultMapModel;
import org.primefaces.model.map.LatLng;
import org.primefaces.model.map.MapModel;
import org.primefaces.model.map.Marker;

/**
 *
 * <AUTHOR>
 */
@Named(value = "clientes")
@ViewScoped
public class ClientesMB implements Serializable {

    private Persistencia persistencia, satellite;
    private List<Clientes> lista;
    private List<ClientesEmail> buscaEmails;
    private List<PessoaCliAut> usuarios, novosUsuarios;
    private PessoaCliAut novoUsuario, novoPessoaChave;
    private Clientes clienteSelecionado, novoCliente, origem, destino, faturamento, novoClienteCaptura;
    private ClientesEmail emailNovo;
    private final ClientesSatMobWeb clientessatmobweb;
    private final GuiasSatWeb guiassatweb;
    private List<MovimentacaoContainer> historicoMovimentacao;
    private BigDecimal codPessoa;
    private String codfil, escolha, banco, operador, codigo, nome, nred, agencia, filialDesc,
            cpfcnpj, ierg, centroMapa,
            pesquisa, param, caminho, log, data1, data2, extenso, codBarras,
            dataContainer1, dataContainer2, dataTela, fotoCarregada, largutaImagem, alturaImagem, listaClientesQrCode, googleApiMob, googleApiOper, clientesImgHtml, Obs;
    private ArquivoLog logerro;
    private int flag, total;
    private Boolean mostrarFiliais, limparFiltros, selecionado, somenteAtivos;
    private List<Municipios> cidades;
    private SasPWFill filial;
    private Filiais filialGuia, filiais;
    private UploadedFile uploadedFile;
    private final LoginSatMobWeb loginsatmobweb;
    private LazyDataModel<Clientes> clientes = null;
    private Fechaduras fechadura, fechaduraSelecionada;
    private List<Fechaduras> listaFechaduras;
    private Map filters;
    private boolean eFilial, eNome, eEmail, eInterfExt, eNRed,
            eRG, eCPF, eCNPJ, eInscMun, eInscEst, eLat, eLon,
            eCodigo, eFone1, eFone2, eEnde, eBairro, eCidade, eUF, eCEP,
            eOperador, eDtAlter, eHrAlter;
    private List<GuiasCliente> guias;
    private GuiasCliente guiaSelecionada;
    private List<CxFGuiasVol> lacres;
    private List<EmailsEnviar> emails;
    private EmailsEnviar email;
    private List<RamosAtiv> ramosAtiv;
    private RamosAtiv ramoAtiv;
    private MapModel mapaCliente;
    private List<Regiao> regioes;
    private Regiao regiao, novaRegiao;
    private final RotasSatWeb rotassatweb;
    private List<GTVeAcesso> chavesAcesso;
    private GTVeAcesso gtveAcesso;

    // Propriedades para criação de novo usuário
    private String emailUsuario;
    private boolean usuarioExistenteSelecionado;
    private boolean podeGravarUsuario;

    private final String CODFIL = "clientes.codFil = ? ",
            CODIGO = "clientes.codigo LIKE ? ",
            NOME = "clientes.nome LIKE ? ",
            NRED = "clientes.nred LIKE ? ",
            AGENCIA = "clientes.agencia LIKE ? ",
            CHAVE = "clientes.NroChave = ? ",
            ENDERECO = "clientes.ende LIKE ? ",
            SEMLATLON = "(clientes.latitude IS NULL OR clientes.longitude IS NULL OR clientes.latitude = '' OR clientes.longitude = '' OR clientes.latitude = ?) ",
            BAIRRO = "clientes.Bairro LIKE ? ",
            REGIAO = "clientes.Regiao LIKE ? ",
            SITUACAO = "clientes.situacao = ? ",
            DT_ALTER = "Clientes.Dt_Alter = ? ";
    private String chavePesquisa = "NRED", chaveOrdem = "NRED", valorPesquisa;

    public ClientesMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        filialDesc = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        novoCliente = new Clientes();
        novoClienteCaptura = new Clientes();
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codigo = "";
        nome = "";
        nred = "";
        agencia = "";
        clientessatmobweb = new ClientesSatMobWeb();
        clienteSelecionado = new Clientes();
        escolha = "cgc";
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        loginsatmobweb = new LoginSatMobWeb();
        logerro = new ArquivoLog();
        eFilial = true;
        eNome = true;
        eEmail = true;
        eNRed = true;
        eCPF = true;
        eCNPJ = true;
        eCodigo = true;
        guiassatweb = new GuiasSatWeb();
        guiaSelecionada = new GuiasCliente();
        buscaEmails = new ArrayList<>();
        dataTela = DataAtual.getDataAtual("SQL");
        rotassatweb = new RotasSatWeb();
        somenteAtivos = true;
        chavesAcesso = new ArrayList<>();
        gtveAcesso = new GTVeAcesso();
        clientesImgHtml = "";
    }

    public void Persistencias(Persistencia pstLocal, Persistencia ss) {
        try {
            this.persistencia = pstLocal;
            this.satellite = ss;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.filters = new HashMap();
            this.filters.put(CODFIL, this.codfil);
            this.filters.put(NOME, "");
            this.filters.put(NRED, "");
            this.filters.put(CODIGO, "");
            this.filters.put(AGENCIA, "");
            this.filters.put(CHAVE, "");
            this.filters.put(ENDERECO, "");
            this.filters.put(SITUACAO, "A");
            this.filters.put(BAIRRO, "");
            this.filters.put(REGIAO, "");
            this.filters.put(DT_ALTER, "");
            this.total = this.clientessatmobweb.Contagem(this.filters, this.codPessoa, this.persistencia);
            this.filiais = this.rotassatweb.buscaInfoFilial(this.codfil, this.persistencia);
            this.ramosAtiv = this.clientessatmobweb.listarRamosAtiv(this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public ClientesMB(Persistencia pst) {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        filialDesc = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        novoCliente = new Clientes();
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        persistencia = pst;
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codigo = "";
        nome = "";
        nred = "";
        agencia = "";
        clientessatmobweb = new ClientesSatMobWeb();
        clienteSelecionado = new Clientes();
        escolha = "cgc";
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        log = new String();
        caminho = FacesContext.getCurrentInstance().getExternalContext().getRealPath(File.separator) + "msgerros_" + banco + ".txt";
        loginsatmobweb = new LoginSatMobWeb();
        logerro = new ArquivoLog();
        guiassatweb = new GuiasSatWeb();
        guiaSelecionada = new GuiasCliente();
        buscaEmails = new ArrayList<>();
        emailNovo = new ClientesEmail();
        dataTela = DataAtual.getDataAtual("SQL");
        rotassatweb = new RotasSatWeb();
    }

    public void detalhesGuias() {
        if (null == this.guiaSelecionada) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneGuia"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            try {
                this.lacres = this.guiassatweb.ListarLacres(this.guiaSelecionada, this.persistencia);
                PrimeFaces.current().executeScript("PF('dlgDetalhes').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void Imprimir() {
        if (null == this.guiaSelecionada) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneGuia"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            try {
                this.lacres = this.guiassatweb.ListarLacres(this.guiaSelecionada, this.persistencia);
                this.filialGuia = this.guiassatweb.filialNota(this.guiaSelecionada.getCodfil(), this.persistencia);
                this.origem = this.clientessatmobweb.ListaClientes(this.guiaSelecionada.getCodfil(), this.guiaSelecionada.getCodorigem(), "", "", "", this.persistencia).get(0);
                this.destino = this.clientessatmobweb.ListaClientes(this.guiaSelecionada.getCodfil(), this.guiaSelecionada.getCoddst(), "", "", "", this.persistencia).get(0);
                this.faturamento = this.clientessatmobweb.ListaClientes(this.guiaSelecionada.getCodfil(), this.guiaSelecionada.getCodfat(), "", "", "", this.persistencia).get(0);
                this.codBarras = FuncoesString.PreencheEsquerda(this.guiaSelecionada.getGuia().substring(0,
                        this.guiaSelecionada.getGuia().lastIndexOf(".0") == -1
                        ? this.guiaSelecionada.getGuia().length()
                        : this.guiaSelecionada.getGuia().lastIndexOf(".0")),
                        8, "0") + " " + this.guiaSelecionada.getSerie();

                this.extenso = Messages.getValorExtensoS(this.guiaSelecionada.getValor());
                this.guiaSelecionada.setCodfil(FuncoesString.PreencheEsquerda(this.guiaSelecionada.getCodfil().substring(0, this.guiaSelecionada.getCodfil().length() - 2), 4, "0"));
                this.guiaSelecionada.setOS(FuncoesString.PreencheEsquerda(this.guiaSelecionada.getOS().substring(0, this.guiaSelecionada.getOS().length() - 2), 6, "0"));
                PrimeFaces.current().executeScript("PF('dlgImprimir').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void BuscarGuias(Clientes cli) {
        this.clienteSelecionado = cli;
        this.data1 = "";
        this.data2 = "";
        ListarGuias();
        if (null != this.clienteSelecionado) {
            //PrimeFaces.current().executeScript("PF('dlgGuias').show();");
        }
    }

    public void utilizarEndereco() {
        try {
            if (null != this.novoCliente.getCEP()
                    && !this.novoCliente.getCEP().trim().equals("")
                    && (null == this.novoCliente.getCEPCob()
                    || this.novoCliente.getCEPCob().trim().equals(""))) {
                this.novoCliente.setCEPCob(this.novoCliente.getCEP());
            }

            if (null != this.novoCliente.getEnde()
                    && !this.novoCliente.getEnde().trim().equals("")
                    && (null == this.novoCliente.getEndCob()
                    || this.novoCliente.getEndCob().trim().equals(""))) {
                this.novoCliente.setEndCob(this.novoCliente.getEnde());
            }

            if (null != this.novoCliente.getCidade()
                    && !this.novoCliente.getCidade().trim().equals("")
                    && (null == this.novoCliente.getCidCob()
                    || this.novoCliente.getCidCob().trim().equals(""))) {
                this.novoCliente.setCidCob(this.novoCliente.getCidade());
            }

            if (null != this.novoCliente.getBairro()
                    && !this.novoCliente.getBairro().trim().equals("")) {

            }

            if (null != this.novoCliente.getEstado()
                    && !this.novoCliente.getEstado().trim().equals("")
                    && (null == this.novoCliente.getUFCob()
                    || this.novoCliente.getUFCob().trim().equals(""))) {
                this.novoCliente.setUFCob(this.novoCliente.getEstado());
            }
        } catch (Exception ex) {
        }
    }

    public void buscarEmails(Clientes cli) {
        this.clienteSelecionado = cli;
        this.data1 = "";
        this.data2 = "";
        this.email = null;
        listarEmails();
        if (null != this.clienteSelecionado) {
            PrimeFaces.current().executeScript("PF('dlgEmails').show();");
        }
    }

    public void listarEmails() {
        if (null == this.clienteSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCliente"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.emails = this.clientessatmobweb.listarEmails(this.clienteSelecionado.getCodFil().toString(), this.clienteSelecionado.getCodigo(),
                        (this.data1), (this.data2), this.persistencia.getEmpresa(), this.satellite);
                this.guiaSelecionada = new GuiasCliente();
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void updateFormularioCofre() {
        try {
            PrimeFaces.current().ajax().update("formCadastrar:nome");
            PrimeFaces.current().ajax().update("formCadastrar:email");
            PrimeFaces.current().ajax().update("formCadastrar:cadastro");
            PrimeFaces.current().ajax().update("formCadastrar:edit");

        } catch (Exception e) {
        }
    }

    public void UpdateFormulario() {
        try {
            PrimeFaces.current().ajax().update("formCadastrar:tabs:codfil");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:nome");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:nred");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:cep");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:bairro");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:ende");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:cidade");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:estado");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:fone1");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:fone2");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:email");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:escolha");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:cgc");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:cpf");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:rg");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:ie");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:im");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:interfext");
        } catch (Exception e) {
            PrimeFaces.current().ajax().update("formCadastrar:nome");
            PrimeFaces.current().ajax().update("formCadastrar:nred");
            PrimeFaces.current().ajax().update("formCadastrar:cep");
            PrimeFaces.current().ajax().update("formCadastrar:bairro");
            PrimeFaces.current().ajax().update("formCadastrar:ende");
            PrimeFaces.current().ajax().update("formCadastrar:cidade");
            PrimeFaces.current().ajax().update("formCadastrar:estado");
            PrimeFaces.current().ajax().update("formCadastrar:fone1");
            PrimeFaces.current().ajax().update("formCadastrar:fone2");
            PrimeFaces.current().ajax().update("formCadastrar:email");
            PrimeFaces.current().ajax().update("formCadastrar:escolha");
            PrimeFaces.current().ajax().update("formCadastrar:pessoa");
            PrimeFaces.current().ajax().update("formCadastrar:cpf");
            PrimeFaces.current().ajax().update("formCadastrar:rg");
            PrimeFaces.current().ajax().update("formCadastrar:cgc");
            PrimeFaces.current().ajax().update("formCadastrar:ie");
            PrimeFaces.current().ajax().update("formCadastrar:im");
            PrimeFaces.current().ajax().update("formCadastrar:interfex");

        }
    }

    public void abrirEmail() {
        if (null == this.email) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneEmail"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.email.setMensagem(this.email.getMensagem().replace("window.document.write(code128", ""));
                this.email.setMensagem(this.email.getMensagem().replace("</script>", "</style>"));
                this.email.setMensagem(this.email.getMensagem().replace("<script>", "<style>"));
                PrimeFaces.current().executeScript("PF('dlgEmail').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void enviarEmail() {
        if (null == this.email) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneEmail"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.clientessatmobweb.enviarEmail(this.email, this.satellite);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void ListarGuias() {
        if (null == this.clienteSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCliente"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.guias = this.guiassatweb.Listar(this.codPessoa, this.clienteSelecionado.getCodFil().toString(), this.clienteSelecionado.getCodigo(),
                        (this.data1), (this.data2), this.persistencia);

                if (this.guias.size() > 0) {
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("origem", "../comercial/clientes.xhtml");
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("cliente", this.clienteSelecionado.getCodigo());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("nomeCliente", this.clienteSelecionado.getNRed());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("agencia", this.clienteSelecionado.getAgencia());
                    FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("subagencia", this.clienteSelecionado.getSubAgencia());
                    FacesContext.getCurrentInstance().getExternalContext().redirect("../guia/guiasclientes.xhtml");
                } else {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SemGuias"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                }
                //this.guiaSelecionada = new GuiasCliente();

            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void Listar() {
        try {
            this.lista = this.clientessatmobweb.ListaClientes(this.codfil, this.codigo, this.nome, this.nred, this.agencia, this.persistencia);
            this.limparFiltros = false;
            this.mostrarFiliais = false;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Endereco() {
        try {
            String enderecoCompleto = new BuscarEndereco().BuscarPeloCEP(this.novoCliente.getCEP());
            JSONObject obj = new JSONObject(enderecoCompleto);
            if (Integer.parseInt(obj.get("resultado").toString()) == 1) {
                this.novoCliente.setBairro(obj.get("bairro").toString());
                this.novoCliente.setEnde(obj.get("tipo_logradouro").toString() + " " + obj.get("logradouro").toString());
                if (obj.get("uf").toString().equals("DF") || obj.get("uf").toString().equals("df")) {
                    this.novoCliente.setCidade("BRASILIA");
                    this.novoCliente.setEstado("DF");
                } else {
                    this.cidades = this.clientessatmobweb.ListaMunicipios(obj.get("uf").toString(), obj.get("cidade").toString(), this.persistencia);
                    this.novoCliente.setCidade(this.cidades.get(0).getNome());
                    this.novoCliente.setEstado(this.cidades.get(0).getUF().substring(0, 2));
                }
                this.novoCliente.setCEP(Mascaras.CEP(this.novoCliente.getCEP()));

                try {
                    String latlon = BuscarLatLon(this.novoCliente.getEnde(), this.googleApiMob);
                    JSONObject objLatLon = new JSONObject(latlon);
                    Object resultsObject = objLatLon.get("results");
                    if (resultsObject instanceof JSONArray) {
                        if (((JSONArray) resultsObject).length() >= 1) {
                            this.novoCliente.setLatitude((((JSONObject) ((JSONObject) ((JSONObject) ((JSONArray) resultsObject).get(0))
                                    .get("geometry")).get("location")).get("lat")).toString());
                            this.novoCliente.setLongitude((((JSONObject) ((JSONObject) ((JSONObject) ((JSONArray) resultsObject).get(0))
                                    .get("geometry")).get("location")).get("lng")).toString());
                        }
                    } else if (resultsObject instanceof JSONObject) {
                        this.novoCliente.setLatitude((((JSONObject) ((JSONObject) ((JSONObject) resultsObject)
                                .get("geometry")).get("location")).get("lat")).toString());
                        this.novoCliente.setLongitude((((JSONObject) ((JSONObject) ((JSONObject) resultsObject)
                                .get("geometry")).get("location")).get("lng")).toString());
                    }
                } catch (Exception eLatLon) {
                    this.log = this.getClass().getSimpleName() + "\r\n"
                            + Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "\r\n" + eLatLon.getMessage() + "\r\n";
                    this.logerro.Grava(this.log, this.caminho);
                }

                PrimeFaces.current().executeScript("PF('dlgOk').show()");
            } else {
                this.novoCliente.setCEP(Mascaras.CEP(this.novoCliente.getCEP()));
                throw new Exception(Messages.getMessageS("EnderecoNaoEncontrado"));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<String> BuscarCidade(String query) {
        try {
            List<String> retorno = new ArrayList<>();
            this.cidades = this.clientessatmobweb.ListaMunicipios(query, this.persistencia);
            for (Municipios cidade : this.cidades) {
                retorno.add(cidade.getNome() + ", " + cidade.getUF());
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    public void SelecionarCidade(SelectEvent event) {
        String[] parts = event.getObject().toString().split(", ");
        this.novoCliente.setCidade(parts[0]);
        this.novoCliente.setEstado(parts[1]);
    }

    public void SelecionarCidadeFat(SelectEvent event) {
        String[] parts = event.getObject().toString().split(", ");
        this.novoCliente.setCidCob(parts[0]);
        this.novoCliente.setUFCob(parts[1]);
    }

    public void Cadastrar() {
        try {
            this.novoCliente.setCodFil(this.filial.getCodfilAc());
            this.novoCliente.setOper_Alt(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novoCliente.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novoCliente.setDt_Alter(LocalDate.now());

            if (this.novoCliente.getNome() != null) {
                this.novoCliente.setNome(this.novoCliente.getNome().toUpperCase());
            }
            if (this.novoCliente.getNRed() != null) {
                this.novoCliente.setNRed(this.novoCliente.getNRed().toUpperCase());
            }
            if (this.novoCliente.getEnde() != null) {
                this.novoCliente.setEnde(this.novoCliente.getEnde().toUpperCase());
            }
            if (this.novoCliente.getBairro() != null) {
                this.novoCliente.setBairro(this.novoCliente.getBairro().toUpperCase());
            }
            if (this.novoCliente.getCidade() != null) {
                this.novoCliente.setCidade(this.novoCliente.getCidade().toUpperCase());
            }
            if (this.novoCliente.getEstado() != null) {
                this.novoCliente.setEstado(this.novoCliente.getEstado().toUpperCase());
            }
            if (this.novoCliente.getIE() != null) {
                this.novoCliente.setIE(this.novoCliente.getIE().toUpperCase());
            }
            this.novoCliente.setSituacao("A");
            this.novoCliente.setDiaFechaFat(1);
            this.novoCliente.setDiaVencNF(1);
            if (this.regiao == null) {
                this.novoCliente.setRegiao("999");
            } else {
                this.novoCliente.setRegiao(this.regiao.getRegiao());
            }

            if (this.escolha.equals("cpf")) {
                this.novoCliente.setRG(this.ierg);
            } else {
                this.novoCliente.setInsc_Munic(null != this.ierg ? this.ierg.toUpperCase() : "");
            }

            this.novoCliente.setFone1(Mascaras.removeMascara(this.novoCliente.getFone1()));
            this.novoCliente.setFone2(Mascaras.removeMascara(this.novoCliente.getFone2()));

            Locale locale = LocaleController.getsCurrentLocale();
            if (locale.getLanguage().toUpperCase().equals("PT")) {
                if (!this.novoCliente.getCPF().isEmpty()) {
                    this.novoCliente.setCPF(Mascaras.removeMascara(this.novoCliente.getCPF()));
                    if (!ValidadorCPF_CNPJ.ValidarCPF(this.novoCliente.getCPF())) {
                        throw new Exception(Messages.getMessageS("CPFInvalido"));
                    }
                }
                if (!this.novoCliente.getCGC().isEmpty()) {
                    this.novoCliente.setCGC(Mascaras.removeMascara(this.novoCliente.getCGC()));
                    if (!ValidadorCPF_CNPJ.ValidarCNPJ(Mascaras.removeMascara(this.novoCliente.getCGC()))) {
                        throw new Exception(Messages.getMessageS("CNPJInvalido"));
                    }
                }
            }

            this.novoCliente.setCEP(Mascaras.removeMascara(this.novoCliente.getCEP()));
            this.novoCliente.setObs(this.novoCliente.getObs());
            this.clientessatmobweb.GravaNovoCliente(this.novoCliente, this.codPessoa, this.persistencia, this.logerro, this.caminho);

            ClientesDao clientesDao = new ClientesDao();
            clientesDao.criarContratoPstAutomaticamente(this.novoCliente, this.persistencia);

            for (PessoaCliAut f : this.usuarios) {
                this.clientessatmobweb.inserirUsuario(f, this.persistencia);
            }

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, this.caminho);
        }
    }

    public void excluirImagem() {
        String foto = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("foto");

        PrimeFaces.current().executeScript("$('.FotoPendente[foto=\"" + foto + "\"]').remove(); TratarFotosCarregadas();");
    }

    public void excluirImagemBD() throws Exception {
        String seqFoto = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("sequencia");

        ClientesDao clientesDao = new ClientesDao();
        ClientesImg clientesImg = new ClientesImg();

        clientesImg.setCodFil(this.clienteSelecionado.getCodFil().toPlainString().replace(".0", ""));
        clientesImg.setCodigo(this.clienteSelecionado.getCodigo());
        clientesImg.setSequencia(seqFoto);

        clientesDao.excluirImagem(clientesImg, this.persistencia);

        carregarImagensCliente(this.clienteSelecionado);

        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExcluidoSucesso"), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    public void tornarImagemPadrao() throws Exception {
        String seqFoto = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("sequencia");

        ClientesDao clientesDao = new ClientesDao();
        ClientesImg clientesImg = new ClientesImg();

        clientesImg.setCodFil(this.clienteSelecionado.getCodFil().toPlainString().replace(".0", ""));
        clientesImg.setCodigo(this.clienteSelecionado.getCodigo());
        clientesImg.setSequencia(seqFoto);

        clientesDao.tornarImagemPadrao(clientesImg, this.persistencia);

        carregarImagensCliente(this.clienteSelecionado);

        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("DadosSalvosSucesso"), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    public void HandleFileUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                String Teste1 = this.largutaImagem;
                String Teste2 = this.alturaImagem;

                this.uploadedFile = fileUploadEvent.getFile();
                String NomeArquivoUpload = DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                NomeArquivoUpload = NomeArquivoUpload.replace("-", "").replace(":", "").replace(" ", "").replace("/", "");

                new File("C:\\xampp\\htdocs\\satellite\\fotos\\" + this.persistencia.getEmpresa() + "\\cliente\\" + this.codfil).mkdirs();
                new File("S:\\fotos\\" + this.persistencia.getEmpresa() + "\\cliente\\" + this.codfil).mkdirs();
                int Tamanho = this.uploadedFile.getFileName().toString().length();
                String NomeArquivo = this.uploadedFile.getFileName().toString();
                Tamanho = Tamanho - 4;
                String ExtensaoArquivo = NomeArquivo.replace(NomeArquivo.substring(0, Tamanho), "");

                if (ExtensaoArquivo.indexOf(".") == -1) {
                    ExtensaoArquivo = "." + ExtensaoArquivo;
                }

                String arquivo = "C:\\xampp\\htdocs\\satellite\\fotos\\" + this.persistencia.getEmpresa() + "\\cliente\\" + this.codfil + "\\" + NomeArquivoUpload + ExtensaoArquivo;
                String arquivo2 = "S:\\fotos\\" + this.persistencia.getEmpresa() + "\\cliente\\" + this.codfil + "\\" + NomeArquivoUpload + ExtensaoArquivo;

                byte[] conteudo = fileUploadEvent.getFile().getContents();
                FileOutputStream fos = new FileOutputStream(arquivo);
                fos.write(conteudo);
                fos.close();

                FileOutputStream fos2 = new FileOutputStream(arquivo2);
                fos2.write(conteudo);
                fos2.close();
                /*double angleOfRotation = 90.0;
                String Extensao = FilenameUtils.getExtension(this.uploadedFile.getFileName());

                BufferedImage imagemOriginal = ImageIO.read(this.uploadedFile.getInputstream());

                if (imagemOriginal.getWidth() >= 2448 || imagemOriginal.getHeight() >= 3264) {
                    BufferedImage imagemProcessada = rotateMyImage(imagemOriginal, angleOfRotation);
                    ImageIO.write(imagemProcessada, Extensao, new File(arquivo));
                } else {
                    ImageIO.write(imagemOriginal, Extensao, new File(arquivo));
                } 
                 */
                String imgRef = "https://mobile.sasw.com.br:9091/satellite/fotos/" + this.persistencia.getEmpresa() + "/cliente/" + this.codfil + "/" + NomeArquivoUpload + ExtensaoArquivo;

                PrimeFaces.current().executeScript("$('.FotoPendente').find('.fa-spin').parents('.FotoPendente').css('background-image', 'url(\"" + imgRef + "\")').find('table').css('display', 'none').find('.fa-spin').attr('class','fa fa-camera'); Rotacionar('" + imgRef + "'); TratarFotosCarregadas();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void HandleFileUpload2(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                String Teste1 = this.largutaImagem;
                String Teste2 = this.alturaImagem;

                this.uploadedFile = fileUploadEvent.getFile();
                String NomeArquivoUpload = DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                NomeArquivoUpload = NomeArquivoUpload.replace("-", "").replace(":", "").replace(" ", "").replace("/", "");

                new File("C:\\xampp\\htdocs\\satellite\\fotos\\" + this.persistencia.getEmpresa() + "\\cliente\\" + this.codfil).mkdirs();
                new File("S:\\fotos\\" + this.persistencia.getEmpresa() + "\\cliente\\" + this.codfil).mkdirs();
                int Tamanho = this.uploadedFile.getFileName().toString().length();
                String NomeArquivo = this.uploadedFile.getFileName().toString();
                Tamanho = Tamanho - 4;
                String ExtensaoArquivo = NomeArquivo.replace(NomeArquivo.substring(0, Tamanho), "");

                if (ExtensaoArquivo.indexOf(".") == -1) {
                    ExtensaoArquivo = "." + ExtensaoArquivo;
                }

                String arquivo = "C:\\xampp\\htdocs\\satellite\\fotos\\" + this.persistencia.getEmpresa() + "\\cliente\\" + this.codfil + "\\" + NomeArquivoUpload + ExtensaoArquivo;

                String arquivo2 = "S:\\fotos\\" + this.persistencia.getEmpresa() + "\\cliente\\" + this.codfil + "\\" + NomeArquivoUpload + ExtensaoArquivo;

                byte[] conteudo = fileUploadEvent.getFile().getContents();
                FileOutputStream fos = new FileOutputStream(arquivo);
                fos.write(conteudo);
                fos.close();

                FileOutputStream fos2 = new FileOutputStream(arquivo2);
                fos2.write(conteudo);
                fos2.close();

                String imgRef = "https://mobile.sasw.com.br:9091/satellite/fotos/" + this.persistencia.getEmpresa() + "/cliente/" + this.codfil + "/" + NomeArquivoUpload + ExtensaoArquivo;

                ClientesDao clientesDao = new ClientesDao();
                ClientesImg clientesImg = new ClientesImg();

                clientesImg.setCodFil(this.clienteSelecionado.getCodFil().toPlainString().replace(".0", ""));
                clientesImg.setCodigo(this.clienteSelecionado.getCodigo());
                clientesImg.setDt_Alter(DataAtual.getDataAtual("SQL"));
                clientesImg.setHr_Alter(DataAtual.getDataAtual("HORA"));
                clientesImg.setImagem(imgRef);
                clientesImg.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));

                clientesDao.inserirImagem(clientesImg, this.persistencia);

                carregarImagensCliente(this.clienteSelecionado);

                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("FotoCarregadaSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public static BufferedImage rotateMyImage(BufferedImage img, double angle) {
        int w = img.getWidth();
        int h = img.getHeight();
        BufferedImage dimg = new BufferedImage(w, h, img.getType());
        Graphics2D g = dimg.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, // Anti-alias!
                RenderingHints.VALUE_ANTIALIAS_ON);

        g.rotate(Math.toRadians(angle), w / 2, h / 2);

        g.drawImage(img, null, 0, 0);
        return dimg;
    }

    public void cadastrarCaptura() {
        try {
            this.novoClienteCaptura.setCodFil(this.codfil);
            this.novoClienteCaptura.setOper_Alt(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novoClienteCaptura.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novoClienteCaptura.setDt_Alter(LocalDate.now());
            this.novoClienteCaptura.setDtSituacao(DataAtual.getDataAtual("SQL"));
            this.novoClienteCaptura.setOper_Inc(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novoClienteCaptura.setDt_Cad(LocalDate.now());

            if (this.novoClienteCaptura.getNome() != null) {
                this.novoClienteCaptura.setNome(this.novoClienteCaptura.getNome().toUpperCase());
            }
            if (this.novoClienteCaptura.getNRed() != null) {
                this.novoClienteCaptura.setNRed(this.novoClienteCaptura.getNRed().toUpperCase());
            }
            if (this.novoClienteCaptura.getEnde() != null) {
                this.novoClienteCaptura.setEnde(this.novoClienteCaptura.getEnde().toUpperCase());
            }
            if (this.novoClienteCaptura.getBairro() != null) {
                this.novoClienteCaptura.setBairro(this.novoClienteCaptura.getBairro().toUpperCase());
            }
            if (this.novoClienteCaptura.getCidade() != null) {
                this.novoClienteCaptura.setCidade(this.novoClienteCaptura.getCidade().toUpperCase());
            }
            if (this.novoClienteCaptura.getEstado() != null) {
                this.novoClienteCaptura.setEstado(this.novoClienteCaptura.getEstado().toUpperCase());
            }
            this.novoClienteCaptura.setSituacao("A");
            this.novoClienteCaptura.setDiaFechaFat(1);
            this.novoClienteCaptura.setDiaVencNF(1);

            this.novoClienteCaptura.setRegiao("999");
            this.novoClienteCaptura.setTpCli("0");
            this.novoClienteCaptura.setBanco("777");
            this.novoClienteCaptura.setFoto(this.fotoCarregada);

            if (null != this.novoClienteCaptura.getCEP()
                    && !this.novoClienteCaptura.getCEP().equals("")) {
                this.novoClienteCaptura.setCEP(Mascaras.removeMascara(this.novoClienteCaptura.getCEP()));
            }

            this.clientessatmobweb.GravaNovoCliente(this.novoClienteCaptura, this.codPessoa, this.persistencia, this.logerro, this.caminho);

            ClientesDao clientesDao = new ClientesDao();
            clientesDao.criarContratoPstAutomaticamente(this.novoClienteCaptura, this.persistencia);
            this.novoClienteCaptura = new Clientes();

            PrimeFaces.current().executeScript("PF('dlgCapturarCliente').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, this.caminho);
        }
    }

    public void excluirFechadura() {
        try {
            if (null == this.fechaduraSelecionada) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                FechadurasDao fechadurasDao = new FechadurasDao();
                this.fechaduraSelecionada.setOperExcl(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.fechaduraSelecionada.setHr_Excl(DataAtual.getDataAtual("HORA"));
                this.fechaduraSelecionada.setDt_Excl(DataAtual.getDataAtual("SQL"));
                fechadurasDao.excluirFechadura(this.fechaduraSelecionada, this.persistencia);

                this.fechaduraSelecionada = null;
                this.fechadura = new Fechaduras();
                this.listaFechaduras = fechadurasDao.listaFechadurasCliente(this.novoCliente.getCodigo(), this.novoCliente.getCodFil().toPlainString(), this.persistencia);

                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void CadastroFechadura() {
        try {
            FechadurasDao fechadurasDao = new FechadurasDao();
            this.fechadura.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.fechadura.setHr_alter(DataAtual.getDataAtual("HORA"));
            this.fechadura.setDt_Alter(DataAtual.getDataAtual("SQL"));

            if (null == this.fechadura.getPK_Fechadura()
                    || this.fechadura.getPK_Fechadura().trim().equals("")) {
                this.fechadura.setPK_Fechadura("0");
            }

            if (null == this.fechaduraSelecionada) {
                // Insert
                this.fechadura.setCodFil(this.codfil);
                this.fechadura.setCodCli(this.clienteSelecionado.getCodigo());
                fechadurasDao.inserirFechadura(this.fechadura, this.persistencia);
            } else {
                // Update
                fechadurasDao.atualizarFechadura(this.fechadura, this.persistencia);
            }

            this.fechaduraSelecionada = null;
            this.fechadura = new Fechaduras();
            this.listaFechaduras = fechadurasDao.listaFechadurasCliente(this.novoCliente.getCodigo(), this.novoCliente.getCodFil().toPlainString(), this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgFechaduras').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void CadastroEmail() {
        try {
            if (!this.emailNovo.getEmail().isEmpty() && !this.emailNovo.getNome().isEmpty()) {
                this.emailNovo.setCodCli(this.clienteSelecionado.getCodigo().toUpperCase());
                this.emailNovo.setCodFil(this.filial.getCodfilAc());
                this.emailNovo.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
                this.emailNovo.setHr_Alter(DataAtual.getDataAtual("HORA"));
                this.emailNovo.setDt_Alter(LocalDate.now());
                this.emailNovo.setNome(this.emailNovo.getNome().toUpperCase());
                this.emailNovo.setEmail(this.emailNovo.getEmail().toLowerCase());

                if (this.clientessatmobweb.ExisteEmail(emailNovo, persistencia).equals(false)) {
                    this.clientessatmobweb.CadastrarEmail(this.emailNovo, this.persistencia);
                    this.buscaEmails = this.clientessatmobweb.BuscarEmail(this.clienteSelecionado.getCodigo(), this.persistencia);
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                } else {
                    this.buscaEmails = this.clientessatmobweb.BuscarEmail(this.clienteSelecionado.getCodigo(), this.persistencia);
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EmailCadastrado"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                }
                PrimeFaces.current().executeScript("PF('dlgAdicionarEmail').hide()");
            } else {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Obrigatorio"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void DeletarEmail() {
        try {
            if (null == this.emailNovo) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SelecioneEmail"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.emailNovo.setEmail(this.emailNovo.getEmail());
                this.emailNovo.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.emailNovo.setHr_Alter(DataAtual.getDataAtual("HORA"));
                this.emailNovo.setDt_Alter(LocalDate.now());

                this.clientessatmobweb.DeletarEmail(this.emailNovo, this.persistencia);
                this.buscaEmails = this.clientessatmobweb.BuscarEmail(this.clienteSelecionado.getCodigo(), this.persistencia);
                PrimeFaces.current().ajax().update("formCadastrar:tabs:emails");

                this.emailNovo = null;
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);

            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void NovoEmail(ActionEvent ac) {
        try {
            if (this.novoCliente.getCodigo().isEmpty()) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ClienteNaoCadastrado"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                this.emailNovo = new ClientesEmail();

                PrimeFaces.current().resetInputs("formCadastrarEmail:addEmail");
                PrimeFaces.current().executeScript("PF('dlgAdicionarEmail').show();");
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + "\r\n" + Logger.objeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void NovaFechadura(ActionEvent ac) {
        try {
            if (this.novoCliente.getCodigo().isEmpty()) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ClienteNaoCadastrado"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                this.fechadura = new Fechaduras();
                this.fechaduraSelecionada = null;

                PrimeFaces.current().resetInputs("formFechaduras");
                PrimeFaces.current().executeScript("PF('dlgFechaduras').show();");
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + "\r\n" + Logger.objeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dblFechadura(SelectEvent event) {
        this.fechaduraSelecionada = (Fechaduras) event.getObject();
        this.fechadura = new Fechaduras();
        this.fechadura = this.fechaduraSelecionada;

        PrimeFaces.current().resetInputs("formFechaduras");
        PrimeFaces.current().executeScript("PF('dlgFechaduras').show();");
    }

    public void EditarFechadura(ActionEvent ac) {
        try {
            if (null == this.fechaduraSelecionada) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                this.fechadura = new Fechaduras();
                this.fechadura = this.fechaduraSelecionada;

                PrimeFaces.current().resetInputs("formFechaduras");
                PrimeFaces.current().executeScript("PF('dlgFechaduras').show();");
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + "\r\n" + Logger.objeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void criarChave() {
        this.novoPessoaChave = new PessoaCliAut();
        this.novosUsuarios = new ArrayList<>();
        this.novosUsuarios.add(this.novoPessoaChave);

        this.gtveAcesso = new GTVeAcesso();
        this.novoPessoaChave = new PessoaCliAut();

        PrimeFaces.current().resetInputs("formCadastrarChaveAcesso");
        PrimeFaces.current().ajax().update("formCadastrarChaveAcesso");
        PrimeFaces.current().executeScript("PF('dlgAdicionarChave').show();");
    }

    public void editarChave() {
        if (null != this.gtveAcesso) {

        } else {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void excluirChave() throws Exception {
        if (null != this.gtveAcesso && null != this.gtveAcesso.getCodPessoa()) {
            ClientesDao clientesDao = new ClientesDao();
            clientesDao.excluirChaveAcesso(this.clienteSelecionado.getCodigo(), this.gtveAcesso.getCodPessoa(), this.clienteSelecionado.getCodFil().toPlainString().replace(".0", ""), FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia, this.satellite);

            this.chavesAcesso = clientesDao.buscarChaveAcesso(this.novoCliente.getCodigo(), this.novoCliente.getCodFil().toPlainString().replace(".0", ""), this.persistencia, this.satellite);

            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExcluidoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void buttonAction(Clientes cli) {
        this.clienteSelecionado = cli;
        this.fechaduraSelecionada = null;

        if (null == this.clienteSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCliente"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.flag = 2;

                this.novoCliente = new Clientes(this.clienteSelecionado);
                this.novoPessoaChave = new PessoaCliAut();
                ClientesDao clienteDao = new ClientesDao();
                this.chavesAcesso = clienteDao.buscarChaveAcesso(this.novoCliente.getCodigo(), this.novoCliente.getCodFil().toPlainString().replace(".0", ""), this.persistencia, this.satellite);
                this.novoCliente.setCEP(Mascaras.CEP(Mascaras.removeMascara(this.novoCliente.getCEP())));

                if (!this.novoCliente.getCPF().equals("") && this.novoCliente.getCPF() != null) {
                    this.cpfcnpj = Mascaras.CPF(this.novoCliente.getCPF());
                    this.escolha = "cpf";
                } else {
                    this.cpfcnpj = Mascaras.CNPJ(this.novoCliente.getCGC());
                    this.escolha = "cgc";
                }

                this.filial = this.loginsatmobweb.BuscaFilial(this.novoCliente.getCodFil().toString(), this.codPessoa, this.persistencia);
                this.buscaEmails = this.clientessatmobweb.BuscarEmail(this.novoCliente.getCodigo(), this.persistencia);
                this.usuarios = this.clientessatmobweb.listarUsuariosCadastrados(this.novoCliente.getCodigo(),
                        this.novoCliente.getCodFil().toString(), this.persistencia);
                this.regioes = this.clientessatmobweb.listarRegioes(this.novoCliente.getCodFil().toString(), this.persistencia);
                this.regiao = new Regiao();
                this.regiao.setCodFil(this.novoCliente.getCodFil().toPlainString());
                this.regiao.setRegiao(this.novoCliente.getRegiao());

                this.novoUsuario = new PessoaCliAut();
                this.novosUsuarios = new ArrayList<>();
                this.novosUsuarios.add(this.novoUsuario);

                this.novaRegiao = new Regiao();
                this.novaRegiao.setCodFil(this.novoCliente.getCodFil().toBigInteger().toString());

                this.ramoAtiv = new RamosAtiv();

                this.emailNovo = new ClientesEmail();

                FechadurasDao fechadurasDao = new FechadurasDao();
                this.listaFechaduras = fechadurasDao.listaFechadurasCliente(this.novoCliente.getCodigo(), this.novoCliente.getCodFil().toPlainString(), this.persistencia);

                // Carregar Imagens do Cliente
                carregarImagensCliente(this.clienteSelecionado);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
            TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:tabs");
            tabs.setActiveIndex(0);
            PrimeFaces.current().ajax().update("formCadastrar:cadastrar");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");

        }
    }

    public void carregarImagensCliente(Clientes clienteImg) throws Exception {
        try {
            this.clientesImgHtml = "<div class=\"ThumbFoto\" ref=\"novaFoto\" style=\"background-color: #AAA; color: #505050; font-weight: bold; font-size: 11pt\">\n"
                    + "<i class=\"fa fa-plus-circle\"></i><br>" + getMessageS("NovaFoto")
                    + "      </div>";
            String bseHtml = "<div class=\"ThumbFoto\" style=\"background-image: url({{Img}});\" sequencia=\"{{Sequencia}}\" onclick=\"AbrirFoto('{{Img}}');\">\n"
                    + "        <i class=\"fa fa-times\" title=\"#{localemsgs.Excluir}\"></i>\n"
                    + "        {{btAcao}}\n"
                    + "      </div>";

            ClientesDao clientesDao = new ClientesDao();
            int contador = 0;
            String btAcao = "";

            for (ClientesImg item : clientesDao.listarImagensCliente(clienteImg, this.persistencia)) {
                if (contador == 0) {
                    btAcao = "<a class=\"btn btn-success\"><i class=\"fa fa-check-square-o\" aria-hidden=\"true\"></i>&nbsp;" + getMessageS("FotoPadrao") + "</a>";
                } else {
                    btAcao = "<a class=\"btn btn-warning\"><i class=\"fa fa-cog\" aria-hidden=\"true\"></i>&nbsp;" + getMessageS("TornarPadrao") + "</a>";
                }

                this.clientesImgHtml += bseHtml.replace("{{Img}}", item.getImagem()).replace("{{Sequencia}}", item.getSequencia().replace(".0", "")).replace("{{btAcao}}", btAcao);

                contador++;
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Editar() {
        try {
            this.clienteSelecionado = this.novoCliente;
            this.clienteSelecionado.setCodFil(this.filial.getCodfilAc());
            this.clienteSelecionado.setOper_Alt(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.clienteSelecionado.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.clienteSelecionado.setDt_Alter(LocalDate.now());

            if (this.regiao == null) {
                this.clienteSelecionado.setRegiao("999");
            } else {
                this.clienteSelecionado.setRegiao(this.regiao.getRegiao());
            }

            if (this.escolha.equals("cpf")) {
                this.clienteSelecionado.setRG(this.ierg);
            } else {
                this.clienteSelecionado.setInsc_Munic(this.ierg.toUpperCase());
            }

            Locale locale = LocaleController.getsCurrentLocale();
            if (locale.getLanguage().toUpperCase().equals("PT")) {
                if (!this.clienteSelecionado.getCPF().isEmpty()) {
                    this.clienteSelecionado.setCPF(Mascaras.removeMascara(this.clienteSelecionado.getCPF()));
                    if (!ValidadorCPF_CNPJ.ValidarCPF(this.clienteSelecionado.getCPF())) {
                        throw new Exception(Messages.getMessageS("CPFInvalido"));
                    }
                }
                if (!this.clienteSelecionado.getCGC().isEmpty() && !this.cpfcnpj.isEmpty()) {
                    this.clienteSelecionado.setCGC(Mascaras.removeMascara(this.clienteSelecionado.getCGC()));
                    if (!ValidadorCPF_CNPJ.ValidarCNPJ(Mascaras.removeMascara(this.clienteSelecionado.getCGC()))) {
                        throw new Exception(Messages.getMessageS("CNPJInvalido"));
                    }
                } else {
                    this.clienteSelecionado.setCGC("");
                }
            }

            if (!this.cpfcnpj.isEmpty()) {
                this.clienteSelecionado.setCGC(Mascaras.removeMascara(this.cpfcnpj));
            }

            if (this.clienteSelecionado.getNome() != null) {
                this.clienteSelecionado.setNome(this.clienteSelecionado.getNome().toUpperCase());
            }
            if (this.clienteSelecionado.getNRed() != null) {
                this.clienteSelecionado.setNRed(this.clienteSelecionado.getNRed().toUpperCase());
            }
            if (this.clienteSelecionado.getEnde() != null) {
                this.clienteSelecionado.setEnde(this.clienteSelecionado.getEnde().toUpperCase());
            }
            if (this.clienteSelecionado.getBairro() != null) {
                this.clienteSelecionado.setBairro(this.clienteSelecionado.getBairro().toUpperCase());
            }
            if (this.clienteSelecionado.getCidade() != null) {
                this.clienteSelecionado.setCidade(this.clienteSelecionado.getCidade().toUpperCase());
            }
            if (this.clienteSelecionado.getEstado() != null) {
                this.clienteSelecionado.setEstado(this.clienteSelecionado.getEstado().toUpperCase());
            }
            if (this.clienteSelecionado.getIE() != null) {
                this.clienteSelecionado.setIE(this.clienteSelecionado.getIE().toUpperCase());
            }

            this.clienteSelecionado.setFone1(Mascaras.removeMascara(this.clienteSelecionado.getFone1()));
            this.clienteSelecionado.setFone2(Mascaras.removeMascara(this.clienteSelecionado.getFone2()));
            this.clienteSelecionado.setCEP(Mascaras.removeMascara(this.clienteSelecionado.getCEP()));
            if (!this.clienteSelecionado.getCEPCob().isEmpty()) {
                this.clienteSelecionado.setCEPCob(Mascaras.removeMascara(this.clienteSelecionado.getCEPCob()));
            }

            this.clientessatmobweb.GravaCliente(this.clienteSelecionado, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        this.novoCliente = new Clientes();
    }

    public void Filtrar() {
        if (this.mostrarFiliais == true) {
            this.codigo = "";
        } else {
            this.codigo = this.codfil;
        }
        try {
            this.lista = this.clientessatmobweb.PesquisaClientes(this.codigo, this.novoCliente, this.codPessoa, this.persistencia);
            this.codigo = "";
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

//    public void PrePesquisar(){
//        this.novoCliente = new Clientes();
//        this.codigo = "";
//        this.filial = new SasPWFill();
//    }
//
    public void Pesquisar() {
        try {
            if (null != this.filial) {
                this.novoCliente.setCodFil(this.filial.getCodfilAc());
                this.codigo = this.filial.getCodfilAc();
            }
            this.lista = this.clientessatmobweb.PesquisaClientes(this.codigo, this.novoCliente, this.codPessoa, this.persistencia);
            this.mostrarFiliais = false;
            if (this.codigo.equals("")) {
                this.mostrarFiliais = true;
            }
            this.codigo = "";

            this.novoCliente = new Clientes();
            this.codigo = "";
            this.filial = new SasPWFill();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Clientes> ListaClienteCodigo(String codfil, String codigo, Persistencia persistencia) {
        try {
            this.lista = this.clientessatmobweb.ListaClientes(codfil, codigo, "", "", "", persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return lista;
    }

    public void onRowSelect(SelectEvent event) {
        this.clienteSelecionado = (Clientes) event.getObject();
        if (this.clienteSelecionado.getCPF().equals("")) {
            this.escolha = "cgc";
        } else if (this.clienteSelecionado.getCGC().equals("")) {
            this.escolha = "cpf";
        }
    }

    public void dblSelect(SelectEvent event) {
        this.clienteSelecionado = (Clientes) event.getObject();
        buttonAction(null);
    }

    public void BuscarCliente() {
        try {
            this.lista = this.clientessatmobweb.ListaClientes(this.codfil, this.codigo, this.nome, this.nred, this.agencia, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preCaptura() {
        try {
            this.fechadura = new Fechaduras();
            this.fechaduraSelecionada = null;
            this.listaFechaduras = new ArrayList<>();
            this.fotoCarregada = "";
            PrimeFaces.current().executeScript("$('.FotoPendente').find('.fa-spin').parents('.FotoPendente').css('background-image', '').find('table').css('display', '').find('i').attr('class','fa fa-camera');");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }

        PrimeFaces.current().ajax().update("formCapturar:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCapturarCliente').show();");
    }

    public void preCadastro() {
        this.fechadura = new Fechaduras();
        this.fechaduraSelecionada = null;
        this.listaFechaduras = new ArrayList<>();
        this.novoCliente = new Clientes();
        this.flag = 1;
        this.usuarios = new ArrayList<>();
        try {
            this.filial = this.loginsatmobweb.BuscaFilial(this.codfil, this.codPessoa, this.persistencia);
            this.novoCliente.setCodFil(this.filial.getCodfilAc());
            this.buscaEmails = null;
            this.regioes = this.clientessatmobweb.listarRegioes(this.codfil, this.persistencia);
            this.regiao = this.regioes.get(0);

            this.novoUsuario = new PessoaCliAut();
            this.novosUsuarios = new ArrayList<>();
            this.novosUsuarios.add(this.novoUsuario);

            this.novaRegiao = new Regiao();
            this.novaRegiao.setCodFil(this.novoCliente.getCodFil().toBigInteger().toString());

            this.ramoAtiv = new RamosAtiv();

            this.emailNovo = new ClientesEmail();

            this.mapaCliente = new DefaultMapModel();
            LatLng pos = new LatLng(0, 0);
            this.mapaCliente.addOverlay(new Marker(pos, "", null, "https://mobile.sasw.com.br:9091/satmobile/img/icone_cliente_verde.png"));
            this.centroMapa = "0,0";
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
        PrimeFaces.current().ajax().update("formCadastrar:cadastrar");
        TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:tabs");
        tabs.setActiveIndex(0);
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");

    }

    public void mascaraCNPJCPF() {
        try {
            Locale locale = LocaleController.getsCurrentLocale();

            switch (locale.getLanguage().toUpperCase()) {
                case "PT":
                    if (this.cpfcnpj != null) {
                        this.cpfcnpj = Mascaras.removeMascara(this.cpfcnpj);

                        if (this.cpfcnpj.length() == 11) {
                            this.escolha = "cpf";
                            this.cpfcnpj = Mascaras.CPF(this.cpfcnpj);
                        } else {
                            this.escolha = "cgc";
                            if (this.cpfcnpj.length() == 14) {
                                this.cpfcnpj = Mascaras.CNPJ(this.cpfcnpj);
                            }
                        }
                    }
                    break;
            }
            System.out.println("mascaraCNPJCPF: " + this.cpfcnpj);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void removerMascaraCNPJCPF() {
        try {
            this.cpfcnpj = Mascaras.removeMascara(this.cpfcnpj);
            System.out.println("removerMascaraCNPJCPF: " + this.cpfcnpj);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void posicaoCliente() {
        try {
            this.mapaCliente = new DefaultMapModel();

            double lat;
            double lon;

            try {
                lat = Double.parseDouble(this.novoCliente.getLatitude());
                lon = Double.parseDouble(this.novoCliente.getLongitude());
            } catch (Exception e1) {
                String latlon = BuscarLatLon(this.novoCliente.getEnde(), this.googleApiMob);
                JSONObject objLatLon = new JSONObject(latlon);
                Object resultsObject = objLatLon.get("results");
                if (resultsObject instanceof JSONArray) {
                    if (((JSONArray) resultsObject).length() >= 1) {
                        this.novoCliente.setLatitude((((JSONObject) ((JSONObject) ((JSONObject) ((JSONArray) resultsObject).get(0))
                                .get("geometry")).get("location")).get("lat")).toString());
                        this.novoCliente.setLongitude((((JSONObject) ((JSONObject) ((JSONObject) ((JSONArray) resultsObject).get(0))
                                .get("geometry")).get("location")).get("lng")).toString());
                    }
                } else if (resultsObject instanceof JSONObject) {
                    this.novoCliente.setLatitude((((JSONObject) ((JSONObject) ((JSONObject) resultsObject)
                            .get("geometry")).get("location")).get("lat")).toString());
                    this.novoCliente.setLongitude((((JSONObject) ((JSONObject) ((JSONObject) resultsObject)
                            .get("geometry")).get("location")).get("lng")).toString());
                }
                lat = Double.parseDouble(this.novoCliente.getLatitude());
                lon = Double.parseDouble(this.novoCliente.getLongitude());
                LatLng pos = new LatLng(lat, lon);
                this.mapaCliente.addOverlay(new Marker(pos, this.novoCliente.getNRed(),
                        this.novoCliente, "https://mobile.sasw.com.br:9091/satmobile/img/icone_cliente_verde.png"));

                this.centroMapa = this.novoCliente.getLatitude() + "," + this.novoCliente.getLongitude();
                PrimeFaces.current().executeScript("PF('dlgMapaCliente').show();");
            }

            LatLng pos = new LatLng(lat, lon);
            this.mapaCliente.addOverlay(new Marker(pos, this.novoCliente.getNRed(),
                    this.novoCliente, "https://mobile.sasw.com.br:9091/satmobile/img/icone_cliente_verde.png"));

            this.centroMapa = this.novoCliente.getLatitude() + "," + this.novoCliente.getLongitude();
            PrimeFaces.current().executeScript("PF('dlgMapaCliente').show();");
            PrimeFaces.current().ajax().update("gmap");

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("MapaIndisponivel"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void PesquisaPaginada() {
        this.clienteSelecionado = this.novoCliente;
        if (null != this.clienteSelecionado.getCodFil() && !this.clienteSelecionado.getCodFil().equals(BigDecimal.ZERO)) {
            this.filters.replace(CODFIL, this.clienteSelecionado.getCodFil().toPlainString());
            if (this.clienteSelecionado.getCodFil().toBigInteger().toString().equals(this.codfil.replace(".0", ""))) {
                this.mostrarFiliais = false;
            } else {
                this.mostrarFiliais = false;
            }
        } else {
            this.filters.replace(CODFIL, "");
            this.mostrarFiliais = true;
        }
        if (!this.clienteSelecionado.getNome().equals("")) {
            this.filters.replace(NOME, "%" + this.clienteSelecionado.getNome() + "%");
        } else {
            this.filters.replace(NOME, "");
        }
        if (!this.clienteSelecionado.getCodigo().equals("")) {
            this.filters.replace(CODIGO, "%" + this.clienteSelecionado.getCodigo() + "%");
        } else {
            this.filters.replace(CODIGO, "");
        }
        if (!this.clienteSelecionado.getNRed().equals("")) {
            this.filters.replace(NRED, "%" + this.clienteSelecionado.getNRed() + "%");
        } else {
            this.filters.replace(NRED, "");
        }

        getAllClientes();
    }

    public void pesquisarUnico() {
        this.limparPesquisa();
        this.replaceFilter(valorPesquisa);
        getAllClientes();
    }

    private boolean isInteger(String valor) {
        try {
            Integer.parseInt(valor);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private String replaceOrder(String Valor) {
        try {
            switch (this.chaveOrdem) {
                case "CODIGO":
                    return "clientes.codigo";
                case "NOME":
                    return "clientes.nome";
                case "NRED":
                    return "clientes.nred";
                case "AGENCIA":
                    return "clientes.agencia";
                case "BAIRRO":
                    return "clientes.Bairro";
                case "REGIAO":
                    return "clientes.Regiao";
                case "CHAVE":
                    return "clientes.NroChave";
                case "ENDERECO":
                    return "clientes.ende";
                default:
                    return "clientes.nred";
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
        return null;
    }

    public void ordenarGride() {
        clientes = new ClientesLazyList(this.persistencia, this.codPessoa, replaceOrder(this.chaveOrdem), this.filters);
        getAllClientes();
    }

    public void carregarClientesQrCode() throws Exception {
        ClientesDao clientesDao = new ClientesDao();
        StringBuilder str = new StringBuilder();

        for (Clientes itemCliente : clientesDao.ListaClientesQrCode(this.persistencia)) {
            if (itemCliente.getCodFil().toPlainString().replace(".0", "").equals(this.codfil)) {
                str.append(" <tr Codigo=\"").append(itemCliente.getCodigo()).append("\" CodPtoCli=\"").append(itemCliente.getCodPtoCli()).append("\" CodFil=\"").append(itemCliente.getCodFil()).append("\">");
            } else {
                str.append(" <tr class=\"objHidden\" Codigo=\"").append(itemCliente.getCodigo()).append("\" CodPtoCli=\"").append(itemCliente.getCodPtoCli()).append("\" CodFil=\"").append(itemCliente.getCodFil()).append("\">");
            }
            str.append("   <td><input type=\"checkbox\" id=\"chk_").append(itemCliente.getCodigo()).append("\" /></td>");
            str.append("   <td><label for=\"chk_").append(itemCliente.getCodigo()).append("\">").append(itemCliente.getNRed()).append("</label></td>");
            str.append(" </tr>");
        }

        if (str.toString().isEmpty()) {
            str.append("<label id=\"NaoHaClientesQrCode\">").append(Messages.getMessageS("NaoHaClietesQrCode")).append("</label>");
        }

        this.listaClientesQrCode = str.toString();
        PrimeFaces.current().executeScript("RemoverOcultos();");
    }

    private void replaceFilter(String valor) {
        try {
            switch (chavePesquisa) {
                case "CODIGO":
                    this.chaveOrdem = "CODIGO";
                    filters.replace(CODIGO, "%" + valor + "%");
                    return;
                case "NOME":
                    this.chaveOrdem = "NOME";
                    filters.replace(NOME, "%" + valor + "%");
                    return;
                case "NRED":
                    this.chaveOrdem = "NRED";
                    filters.replace(NRED, "%" + valor + "%");
                    return;
                case "AGENCIA":
                    this.chaveOrdem = "AGENCIA";
                    filters.replace(AGENCIA, "%" + valor + "%");
                    return;
                case "BAIRRO":
                    this.chaveOrdem = "BAIRRO";
                    filters.replace(BAIRRO, "%" + valor + "%");
                    return;
                case "REGIAO":
                    this.chaveOrdem = "REGIAO";
                    filters.replace(REGIAO, "%" + valor + "%");
                    return;
                case "CHAVE":
                    if (this.isInteger(valor)) {
                        this.chaveOrdem = "CHAVE";
                        filters.replace(CHAVE, valor);
                        return;
                    } else {
                        throw new Exception("DeveSerNumerico");
                    }
                case "ENDERECO":
                    this.chaveOrdem = "ENDERECO";
                    filters.replace(ENDERECO, "%" + valor + "%");
                    return;
                case "SEMLATLON":
                    this.filters.put(SEMLATLON, "0");
                    return;
                default:
                    throw new Exception("CampoNaoExiste");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public LazyDataModel<Clientes> getAllClientes() {
        if (clientes == null) {
            filters.replace(CODFIL, codfil);
            this.chaveOrdem = "NRED";
            clientes = new ClientesLazyList(persistencia, codPessoa, filters);
        } else {
            ((ClientesLazyList) clientes).setFilters(filters);
        }
        try {
            total = clientessatmobweb.Contagem(filters, codPessoa, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
        return clientes;
    }

    public void AtualizaTabela() {
        PrimeFaces.current().ajax().update("main:tabela");
    }

    public void AtualizaTabelaMenu() {
        PrimeFaces.current().ajax().update("exportarClientes:tabela");
    }

    public void LimparFiltros() {
        this.chaveOrdem = "NRED";
        clientes = new ClientesLazyList(persistencia, codPessoa, filters);
        mostrarFiliais = false;
        somenteAtivos = true;
        limparFiltros = false;
        filters.replace(CODFIL, codfil);
        filters.replace(NOME, "");
        filters.replace(NRED, "");
        filters.replace(CODIGO, "");
        filters.replace(BAIRRO, "");
        filters.replace(REGIAO, "");
        filters.replace(SITUACAO, "A");
        filters.replace(DT_ALTER, "");
        getAllClientes();
    }

    public void limparPesquisa() {
        this.chaveOrdem = "NRED";
        clientes = new ClientesLazyList(persistencia, codPessoa, filters);
        filters.replace(CODFIL, codfil);
        filters.replace(NOME, "");
        filters.replace(NRED, "");
        filters.replace(CODIGO, "");
        filters.replace(AGENCIA, "");
        filters.replace(CHAVE, "");
        filters.replace(ENDERECO, "");
        filters.replace(BAIRRO, "");
        filters.replace(REGIAO, "");
        try {
            filters.remove(SEMLATLON);
        } catch (Exception ex) {
        }
        filters.replace(SITUACAO, "A");
    }

    public void MostrarFiliais() {
        if (mostrarFiliais) {
            filters.replace(CODFIL, "");
        } else {
            filters.replace(CODFIL, codfil);
        }
        getAllClientes();
    }

    public void mostrarSomenteAtivos() {
        this.filters.replace(SITUACAO, this.somenteAtivos ? "A" : "");
        getAllClientes();
    }

    public void listarContainers() {
        try {
            Calendar c = Calendar.getInstance();
            c.setTime(Date.from(Instant.now()));
            c.set(Calendar.DAY_OF_MONTH, 1);

            this.dataContainer1 = c.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
            this.dataContainer2 = c.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            PrimeFaces.current().ajax().update("formContainers");
            PrimeFaces.current().executeScript("PF('dlgContainers').show();");
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void listarMovimentacaoContainers() {
        try {
            if (null == this.clienteSelecionado) {
                throw new Exception(getMessageS("SelecioneCliente"));
            } else {
                this.historicoMovimentacao = this.clientessatmobweb.listarMovimentacaoContainer(this.clienteSelecionado.getCodigo(),
                        this.clienteSelecionado.getCodFil().toPlainString(), this.dataContainer1, this.dataContainer2, this.persistencia);
                PrimeFaces.current().ajax().update("formContainers:tabelaMovimentacaoContainer");
                PrimeFaces.current().executeScript("PF('dlgMovimentacaoContainer').show();");
            }
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void preCadastroRamosAtiv() {
        try {
            this.ramoAtiv = new RamosAtiv();

            PrimeFaces.current().resetInputs("formCadastrar:addRamosAtiv");
            PrimeFaces.current().executeScript("PF('dlgAdicionarRamosAtiv').show();");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + "\r\n" + Logger.objeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrarRamosAtiv() {
        try {
            this.ramoAtiv.setDt_alter(getDataAtual("SQL"));
            this.ramoAtiv.setHr_Alter(getDataAtual("HORA"));
            this.ramoAtiv.setOperador(RecortaAteEspaço(this.operador, 0, 10));

            if (this.clientessatmobweb.inserirRamosAtiv(this.ramoAtiv, this.persistencia)) {
                this.ramosAtiv = this.clientessatmobweb.listarRamosAtiv(this.persistencia);
                PrimeFaces.current().executeScript("PF('dlgAdicionarRamosAtiv').hide();");
            } else {
                throw new Exception("ErroInserirVerifiqueiCodigo");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + "\r\n" + Logger.objeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preCadastroRegiao() {
        try {
            this.novaRegiao = new Regiao();
            this.novaRegiao.setCodFil(this.novoCliente.getCodFil().toBigInteger().toString());

            PrimeFaces.current().resetInputs("formCadastrar:addRegiao");
            PrimeFaces.current().executeScript("PF('dlgAdicionarRegiao').show();");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + "\r\n" + Logger.objeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrarRegiao() {
        try {
            this.novaRegiao.setDt_Alter(getDataAtual("SQL"));
            this.novaRegiao.setHr_Alter(getDataAtual("HORA"));
            this.novaRegiao.setOperador(RecortaAteEspaço(this.operador, 0, 10));

            if (this.clientessatmobweb.inserirRegiao(this.novaRegiao, this.persistencia)) {
                this.regioes = this.clientessatmobweb.listarRegioes(this.novoCliente.getCodFil().toString(), this.persistencia);
                PrimeFaces.current().executeScript("PF('dlgAdicionarRegiao').hide();");
            } else {
                throw new Exception("ErroInserirVerifiqueiCodigo");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + "\r\n" + Logger.objeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void atribuirChavesGoogle(String ingoogleApiMob, String ingoogleApiOper) {
        this.googleApiMob = ingoogleApiMob;
        this.googleApiOper = ingoogleApiOper;
    }

    public void preCadastroUsuario() {
        try {
            this.novoUsuario = new PessoaCliAut();
            this.novosUsuarios = new ArrayList<>();
            this.novosUsuarios.add(this.novoUsuario);

            PrimeFaces.current().resetInputs("formCadastrarUsuario");
            PrimeFaces.current().ajax().update("formCadastrarUsuario");
            PrimeFaces.current().executeScript("PF('dlgAdicionarUsuario').show();");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + "\r\n" + Logger.objeto2String(this.clientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrarChave() throws Exception {
        if (this.novoPessoaChave != null
                && this.novoPessoaChave.getCodigo() != null
                && this.novoPessoaChave.getCodigo() != BigDecimal.ZERO
                && this.novoPessoaChave.getPwweb() != null
                && !this.novoPessoaChave.getPwweb().equals("")) {
            ClientesDao clientesDao = new ClientesDao();
            clientesDao.criarChaveAcesso(this.clienteSelecionado.getCodigo(), this.novoPessoaChave.getCodigo().toPlainString().replace(".0", ""), this.clienteSelecionado.getCodFil().toPlainString().replace(".0", ""), FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia, this.satellite);
            this.chavesAcesso = clientesDao.buscarChaveAcesso(this.novoCliente.getCodigo(), this.novoCliente.getCodFil().toPlainString().replace(".0", ""), this.persistencia, this.satellite);

            Pessoa pessoa = new Pessoa();
            PessoaDao pessoaDao = new PessoaDao();
            pessoa.setCodigo(this.novoPessoaChave.getCodigo());
            pessoa.setPWWeb(this.novoPessoaChave.getPwweb());
            pessoa.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            pessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
            pessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
            pessoaDao.atualizaSenhaSatMob(pessoa, this.persistencia);

            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("DadosSalvosSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            PrimeFaces.current().executeScript("PF('dlgAdicionarChave').hide();");
        } else {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("InformePessoaSenha"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void dataAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            filters.replace(DT_ALTER, this.dataTela);
            getAllClientes();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            //this.filters.replace("dt_Alter = ?", this.dataTela);            
            filters.replace(DT_ALTER, this.dataTela);
            getAllClientes();            
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarData(SelectEvent data) {
        this.dataTela = (String) data.getObject();
        //this.filters.replace("Dt_Alter = ?", this.dataTela);
                         
        filters.replace(DT_ALTER, this.dataTela);
        getAllClientes();
    }

    /**
     * Tranforma Date em String
     *
     * @param date Data a ser formatada
     * @return String no formato yyyyMMdd
     */
    public String Date2String(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    /**
     * Procura o último dia do mês atual
     */
    public static void ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONDAY) + 1);
        int ano = calendar.get(Calendar.YEAR);

        try {
            Date ultimoDia = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void cadastrarUsuario() {
        try {
            // Verifica se é criação de novo usuário ou usuário existente
            if (!this.usuarioExistenteSelecionado && this.podeGravarUsuario) {
                // Criar novo usuário seguindo o fluxo especificado
                String nomeDigitado = "";
                if (this.novoUsuario != null && this.novoUsuario.getNome() != null) {
                    nomeDigitado = this.novoUsuario.getNome();
                }
                criarNovoUsuarioPortalClientes(nomeDigitado, this.emailUsuario);
            } else if (this.usuarioExistenteSelecionado && this.novoUsuario != null) {
                // Fluxo original para usuário existente
                this.novoUsuario.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.novoUsuario.setDt_Alter(DataAtual.getDataAtual("SQL"));
                this.novoUsuario.setHr_Alter(DataAtual.getDataAtual("HORA"));
                this.novoUsuario.setFlag_Excl("");
                if (this.flag == 1) {
                    Boolean add = true;
                    for (PessoaCliAut f : this.usuarios) {
                        if (f.getCodigo().toBigInteger().toString().equals(this.novoUsuario.getCodigo().toBigInteger().toString())) {
                            add = false;
                        }
                    }
                    if (add) {
                        this.usuarios.add(this.novoUsuario);
                    }
                } else if (this.flag == 2) {
                    this.clientessatmobweb.inserirUsuario(this.novoUsuario, this.persistencia);
                    this.usuarios = this.clientessatmobweb.listarUsuariosCadastrados(this.novoCliente.getCodigo(),
                            this.novoCliente.getCodFil().toString(), this.persistencia);
                }
            }

            // Limpar campos
            this.emailUsuario = null;
            this.usuarioExistenteSelecionado = false;
            this.podeGravarUsuario = false;
            this.novoUsuario = null;

            PrimeFaces.current().executeScript("PF('dlgAdicionarUsuario').hide();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Método principal para criar novo usuário portal de clientes
     * Executa todo o fluxo especificado nos 6 passos
     */
    private void criarNovoUsuarioPortalClientes(String nomeCompleto, String email) throws Exception {
        try {
            // Passo 1: Verificar se email já existe na base central
            String codPessoaWEB = verificarEmailBaseCentral(email);

            // Passo 2: Se não existir, inserir na base central
            if (codPessoaWEB == null || codPessoaWEB.isEmpty()) {
                codPessoaWEB = inserirPessoaBaseCentral(nomeCompleto, email);
            }

            // Passo 3: Inserir na base local com senha aleatória
            String senhaAleatoria = gerarSenhaAleatoria();
            String codPessoaLocal = inserirPessoaBaseLocal(nomeCompleto, email, codPessoaWEB, senhaAleatoria);

            // Passo 4: Inserir no SASPW Local
            inserirSASPWLocal(nomeCompleto, codPessoaLocal, codPessoaWEB);

            // Passo 5: Inserir no PessoaLogin (Base central)
            inserirPessoaLogin(codPessoaWEB, codPessoaLocal);

            // Passo 6: Criar chave de acesso
            String chaveAcesso = criarChaveAcesso(codPessoaLocal);

            // Enviar email de confirmação
            enviarEmailConfirmacao(email, nomeCompleto, chaveAcesso, senhaAleatoria);

            // Atualizar lista de usuários
            if (this.flag == 2) {
                this.usuarios = this.clientessatmobweb.listarUsuariosCadastrados(this.novoCliente.getCodigo(),
                        this.novoCliente.getCodFil().toString(), this.persistencia);
            }

            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO,
                "Usuário criado com sucesso! Um email de confirmação foi enviado para " + email, null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);

        } catch (Exception e) {
            throw new Exception("Erro ao criar usuário portal de clientes: " + e.getMessage());
        }
    }

    /**
     * Passo 1: Verificar se email já existe na base central (Satellite)
     */
    private String verificarEmailBaseCentral(String email) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            List<Pessoa> pessoas = pessoaDao.BuscaEmail(email, this.satellite);

            if (pessoas != null && !pessoas.isEmpty()) {
                return pessoas.get(0).getCodigo().toString();
            }
            return null;
        } catch (Exception e) {
            throw new Exception("Erro ao verificar email na base central: " + e.getMessage());
        }
    }

    /**
     * Passo 2: Inserir na base central (Satellite)
     */
    private String inserirPessoaBaseCentral(String nomeCompleto, String email) throws Exception {
        try {
            Pessoa pessoa = new Pessoa();
            pessoa.setNome(nomeCompleto);
            pessoa.setEmail(email);
            pessoa.setSituacao("W");
            pessoa.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            pessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
            pessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));

            PessoaDao pessoaDao = new PessoaDao();
            String codigo = pessoaDao.inserirPessoaPortalClientes(pessoa, this.satellite);

            return codigo;
        } catch (Exception e) {
            throw new Exception("Erro ao inserir pessoa na base central: " + e.getMessage());
        }
    }

    /**
     * Passo 3: Inserir na base local com senha aleatória
     */
    private String inserirPessoaBaseLocal(String nomeCompleto, String email, String codPessoaWEB, String senhaAleatoria) throws Exception {
        try {
            Pessoa pessoa = new Pessoa();
            pessoa.setNome(nomeCompleto);
            pessoa.setEmail(email);
            pessoa.setSituacao("W");
            pessoa.setCodPessoaWEB(new BigDecimal(codPessoaWEB));
            pessoa.setPWWeb(senhaAleatoria);
            pessoa.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            pessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
            pessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));

            PessoaDao pessoaDao = new PessoaDao();
            String codigo = pessoaDao.inserirPessoaPortalClientesLocal(pessoa, this.persistencia);

            return codigo;
        } catch (Exception e) {
            throw new Exception("Erro ao inserir pessoa na base local: " + e.getMessage());
        }
    }

    /**
     * Gerar senha aleatória de 6 dígitos
     */
    private String gerarSenhaAleatoria() {
        return String.valueOf((int) (Math.random() * 10))
                + String.valueOf((int) (Math.random() * 10))
                + String.valueOf((int) (Math.random() * 10))
                + String.valueOf((int) (Math.random() * 10))
                + String.valueOf((int) (Math.random() * 10))
                + String.valueOf((int) (Math.random() * 10));
    }

    /**
     * Passo 4: Inserir no SASPW Local
     */
    private void inserirSASPWLocal(String nomeCompleto, String codPessoaLocal, String codPessoaWEB) throws Exception {
        try {
            // Buscar dados da pessoa local para inserir no SASPW
            PessoaDao pessoaDao = new PessoaDao();
            Pessoa pessoa = pessoaDao.buscarPorCodigo(codPessoaLocal, this.persistencia);

            if (pessoa != null) {
                // Criar registro SASPW baseado na especificação
                SaspwDao saspwDao = new SaspwDao();
                Saspw saspw = new Saspw();
                saspw.setNome(codPessoaLocal);
                saspw.setNomeCompleto(nomeCompleto);
                saspw.setSituacao("A");
                saspw.setCodigo(codPessoaLocal);
                saspw.setCodPessoa(codPessoaLocal);
                saspw.setCodPessoaWeb(codPessoaWEB);
                saspw.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                saspw.setDt_Alter(DataAtual.getDataAtual("SQL"));
                saspw.setHr_Alter(DataAtual.getDataAtual("HORA"));

                saspwDao.inserirSasPW(saspw, this.persistencia);
            }
        } catch (Exception e) {
            throw new Exception("Erro ao inserir no SASPW Local: " + e.getMessage());
        }
    }

    /**
     * Passo 5: Inserir no PessoaLogin (Base central)
     */
    private void inserirPessoaLogin(String codPessoaWEB, String codPessoaLocal) throws Exception {
        try {
            // Buscar CodPessoa da base local no SASPW
            SaspwDao saspwDao = new SaspwDao();
            Saspw saspw = saspwDao.buscarPorCodPessoaWEB(codPessoaWEB, this.persistencia);

            if (saspw != null) {
                PessoaLogin pessoaLogin = new PessoaLogin();
                pessoaLogin.setCodigo(new BigDecimal(codPessoaWEB));
                pessoaLogin.setBancoDados(this.persistencia.getEmpresa()); // Nome do banco local
                pessoaLogin.setNivel("5");
                pessoaLogin.setCodPessoaBD(new BigDecimal(codPessoaLocal));
                pessoaLogin.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                pessoaLogin.setDt_alter(DataAtual.getDataAtual("SQL"));
                pessoaLogin.setHr_Alter(DataAtual.getDataAtual("HORA"));

                PessoaLoginDao pessoaLoginDao = new PessoaLoginDao();
                pessoaLoginDao.inserirPessoaLogin(pessoaLogin, this.satellite);
            }
        } catch (Exception e) {
            throw new Exception("Erro ao inserir no PessoaLogin: " + e.getMessage());
        }
    }

    /**
     * Passo 6: Criar chave de acesso na tabela GTVEAcesso
     */
    private String criarChaveAcesso(String codPessoaLocal) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            clientesDao.criarChaveAcesso(
                this.clienteSelecionado.getCodigo(),
                codPessoaLocal,
                this.clienteSelecionado.getCodFil().toPlainString().replace(".0", ""),
                FuncoesString.RecortaAteEspaço(this.operador, 0, 10),
                this.persistencia,
                this.satellite
            );

            // Buscar a chave criada para o usuário específico
            PessoaDao pessoaDao = new PessoaDao();
            Pessoa pessoa = new Pessoa();
            pessoa.setCodigo(new BigDecimal(codPessoaLocal));
            String chaveAcesso = pessoaDao.pesquisarChaveAcesso(pessoa, this.persistencia, this.satellite);

            return chaveAcesso;
        } catch (Exception e) {
            throw new Exception("Erro ao criar chave de acesso: " + e.getMessage());
        }
    }

    /**
     * Enviar email de confirmação com chave de acesso e senha temporária
     */
    private void enviarEmailConfirmacao(String email, String nomeCompleto, String chaveAcesso, String senhaTemporaria) throws Exception {
        try {
            // Criar link de confirmação
            String linkConfirmacao = "http://localhost:8080/SatMobWeb/confirmar-senha.xhtml?chave=" + chaveAcesso;

            // Criar mensagem HTML do email
            String mensagemHtml = criarMensagemEmailConfirmacao(nomeCompleto, chaveAcesso, senhaTemporaria, linkConfirmacao);

            // Criar registro de email para envio
            EmailsEnviar emailEnviar = new EmailsEnviar();
            emailEnviar.setSmtp("smtplw.com.br");
            emailEnviar.setDest_email(email);
            emailEnviar.setDest_nome(nomeCompleto);
            emailEnviar.setRemet_email("<EMAIL>");
            emailEnviar.setRemet_nome("SatMOB Portal");
            emailEnviar.setAssunto("Confirmação de Cadastro - Portal de Clientes");
            emailEnviar.setMensagem(mensagemHtml);
            emailEnviar.setAut_login("sasw");
            emailEnviar.setAut_senha("xNiadJEj9607");
            emailEnviar.setPorta(587);
            emailEnviar.setCodFil(this.clienteSelecionado.getCodFil().toPlainString());
            emailEnviar.setCodCli(this.clienteSelecionado.getCodigo());
            emailEnviar.setParametro(this.persistencia.getEmpresa());

            // Inserir email na fila de envio
            EmailsEnviarDao emailsEnviarDao = new EmailsEnviarDao();
            emailsEnviarDao.InserirEmail(emailEnviar, this.satellite);

        } catch (Exception e) {
            throw new Exception("Erro ao enviar email de confirmação: " + e.getMessage());
        }
    }

    /**
     * Criar mensagem HTML para email de confirmação
     */
    private String criarMensagemEmailConfirmacao(String nomeCompleto, String chaveAcesso, String senhaTemporaria, String linkConfirmacao) {
        StringBuilder html = new StringBuilder();
        html.append("<html><body>");
        html.append("<h2>Bem-vindo ao Portal de Clientes SatMOB!</h2>");
        html.append("<p>Olá ").append(nomeCompleto).append(",</p>");
        html.append("<p>Seu cadastro foi criado com sucesso no Portal de Clientes. Para ativar sua conta, você precisa confirmar seus dados e definir uma nova senha.</p>");
        html.append("<h3>Seus dados de acesso:</h3>");
        html.append("<p><strong>Chave de Acesso:</strong> ").append(chaveAcesso).append("</p>");
        html.append("<p><strong>Senha Temporária:</strong> ").append(senhaTemporaria).append("</p>");
        html.append("<h3>Próximos passos:</h3>");
        html.append("<p>1. Clique no link abaixo para acessar a página de confirmação:</p>");
        html.append("<p><a href=\"").append(linkConfirmacao).append("\" style=\"background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\">Confirmar Cadastro</a></p>");
        html.append("<p>2. Digite sua chave de acesso e senha temporária</p>");
        html.append("<p>3. Defina sua nova senha</p>");
        html.append("<p><strong>Importante:</strong> Por segurança, você só poderá usar este link uma vez. Após definir sua nova senha, use apenas a nova senha para acessar o sistema.</p>");
        html.append("<p>Se você não conseguir clicar no link, copie e cole o seguinte endereço no seu navegador:</p>");
        html.append("<p>").append(linkConfirmacao).append("</p>");
        html.append("<hr>");
        html.append("<p><small>Este é um email automático, não responda a esta mensagem.</small></p>");
        html.append("</body></html>");
        return html.toString();
    }

    public List<PessoaCliAut> buscarUsuarios(String query) {
        try {
            this.novosUsuarios = this.clientessatmobweb.buscarUsuarios(query, this.novoCliente.getCodigo(),
                    this.novoCliente.getCodFil().toString(), this.persistencia);

            // Se não encontrou usuários, criar um objeto temporário para permitir digitação livre
            if (this.novosUsuarios == null || this.novosUsuarios.isEmpty()) {
                this.novosUsuarios = new ArrayList<>();
                PessoaCliAut usuarioTemp = new PessoaCliAut();
                usuarioTemp.setNome(query);
                usuarioTemp.setCodigo("0"); // Código zero indica que é novo usuário
                this.novosUsuarios.add(usuarioTemp);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.novosUsuarios;
    }

    public List<PessoaCliAut> buscarUsuariosPessoa(String query) {
        try {
            this.novosUsuarios = this.clientessatmobweb.buscarUsuariosPessoa(query, this.novoCliente.getCodigo(),
                    this.novoCliente.getCodFil().toString(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.novosUsuarios;
    }

    public void onUsuarioSelect() {
        if (this.novoUsuario != null && this.novoUsuario.getCodigo() != null && !this.novoUsuario.getCodigo().equals(BigDecimal.ZERO)) {
            // Usuário existente selecionado
            this.usuarioExistenteSelecionado = true;
            this.emailUsuario = this.novoUsuario.getEmail();
        } else {
            // Novo usuário (código zero ou nulo)
            this.usuarioExistenteSelecionado = false;
            this.emailUsuario = null;
        }
        validarCamposUsuario();
    }

    public void onUsuarioQuery() {
        this.usuarioExistenteSelecionado = false;
        this.novoUsuario = null;
        validarCamposUsuario();
    }

    public void validarCamposUsuario() {
        if (this.usuarioExistenteSelecionado) {
            // Usuário existente selecionado - pode gravar se tiver usuário válido
            this.podeGravarUsuario = (this.novoUsuario != null && this.novoUsuario.getCodigo() != null
                    && !this.novoUsuario.getCodigo().equals(BigDecimal.ZERO));
        } else {
            // Novo usuário - precisa ter nome digitado no autocomplete e email válido
            String nomeDigitado = "";
            if (this.novoUsuario != null && this.novoUsuario.getNome() != null) {
                nomeDigitado = this.novoUsuario.getNome();
            }

            this.podeGravarUsuario = (nomeDigitado != null && !nomeDigitado.trim().isEmpty()
                    && this.emailUsuario != null && !this.emailUsuario.trim().isEmpty()
                    && this.emailUsuario.contains("@"));
        }
    }

    public void criarUsuario() {
        this.novoUsuario = new PessoaCliAut();
        this.emailUsuario = null;
        this.usuarioExistenteSelecionado = false;
        this.podeGravarUsuario = false;

        PrimeFaces.current().resetInputs("formCadastrarUsuario");
        PrimeFaces.current().ajax().update("formCadastrarUsuario");
        PrimeFaces.current().executeScript("PF('dlgAdicionarUsuario').show();");
    }

    public void apagarUsuario() {
        if (this.novoUsuario == null) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCliente"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            if (this.flag == 1) {
                for (PessoaCliAut p : this.usuarios) {
                    if (p.getCodigo().toBigInteger().toString().equals(this.novoUsuario.getCodigo().toBigInteger().toString())) {
                        this.usuarios.remove(p);
                        break;
                    }
                }
            } else if (this.flag == 2) {
                try {
                    this.clientessatmobweb.apagarCliente(this.novoUsuario, this.persistencia);
                    this.usuarios = this.clientessatmobweb.listarUsuariosCadastrados(this.novoCliente.getCodigo(),
                            this.novoCliente.getCodFil().toString(), this.persistencia);
                } catch (Exception e) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    log = this.getClass().getSimpleName() + "\r\n"
                            + Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                    this.logerro.Grava(log, caminho);
                }
            }
        }
    }

    public List<Clientes> getLista() {
        return lista;
    }

    public void setLista(List<Clientes> lista) {
        this.lista = lista;
    }

    public String getEscolha() {
        return escolha;
    }

    public void setEscolha(String escolha) {
        this.escolha = escolha;
    }

    public Clientes getClienteSelecionado() {
        return clienteSelecionado;
    }

    public void setClienteSelecionado(Clientes clienteSelecionado) {
        this.clienteSelecionado = clienteSelecionado;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Clientes getNovoCliente() {
        return novoCliente;
    }

    public void setNovoCliente(Clientes novoCliente) {
        this.novoCliente = novoCliente;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNred() {
        return nred;
    }

    public void setNred(String nred) {
        this.nred = nred;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getPesquisa() {
        return pesquisa;
    }

    public void setPesquisa(String pesquisa) {
        this.pesquisa = pesquisa;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getFilialDesc() {
        return filialDesc;
    }

    public void setFilialDesc(String filialDesc) {
        this.filialDesc = filialDesc;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public List<Municipios> getCidades() {
        return cidades;
    }

    public void setCidades(List<Municipios> cidades) {
        this.cidades = cidades;
    }

    public Boolean getMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(Boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public Boolean getLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(Boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public LazyDataModel<Clientes> getClientes() {
        return clientes;
    }

    public void setClientes(LazyDataModel<Clientes> clientes) {
        this.clientes = clientes;
    }

    public boolean iseFilial() {
        return eFilial;
    }

    public void seteFilial(boolean eFilial) {
        this.eFilial = eFilial;
    }

    public boolean iseNome() {
        return eNome;
    }

    public void seteNome(boolean eNome) {
        this.eNome = eNome;
    }

    public boolean iseEmail() {
        return eEmail;
    }

    public void seteEmail(boolean eEmail) {
        this.eEmail = eEmail;
    }

    public boolean iseInterfExt() {
        return eInterfExt;
    }

    public void seteInterfExt(boolean eInterfExt) {
        this.eInterfExt = eInterfExt;
    }

    public boolean iseNRed() {
        return eNRed;
    }

    public void seteNRed(boolean eNRed) {
        this.eNRed = eNRed;
    }

    public boolean iseRG() {
        return eRG;
    }

    public void seteRG(boolean eRG) {
        this.eRG = eRG;
    }

    public boolean iseCPF() {
        return eCPF;
    }

    public void seteCPF(boolean eCPF) {
        this.eCPF = eCPF;
    }

    public boolean iseCNPJ() {
        return eCNPJ;
    }

    public void seteCNPJ(boolean eCNPJ) {
        this.eCNPJ = eCNPJ;
    }

    public boolean iseInscMun() {
        return eInscMun;
    }

    public void seteInscMun(boolean eInscMun) {
        this.eInscMun = eInscMun;
    }

    public boolean iseInscEst() {
        return eInscEst;
    }

    public void seteInscEst(boolean eInscEst) {
        this.eInscEst = eInscEst;
    }

    public boolean iseLat() {
        return eLat;
    }

    public void seteLat(boolean eLat) {
        this.eLat = eLat;
    }

    public boolean iseLon() {
        return eLon;
    }

    public void seteLon(boolean eLon) {
        this.eLon = eLon;
    }

    public boolean iseCodigo() {
        return eCodigo;
    }

    public void seteCodigo(boolean eCodigo) {
        this.eCodigo = eCodigo;
    }

    public boolean iseFone1() {
        return eFone1;
    }

    public void seteFone1(boolean eFone1) {
        this.eFone1 = eFone1;
    }

    public boolean iseFone2() {
        return eFone2;
    }

    public void seteFone2(boolean eFone2) {
        this.eFone2 = eFone2;
    }

    public boolean iseEnde() {
        return eEnde;
    }

    public void seteEnde(boolean eEnde) {
        this.eEnde = eEnde;
    }

    public boolean iseCidade() {
        return eCidade;
    }

    public void seteCidade(boolean eCidade) {
        this.eCidade = eCidade;
    }

    public boolean iseUF() {
        return eUF;
    }

    public void seteUF(boolean eUF) {
        this.eUF = eUF;
    }

    public boolean iseCEP() {
        return eCEP;
    }

    public void seteCEP(boolean eCEP) {
        this.eCEP = eCEP;
    }

    public boolean iseOperador() {
        return eOperador;
    }

    public void seteOperador(boolean eOperador) {
        this.eOperador = eOperador;
    }

    public boolean iseDtAlter() {
        return eDtAlter;
    }

    public void seteDtAlter(boolean eDtAlter) {
        this.eDtAlter = eDtAlter;
    }

    public boolean iseHrAlter() {
        return eHrAlter;
    }

    public void seteHrAlter(boolean eHrAlter) {
        this.eHrAlter = eHrAlter;
    }

    public boolean iseBairro() {
        return eBairro;
    }

    public void seteBairro(boolean eBairro) {
        this.eBairro = eBairro;
    }

    public List<GuiasCliente> getGuias() {
        return guias;
    }

    public void setGuias(List<GuiasCliente> guias) {
        this.guias = guias;
    }

    public GuiasCliente getGuiaSelecionada() {
        return guiaSelecionada;
    }

    public void setGuiaSelecionada(GuiasCliente guiaSelecionada) {
        this.guiaSelecionada = guiaSelecionada;
    }

    public Clientes getOrigem() {
        return origem;
    }

    public void setOrigem(Clientes origem) {
        this.origem = origem;
    }

    public Clientes getDestino() {
        return destino;
    }

    public void setDestino(Clientes destino) {
        this.destino = destino;
    }

    public Clientes getFaturamento() {
        return faturamento;
    }

    public void setFaturamento(Clientes faturamento) {
        this.faturamento = faturamento;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        try {
            this.data1 = DataAtual.inverteData2(data1);
        } catch (Exception e) {
            this.data1 = data1;
        }
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        try {
            this.data2 = DataAtual.inverteData2(data2);
        } catch (Exception e) {
            this.data2 = data2;
        }
    }

    public String getExtenso() {
        return extenso;
    }

    public void setExtenso(String extenso) {
        this.extenso = extenso;
    }

    public String getCodBarras() {
        return codBarras;
    }

    public void setCodBarras(String codBarras) {
        this.codBarras = codBarras;
    }

    public Filiais getFilialGuia() {
        return filialGuia;
    }

    public void setFilialGuia(Filiais filialGuia) {
        this.filialGuia = filialGuia;
    }

    public List<CxFGuiasVol> getLacres() {
        return lacres;
    }

    public void setLacres(List<CxFGuiasVol> lacres) {
        this.lacres = lacres;
    }

    public List<EmailsEnviar> getEmails() {
        return emails;
    }

    public void setEmails(List<EmailsEnviar> emails) {
        this.emails = emails;
    }

    public EmailsEnviar getEmail() {
        return email;
    }

    public void setEmail(EmailsEnviar email) {
        this.email = email;
    }

    public List<ClientesEmail> getBuscaEmails() {
        return buscaEmails;
    }

    public void setBuscaEmails(List<ClientesEmail> buscaEmails) {
        this.buscaEmails = buscaEmails;
    }

    public ClientesEmail getEmailNovo() {
        return emailNovo;
    }

    public void setEmailNovo(ClientesEmail emailNovo) {
        this.emailNovo = emailNovo;
    }

    public boolean getObrigatorio() {
        Locale locale = LocaleController.getsCurrentLocale();
        return locale.getLanguage().toUpperCase().equals("PT");
    }

    public List<MovimentacaoContainer> getHistoricoMovimentacao() {
        return historicoMovimentacao;
    }

    public void setHistoricoMovimentacao(List<MovimentacaoContainer> historicoMovimentacao) {
        this.historicoMovimentacao = historicoMovimentacao;
    }

    public String getDataContainer1() {
        return dataContainer1;
    }

    public void setDataContainer1(String dataContainer1) {
        this.dataContainer1 = dataContainer1;
    }

    public String getDataContainer2() {
        return dataContainer2;
    }

    public void setDataContainer2(String dataContainer2) {
        this.dataContainer2 = dataContainer2;
    }

    public Boolean getSomenteAtivos() {
        return somenteAtivos;
    }

    public void setSomenteAtivos(Boolean somenteAtivos) {
        this.somenteAtivos = somenteAtivos;
    }

    public List<RamosAtiv> getRamosAtiv() {
        return ramosAtiv;
    }

    public void setRamosAtiv(List<RamosAtiv> ramosAtiv) {
        this.ramosAtiv = ramosAtiv;
    }

    public String getCpfcnpj() {
        return cpfcnpj;
    }

    public void setCpfcnpj(String cpfcnpj) {
        this.cpfcnpj = cpfcnpj;
    }

    public String getIerg() {
        return ierg;
    }

    public void setIerg(String ierg) {
        this.ierg = ierg;
    }

    public List<PessoaCliAut> getUsuarios() {
        return usuarios;
    }

    public void setUsuarios(List<PessoaCliAut> usuarios) {
        this.usuarios = usuarios;
    }

    public PessoaCliAut getNovoUsuario() {
        return novoUsuario;
    }

    public void setNovoUsuario(PessoaCliAut novoUsuario) {
        this.novoUsuario = novoUsuario;
    }

    public List<PessoaCliAut> getNovosUsuarios() {
        return novosUsuarios;
    }

    public void setNovosUsuarios(List<PessoaCliAut> novosUsuarios) {
        this.novosUsuarios = novosUsuarios;
    }

    public String getCentroMapa() {
        return centroMapa;
    }

    public void setCentroMapa(String centroMapa) {
        this.centroMapa = centroMapa;
    }

    public MapModel getMapaCliente() {
        return mapaCliente;
    }

    public void setMapaCliente(MapModel mapaCliente) {
        this.mapaCliente = mapaCliente;
    }

    public List<Regiao> getRegioes() {
        return regioes;
    }

    public void setRegioes(List<Regiao> regioes) {
        this.regioes = regioes;
    }

    public Regiao getRegiao() {
        return regiao;
    }

    public void setRegiao(Regiao regiao) {
        this.regiao = regiao;
    }

    public Regiao getNovaRegiao() {
        return novaRegiao;
    }

    public void setNovaRegiao(Regiao novaRegiao) {
        this.novaRegiao = novaRegiao;
    }

    public RamosAtiv getRamoAtiv() {
        return ramoAtiv;
    }

    public void setRamoAtiv(RamosAtiv ramoAtiv) {
        this.ramoAtiv = ramoAtiv;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public String getChaveOrdem() {
        return chaveOrdem;
    }

    public void setChaveOrdem(String chaveOrdem) {
        this.chaveOrdem = chaveOrdem;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

    public Clientes getNovoClienteCaptura() {
        return novoClienteCaptura;
    }

    public void setNovoClienteCaptura(Clientes novoClienteCaptura) {
        this.novoClienteCaptura = novoClienteCaptura;
    }

    public String getLargutaImagem() {
        return largutaImagem;
    }

    public void setLargutaImagem(String largutaImagem) {
        this.largutaImagem = largutaImagem;
    }

    public String getAlturaImagem() {
        return alturaImagem;
    }

    public void setAlturaImagem(String alturaImagem) {
        this.alturaImagem = alturaImagem;
    }

    public String getListaClientesQrCode() {
        return listaClientesQrCode;
    }

    public void setListaClientesQrCode(String listaClientesQrCode) {
        this.listaClientesQrCode = listaClientesQrCode;
    }

    public Fechaduras getFechadura() {
        return fechadura;
    }

    public void setFechadura(Fechaduras fechadura) {
        this.fechadura = fechadura;
    }

    public Fechaduras getFechaduraSelecionada() {
        return fechaduraSelecionada;
    }

    public void setFechaduraSelecionada(Fechaduras fechaduraSelecionada) {
        this.fechaduraSelecionada = fechaduraSelecionada;
    }

    public List<Fechaduras> getListaFechaduras() {
        return listaFechaduras;
    }

    public void setListaFechaduras(List<Fechaduras> listaFechaduras) {
        this.listaFechaduras = listaFechaduras;
    }

    public List<GTVeAcesso> getChavesAcesso() {
        return chavesAcesso;
    }

    public void setChavesAcesso(List<GTVeAcesso> chavesAcesso) {
        this.chavesAcesso = chavesAcesso;
    }

    public GTVeAcesso getGtveAcesso() {
        return gtveAcesso;
    }

    public void setGtveAcesso(GTVeAcesso gtveAcesso) {
        this.gtveAcesso = gtveAcesso;
    }

    public PessoaCliAut getNovoPessoaChave() {
        return novoPessoaChave;
    }

    public void setNovoPessoaChave(PessoaCliAut novoPessoaChave) {
        this.novoPessoaChave = novoPessoaChave;
    }

    public String getClientesImgHtml() {
        return clientesImgHtml;
    }

    public void setClientesImgHtml(String clientesImgHtml) {
        this.clientesImgHtml = clientesImgHtml;
    }

    public String getFotoCarregada() {
        return fotoCarregada;
    }

    public String getEmailUsuario() {
        return emailUsuario;
    }

    public void setEmailUsuario(String emailUsuario) {
        this.emailUsuario = emailUsuario;
    }

    public boolean isUsuarioExistenteSelecionado() {
        return usuarioExistenteSelecionado;
    }

    public void setUsuarioExistenteSelecionado(boolean usuarioExistenteSelecionado) {
        this.usuarioExistenteSelecionado = usuarioExistenteSelecionado;
    }

    public boolean isPodeGravarUsuario() {
        return podeGravarUsuario;
    }

    public void setPodeGravarUsuario(boolean podeGravarUsuario) {
        this.podeGravarUsuario = podeGravarUsuario;
    }

    public void setFotoCarregada(String fotoCarregada) {
        this.fotoCarregada = fotoCarregada;
    }
}
