/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Dados.Persistencia;
import SasBeansCompostas.NFiscalCliente;
import SasDaos.NFiscalDao;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class NFiscalClienteLazyList extends LazyDataModel<NFiscalCliente> {

    private static final long serialVersionUID = 1L;
    private List<NFiscalCliente> notasFiscais;
    private final NFiscalDao nfiscalDao;
    private final Persistencia persistencia;
    private final Persistencia central;
    private final BigDecimal codPessoa;
    private final String codFil;
    private final String competencia;

    public NFiscalClienteLazyList(Persistencia persistencia, Persistencia central, BigDecimal codPessoa, String codFil, String competencia) {
        this.persistencia = persistencia;
        this.central = central;
        this.codPessoa = codPessoa;
        this.codFil = codFil;
        this.competencia = competencia;
        this.nfiscalDao = new NFiscalDao();
    }

    @Override
    public List<NFiscalCliente> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            System.out.println("=== NFiscalClienteLazyList.load() CHAMADO ===");
            System.out.println("Página: " + pageSize + " First: " + first + " Field: " + sortField);
            System.out.println("Parâmetros: codPessoa=" + this.codPessoa + ", codFil=" + this.codFil + ", competencia=" + this.competencia);

            // Para LazyDataModel simples, carregamos todos os dados
            System.out.println("Chamando nfiscalDao.listarNotasFiscaisCliente...");
            this.notasFiscais = this.nfiscalDao.listarNotasFiscaisCliente(
                this.codPessoa.toString(),
                this.codFil,
                this.competencia,
                this.persistencia,
                this.central
            );

            System.out.println("Resultado da consulta: " + (this.notasFiscais != null ? this.notasFiscais.size() : "null") + " registros");

            if (this.notasFiscais == null) {
                this.notasFiscais = new ArrayList<>();
                System.out.println("Lista era null, criada lista vazia");
            }

            // Define o total de registros
            setRowCount(this.notasFiscais.size());
            System.out.println("RowCount definido: " + this.notasFiscais.size());

            // Define o tamanho da página
            setPageSize(pageSize);

            // Retorna a página solicitada
            int endIndex = Math.min(first + pageSize, this.notasFiscais.size());
            System.out.println("Calculando paginação: first=" + first + ", pageSize=" + pageSize + ", endIndex=" + endIndex + ", totalSize=" + this.notasFiscais.size());

            if (first < this.notasFiscais.size()) {
                List<NFiscalCliente> resultado = this.notasFiscais.subList(first, endIndex);
                System.out.println("Retornando sublist: " + resultado.size() + " registros (de " + first + " até " + endIndex + ")");

                // Debug: mostrar dados dos registros
                for (int i = 0; i < resultado.size(); i++) {
                    NFiscalCliente nf = resultado.get(i);
                    System.out.println("Registro " + i + ": " + nf.getNumero() + " - " + nf.getPraca());
                }

                return resultado;
            } else {
                System.out.println("Retornando lista completa: " + this.notasFiscais.size() + " registros");
                return this.notasFiscais;
            }

        } catch (Exception e) {
            System.out.println("ERRO no NFiscalClienteLazyList.load(): " + e.getMessage());
            e.printStackTrace();
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Erro ao carregar notas fiscais: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            return new ArrayList<>();
        }
    }

    @Override
    public Object getRowKey(NFiscalCliente nf) {
        try {
            return nf.getNumero() + "_" + nf.getPraca();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public NFiscalCliente getRowData(String rowKey) {
        try {
            if (this.notasFiscais != null) {
                for (NFiscalCliente nf : this.notasFiscais) {
                    String key = nf.getNumero() + "_" + nf.getPraca();
                    if (key.equals(rowKey)) {
                        return nf;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            System.out.println("Erro ao buscar nota fiscal: " + rowKey + " - " + e.getMessage());
            return null;
        }
    }
}
